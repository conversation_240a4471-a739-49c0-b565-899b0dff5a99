spring.application.name=fh-admin-system
#server.port=801
server.port=8081
#server.port=80



#数据源1
datasource.no1.driver-class-name: com.mysql.cj.jdbc.Driver
#本地测试源
datasource.no1.url=*****************************************************************************************************************
datasource.no1.username=root
datasource.no1.password=Passw0rd@ylzl
#公司测试源
#datasource.no1.url=*****************************************************************************************************************
#datasource.no1.username=Lurker
#datasource.no1.password=Lurker12580
#视频网源
#datasource.no1.url=*************************************************************************************************************
#datasource.no1.username=root
#datasource.no1.password=root123

#数据源2(默认没用，俩地址写一样即可)
datasource.no2.driver-class-name: oracle.jdbc.OracleDriver
#本地测试源
datasource.no2.url=************************************
#公安网测试源
#datasource.no2.url=*****************************************
#datasource.no2.url=***************************************
datasource.no2.username=MJJADAPP
datasource.no2.password=root123
#公司测试源
#datasource.no2.url=******************************************
#datasource.no2.username=MJJADAPP
#datasource.no2.password=MJJADAPP
#视频专网测试源
#datasource.no2.url=**************************************
#datasource.no2.url=**************************************
#datasource.no2.username=MJJADAPP
#datasource.no2.password=123456

#druid连接池
spring.datasource.type: com.alibaba.druid.pool.DruidDataSource
#最大活跃数
spring.datasource.maxActive: 50000
#初始化数量
spring.datasource.initialSize: 1000
#最大连接等待超时时间
spring.datasource.maxWait: 60000
#打开PSCache，并且指定每个连接PSCache的大小
spring.datasource.poolPreparedStatements: true
spring.datasource.maxPoolPreparedStatementPerConnectionSize: 20
#通过connectionProperties属性来打开mergeSql功能；慢SQL记录
#connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
spring.datasource.minIdle: 1
spring.datasource.timeBetweenEvictionRunsMillis: 60000
spring.datasource.minEvictableIdleTimeMillis: 300000
spring.datasource.validationQuery: select 1 from dual
spring.datasource.testWhileIdle: true
spring.datasource.testOnBorrow: false
spring.datasource.testOnReturn: false
#配置监控统计拦截的filters，去掉后监控界面sql将无法统计,'wall'用于防火墙
filters: stat, wall, log4j

#缓存配置文件位置
spring.cache.ehcache.cofnig=ehcache.xml

#配置这句话,控制台输出sql语句
#logging.level.org.fh.mapper=debug

#上传文件大小限制
spring.servlet.multipart.max-file-size=500MB
spring.servlet.multipart.max-request-size=500MB

spring.thymeleaf.cache=true
#activiti模型检测
spring.activiti.check-process-definitions=false
#mybatis过滤null
mybatis.configuration.call-setters-on-nulls: true
#jar包部署时去掉注释
web.upload-path=h:/
web.front-path=h:/
spring.resources.static-locations=file:${web.upload-path},file:${web.front-path}

