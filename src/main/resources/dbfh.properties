##用于备份、还原数据库、在线编辑 SQL

# 是否远程备份数据库（例如tomcat和数据库不在同一个服务器中，非linux） yes or no , DBSeverport远程服务器备份程序端口
remoteDB=no
DBSeverport=6666

####################################
# 数据库类型
dbtype=mysql
# 数据库地址
dbAddress=localhost
#端口
dbport=3306
# 数据库名称
databaseName=fhbootva6
# 数据库用户名
username=root
# 数据库密码
password=root
accountManagePwd = 888888

# mysqldump 路径 Linux
#dbpath = /usr/bin/
# 备份文件存放位置 Linux
#sqlFilePath =/mysql_backup/
# mysqldump 路径 Windows(mysql安装目录)
dbpath = E\://Soft//mysql5.6//MySQL Server 5.6//bin//
# 备份文件存放位置 Windows
sqlFilePath =L\:/mysql_backup/
####################################
interfaceUsername = txz2021
interfacePwd = txz*#2890114
#公安网六合一地址
intertfaceUrl = http://*************:8090/
#视频网六合一地址
#intertfaceUrl = http://************:8090/
intertfaceKey = a8ec5124646vf43393354b8ab40cf3a69e3be326
#提供出去的秘钥
outintertfaceKey = txz54321

####################################
# 数据库类型a
#dbtype=oracle
# 数据库地址
#dbAddress=*************
#端口
#dbport=1521
# 数据库用户名
#username=C##FHBOOT
# 数据库名称
#databaseName=orcl
# 数据库密码
#password = root
# 备份文件存放位置 Linux
#sqlFilePath =/mysql_backup/
# 备份文件存放位置 Windows
#sqlFilePath =E\://oracle_backup//
####################################


####################################
# 数据库类型
#dbtype=sqlserver
# 数据库地址
#dbAddress=*************
#端口
#dbport=1433
# 数据库的用户
#username = sa
# 数据库名称
#databaseName=fhboot
# 数据库密码
#password = root
# 备份文件存放位置  (只有Windows）确保此路径存在，否则备份失败
#sqlFilePath =E\:/sqlserverBackup/
####################################


