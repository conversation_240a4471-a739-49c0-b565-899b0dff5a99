<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.fhoa.MyleaveMapper">
	
	<!--表名 -->
	<sql id="tableName">
		OA_MYLEAVE
	</sql>
	
	<!-- 字段 -->
	<sql id="Field">
		USERNAME,	
		TYPE,	
		STARTTIME,	
		ENDTIME,	
		WHENLONG,	
		REASON,	
		MYLEAVE_ID
	</sql>
	
	<!-- 字段值 -->
	<sql id="FieldValue">
		#{USERNAME},	
		#{TYPE},	
		#{STARTTIME},	
		#{ENDTIME},	
		#{WHENLONG},	
		#{REASON},	
		#{MYLEAVE_ID}
	</sql>
	
	<!-- 新增-->
	<insert id="save" parameterType="pd">
		insert into 
	<include refid="tableName"></include>
		(
	<include refid="Field"></include>
		) values (
	<include refid="FieldValue"></include>
		)
	</insert>
	
	<!-- 删除-->
	<delete id="delete" parameterType="pd">
		delete from
		<include refid="tableName"></include>
		where 
			MYLEAVE_ID = #{MYLEAVE_ID}
	</delete>
	
	<!-- 修改 -->
	<update id="edit" parameterType="pd">
		update
		<include refid="tableName"></include>
		set 
			TYPE = #{TYPE},
			STARTTIME = #{STARTTIME},
			ENDTIME = #{ENDTIME},
			WHENLONG = #{WHENLONG},
			REASON = #{REASON},
		MYLEAVE_ID = MYLEAVE_ID
		where 
		MYLEAVE_ID = #{MYLEAVE_ID}
	</update>
	
	<!-- 通过ID获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select 
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include>
		where 
			MYLEAVE_ID = #{MYLEAVE_ID}
	</select>
	
	<!-- 列表 -->
	<select id="datalistPage" parameterType="page" resultType="pd">
		select
			l.USERNAME,	
			l.TYPE,	
			l.STARTTIME,	
			l.ENDTIME,	
			l.WHENLONG,	
			l.MYLEAVE_ID,
			l.REASON
		from 
		<include refid="tableName"></include> l
		where 1=1
		<if test="pd.keywords!= null and pd.keywords != ''"><!-- 关键词检索 -->
			and
				(
					l.REASON LIKE CONCAT(CONCAT('%', #{pd.keywords}),'%')
					 or 
					l.USERNAME LIKE CONCAT(CONCAT('%', #{pd.keywords}),'%') 
				)
		</if>
		<if test="pd.USERNAME!= null and pd.USERNAME != ''">
			and l.USERNAME = #{pd.USERNAME}
		</if>
		<if test="pd.TYPE != null and pd.TYPE != ''"><!-- 分类检索 -->
			and l.TYPE=#{pd.TYPE} 
		</if>
		order by l.ENDTIME desc
	</select>
	
	<!-- 批量删除 -->
	<delete id="deleteAll" parameterType="String">
		delete from
		<include refid="tableName"></include>
		where 
			MYLEAVE_ID in
		<foreach item="item" index="index" collection="array" open="(" separator="," close=")">
                 #{item}
		</foreach>
	</delete>
	
	<!-- fh313596790qq(青苔) -->
</mapper>