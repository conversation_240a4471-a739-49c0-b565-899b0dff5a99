<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.fhoa.WorklogMapper">
	
	<!--表名 -->
	<sql id="tableName">
		OA_WORKLOG
	</sql>
	
	<!--数据字典表名 -->
	<sql id="dicTableName">
		SYS_DICTIONARIES
	</sql>
	
	<!-- 字段 -->
	<sql id="Field">
		f.UNAME,	
		f.CONTENT,	
		f.CTIME,	
		f.BZ,	
		f.USERNAME,	
		f.DEPARTMENT_ID,	
		f.WORKLOG_ID
	</sql>
	
	<!-- 字段用于新增 -->
	<sql id="Field2">
		UNAME,	
		CONTENT,	
		CTIME,	
		BZ,	
		USERNAME,	
		DEPARTMENT_ID,	
		WORKLOG_ID
	</sql>
	
	<!-- 字段值 -->
	<sql id="FieldValue">
		#{UNAME},	
		#{CONTENT},	
		#{CTIME},	
		#{BZ},	
		#{USERNAME},	
		#{DEPARTMENT_ID},	
		#{WORKLOG_ID}
	</sql>
	
	<!-- 新增-->
	<insert id="save" parameterType="pd">
		insert into 
	<include refid="tableName"></include>
		(
	<include refid="Field2"></include>
		) values (
	<include refid="FieldValue"></include>
		)
	</insert>
	
	<!-- 删除-->
	<delete id="delete" parameterType="pd">
		delete from
		<include refid="tableName"></include>
		where 
			WORKLOG_ID = #{WORKLOG_ID}
	</delete>
	
	<!-- 修改 -->
	<update id="edit" parameterType="pd">
		update
		<include refid="tableName"></include>
		set 
			CONTENT = #{CONTENT},
			BZ = #{BZ},
			WORKLOG_ID = WORKLOG_ID
		where 
			WORKLOG_ID = #{WORKLOG_ID}
	</update>
	
	<!-- 通过ID获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select 
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include> f
		where 
			f.WORKLOG_ID = #{WORKLOG_ID}
	</select>
	
	<!-- 列表 -->
	<select id="datalistPage" parameterType="page" resultType="pd">
		select
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include> f
		where 1=1
		<if test="pd.KEYWORDS != null and pd.KEYWORDS != ''"><!-- 关键词检索 -->
			and
				(
					CONTENT LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
					or
					USERNAME LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
				)
		</if>
		<if test="pd.lastStart!=null and pd.lastStart!=''"><!-- 开始时间检索 -->
			and CTIME &gt;= #{pd.lastStart} 
		</if>
		<if test="pd.lastEnd!=null and pd.lastEnd!=''"><!-- 结束时间检索 -->
			and CTIME &lt;= #{pd.lastEnd} 
		</if>
		<if test="pd.item!=null and pd.item!=''"><!-- 根据部门ID过滤 -->
			and 
				(
					DEPARTMENT_ID in ${pd.item}
					or
					USERNAME = #{pd.ORUSERNAME}
				)
		</if>
		<if test="pd.USERNAME!=null and pd.USERNAME!=''"><!-- 根据用户名过滤 -->
			and USERNAME = #{pd.USERNAME}
		</if>
	</select>
	
	<!-- 列表(全部) -->
	<select id="listAll" parameterType="pd" resultType="pd">
		select
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include> f
	</select>
	
	<!-- fh313596790qq(青苔) -->
</mapper>