<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.fhim.QgroupMapper">
	
	<!--表名 -->
	<sql id="tableName">
		IM_QGROUP
	</sql>
	
	<!--数据字典表名 -->
	<sql id="dicTableName">
		SYS_DICTIONARIES
	</sql>
	
	<!-- 字段 -->
	<sql id="Field">
		f.NAME,	
		f.USERNAME,	
		f.PHOTO,	
		f.CTIME,	
		f.QGROUP_ID
	</sql>
	
	<!-- 字段用于新增 -->
	<sql id="Field2">
		NAME,	
		USERNAME,	
		PHOTO,	
		CTIME,	
		QGROUP_ID
	</sql>
	
	<!-- 字段值 -->
	<sql id="FieldValue">
		#{NAME},	
		#{USERNAME},	
		#{PHOTO},	
		#{CTIME},	
		#{QGROUP_ID}
	</sql>
	
	<!-- 新增-->
	<insert id="save" parameterType="pd">
		insert into 
	<include refid="tableName"></include>
		(
	<include refid="Field2"></include>
		) values (
	<include refid="FieldValue"></include>
		)
	</insert>
	
	<!-- 删除-->
	<delete id="delete" parameterType="pd">
		delete from
		<include refid="tableName"></include>
		where 
			QGROUP_ID = #{QGROUP_ID}
	</delete>
	
	<!-- 删除图片 -->
	<update id="delTp" parameterType="pd">
		update
		<include refid="tableName"></include>
		set 
			PHOTO = ''
		where 
			QGROUP_ID = #{QGROUP_ID}
	</update>
	
	<!-- 修改 -->
	<update id="edit" parameterType="pd">
		update
		<include refid="tableName"></include>
		set 
			NAME = #{NAME},
			PHOTO = #{PHOTO}
		where 
			QGROUP_ID = #{QGROUP_ID}
	</update>
	
	<!-- 通过ID获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select 
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include> f
		where 
			f.QGROUP_ID = #{QGROUP_ID}
	</select>
	
	<!-- 列表 -->
	<select id="datalistPage" parameterType="page" resultType="pd">
		select
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include> f
		where 1=1
		<if test="pd.keywords!= null and pd.keywords != ''"><!-- 关键词检索 -->
			and
				(
					NAME LIKE CONCAT(CONCAT('%', #{pd.keywords}),'%')
				)
		</if>
		<if test="pd.item!=null and pd.item!=''"><!-- 根据群组ID过滤 -->
			and QGROUP_ID in ${pd.item}
		</if>
	</select>
	
	<!-- 我在的全部群列表 -->
	<select id="mylistAll" parameterType="pd" resultType="pd">
		select
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include> f
		where 1=1
		<if test="item!=null and item!=''"><!-- 根据群组ID过滤 -->
			and QGROUP_ID in ${item}
		</if>
	</select>
	
	<!-- 群检索列表 -->
	<select id="searchListAll" parameterType="pd" resultType="pd">
		select
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include> f
		where 1=1
		<if test="keywords!= null and keywords != ''"><!-- 关键词检索 -->
			and
				(
					NAME LIKE CONCAT(CONCAT('%', #{keywords}),'%')
				)
		</if>
	</select>
	
	<!-- 列表(全部) -->
	<select id="listAll" parameterType="pd" resultType="pd">
		select
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include> f
	</select>
	
	<!-- fh313596790qq(青苔) -->
</mapper>