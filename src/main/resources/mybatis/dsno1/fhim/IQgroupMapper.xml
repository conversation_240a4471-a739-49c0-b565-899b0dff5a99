<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.fhim.IQgroupMapper">
	
	<!--表名 -->
	<sql id="tableName">
		IM_IQGROUP
	</sql>
	
	<!--表名(状态) -->
	<sql id="statetableName">
		IM_IMSTATE
	</sql>
	
	<!--表名(用户头像) -->
	<sql id="phototableName">
		SYS_USERPHOTO
	</sql>
	
	<!--表名(系统用户) -->
	<sql id="usertableName">
		SYS_USER
	</sql>
	
	<!--数据字典表名 -->
	<sql id="dicTableName">
		SYS_DICTIONARIES
	</sql>
	
	<!-- 字段 -->
	<sql id="Field">
		f.USERNA<PERSON>,	
		f.QGROUPS,	
		f.IQGROUP_ID
	</sql>
	
	<!-- 字段用于新增 -->
	<sql id="Field2">
		USERNAME,	
		QGROUPS,	
		IQGROUP_ID
	</sql>
	
	<!-- 字段值 -->
	<sql id="FieldValue">
		#{USERNAME},	
		#{QGROUPS},	
		#{IQGROUP_ID}
	</sql>
	
	<!-- 新增-->
	<insert id="save" parameterType="pd">
		insert into 
	<include refid="tableName"></include>
		(
	<include refid="Field2"></include>
		) values (
	<include refid="FieldValue"></include>
		)
	</insert>
	
	<!-- 删除-->
	<delete id="delete" parameterType="pd">
		delete from
		<include refid="tableName"></include>
		where 
			USERNAME = #{USERNAME}
	</delete>
	
	<!-- 修改 -->
	<update id="edit" parameterType="pd">
		update
		<include refid="tableName"></include>
		set 
			QGROUPS = #{QGROUPS}
		where 
			USERNAME = #{USERNAME}
	</update>
	
	<!-- 通过用户(作为ID)获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select 
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include> f
		where 
			f.USERNAME = #{USERNAME}
	</select>
	
	<!-- 判断我是否在某群 -->
	<select id="findByIdandQid" parameterType="pd" resultType="pd">
		select 
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include> f
		where 
			f.USERNAME = #{USERNAME}
		and
			QGROUPS LIKE CONCAT(CONCAT('%', #{QGROUP_ID}),'%')
	</select>
	
	<!-- 列表 -->
	<select id="datalistPage" parameterType="page" resultType="pd">
		select
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include> f
		where 1=1
		<if test="pd.keywords!= null and pd.keywords != ''"><!-- 关键词检索 -->
			and
				(
				<!--	根据需求自己加检索条件
					字段1 LIKE CONCAT(CONCAT('%', #{pd.keywords}),'%')
					 or 
					字段2 LIKE CONCAT(CONCAT('%', #{pd.keywords}),'%') 
				-->
				)
		</if>
	</select>
	
	<!-- 列表(全部群成员)带分页 -->
	<select id="memberslistPage" parameterType="page" resultType="pd">
		select
			f.USERNAME,
			u.NAME,
			p.PHOTO2
		from 
		<include refid="tableName"></include> f
		left join
			<include refid="usertableName"></include> u
		on
			f.USERNAME = u.USERNAME
		left join
			<include refid="phototableName"></include> p 
		on
			f.USERNAME = p.USERNAME
		where
			f.QGROUPS LIKE CONCAT(CONCAT('%', #{pd.QGROUP_ID}),'%')
		<if test="pd.keywords!= null and pd.keywords != ''"><!-- 关键词检索 -->
			and
				(
					f.USERNAME LIKE CONCAT(CONCAT('%', #{pd.keywords}),'%')
					 or 
					u.NAME LIKE CONCAT(CONCAT('%', #{pd.keywords}),'%') 
				)
		</if>
		and f.USERNAME != #{pd.USERNAME}
		order by u.NAME
	</select>
	
	<!-- 列表(全部群成员) -->
	<select id="listAll" parameterType="pd" resultType="pd">
		select
			f.USERNAME,
			f.QGROUPS,
			u.NAME,
			p.PHOTO2,
			s.ONLINE,
			s.AUTOGRAPH
		from 
		<include refid="tableName"></include> f
		left join
			<include refid="usertableName"></include> u
		on
			f.USERNAME = u.USERNAME
		left join
			<include refid="phototableName"></include> p 
		on
			f.USERNAME = p.USERNAME
		left join
			<include refid="statetableName"></include> s 
		on
			f.USERNAME = s.USERNAME
		where
			f.QGROUPS LIKE CONCAT(CONCAT('%', #{QGROUP_ID}),'%')
		order by u.NAME
	</select>
	
	<!-- fh313596790qq(青苔) -->
</mapper>