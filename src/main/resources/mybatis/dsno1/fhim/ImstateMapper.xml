<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.fhim.ImstateMapper">
	
	<!--表名 -->
	<sql id="tableName">
		IM_IMSTATE
	</sql>
	
	<!--数据字典表名 -->
	<sql id="dicTableName">
		SYS_DICTIONARIES
	</sql>
	
	<!-- 字段 -->
	<sql id="Field">
		f.USERNAME,	
		f.ONLINE,	
		f.AUTOGRAPH,
		f.SIGN,	
		f.IMSTATE_ID
	</sql>
	
	<!-- 字段用于新增 -->
	<sql id="Field2">
		USERNAME,	
		ONLINE,	
		AUTOGRAPH,
		SIGN,	
		IMSTATE_ID
	</sql>
	
	<!-- 字段值 -->
	<sql id="FieldValue">
		#{USERNAME},	
		#{ONLINE},	
		#{AUTOGRAPH},
		#{SIGN},
		#{IMSTATE_ID}
	</sql>
	
	<!-- 新增-->
	<insert id="save" parameterType="pd">
		insert into 
	<include refid="tableName"></include>
		(
	<include refid="Field2"></include>
		) values (
	<include refid="FieldValue"></include>
		)
	</insert>
	
	<!-- 修改在线状态 -->
	<update id="editOnline" parameterType="pd">
		update
		<include refid="tableName"></include>
		set 
			ONLINE = #{ONLINE}
		where 
			USERNAME = #{USERNAME}
	</update>
	
	<!-- 修改 个性签名-->
	<update id="editAuto" parameterType="pd">
		update
		<include refid="tableName"></include>
		set 
			AUTOGRAPH = #{AUTOGRAPH}
		where 
			USERNAME = #{USERNAME}
	</update>
	
	<!-- 修改皮肤 -->
	<update id="editSign" parameterType="pd">
		update
		<include refid="tableName"></include>
		set 
			SIGN = #{SIGN}
		where 
			USERNAME = #{USERNAME}
	</update>
	
	<!-- 通过ID获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select 
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include> f
		where 
			f.IMSTATE_ID = #{IMSTATE_ID}
	</select>
	
	<!-- 通过用户名获取数据 -->
	<select id="findByUsername" parameterType="pd" resultType="pd">
		select 
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include> f
		where 
			f.USERNAME = #{USERNAME}
	</select>
	
	<!-- 列表 -->
	<select id="datalistPage" parameterType="page" resultType="pd">
		select
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include> f
		where 1=1
		<if test="pd.keywords!= null and pd.keywords != ''"><!-- 关键词检索 -->
			and
				(
				<!--	根据需求自己加检索条件
					字段1 LIKE CONCAT(CONCAT('%', #{pd.keywords}),'%')
					 or 
					字段2 LIKE CONCAT(CONCAT('%', #{pd.keywords}),'%') 
				-->
				)
		</if>
	</select>
	
	<!-- 列表(全部) -->
	<select id="listAll" parameterType="pd" resultType="pd">
		select
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include> f
	</select>
	
	<!-- 删除-->
	<delete id="delete" parameterType="pd">
		delete from
		<include refid="tableName"></include>
		where 
			IMSTATE_ID = #{IMSTATE_ID}
	</delete>
	
	<!-- 批量删除 -->
	<delete id="deleteAll" parameterType="String">
		delete from
		<include refid="tableName"></include>
		where 
			IMSTATE_ID in
		<foreach item="item" index="index" collection="array" open="(" separator="," close=")">
                 #{item}
		</foreach>
	</delete>
	
	<!-- fh313596790qq(青苔) -->
</mapper>