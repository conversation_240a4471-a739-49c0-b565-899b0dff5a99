<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.fhdb.TimingBackUpMapper">
	
	<!--表名 -->
	<sql id="tableName">
		DB_TIMINGBACKUP
	</sql>
	
	<!-- 字段 -->
	<sql id="Field">
		JOBNAME,	
		CREATE_TIME,	
		TABLENAME,	
		STATUS,	
		FHTIME,	
		TIMEEXPLAIN,	
		BZ,	
		TIMINGBACKUP_ID
	</sql>
	
	<!-- 字段值 -->
	<sql id="FieldValue">
		#{JOBNAME},	
		#{CREATE_TIME},	
		#{TABLENAME},	
		#{STATUS},	
		#{FHTIME},	
		#{TIMEEXPLAIN},	
		#{BZ},	
		#{TIMINGBACKUP_ID}
	</sql>
	
	<!-- 新增-->
	<insert id="save" parameterType="pd">
		insert into 
	<include refid="tableName"></include>
		(
	<include refid="Field"></include>
		) values (
	<include refid="FieldValue"></include>
		)
	</insert>
	
	<!-- 删除-->
	<delete id="delete" parameterType="pd">
		delete from
		<include refid="tableName"></include>
		where 
			TIMINGBACKUP_ID = #{TIMINGBACKUP_ID}
	</delete>
	
	<!-- 修改 -->
	<update id="edit" parameterType="pd">
		update
		<include refid="tableName"></include>
		set 
			TABLENAME = #{TABLENAME},
			FHTIME = #{FHTIME},
			TIMEEXPLAIN = #{TIMEEXPLAIN},
			JOBNAME = #{JOBNAME},
			CREATE_TIME = #{CREATE_TIME},
			STATUS = #{STATUS},
			BZ = #{BZ},
			TIMINGBACKUP_ID = TIMINGBACKUP_ID
		where 
		TIMINGBACKUP_ID = #{TIMINGBACKUP_ID}
	</update>
	
	<!-- 通过ID获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select 
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include>
		where 
			TIMINGBACKUP_ID = #{TIMINGBACKUP_ID}
	</select>
	
	<!-- 列表 -->
	<select id="datalistPage" parameterType="page" resultType="pd">
		select
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include>
		where 1=1
		<if test="pd.keywords!= null and pd.keywords != ''"><!-- 关键词检索 -->
			and
				(
					TABLENAME LIKE CONCAT(CONCAT('%', #{pd.keywords}),'%')
					 or 
					TIMEEXPLAIN LIKE CONCAT(CONCAT('%', #{pd.keywords}),'%') 
					 or 
					BZ LIKE CONCAT(CONCAT('%', #{pd.keywords}),'%') 
				)
		</if>
		<if test="pd.lastStart!=null and pd.lastStart!=''"><!-- 开始时间检索 -->
			and CREATE_TIME &gt;= #{pd.lastStart} 
		</if>
		<if test="pd.lastEnd!=null and pd.lastEnd!=''"><!-- 结束时间检索 -->
			and CREATE_TIME &lt;= #{pd.lastEnd} 
		</if>
		<if test="pd.STATUS != null and pd.STATUS != ''"><!-- 角色检索 -->
			and STATUS=#{pd.STATUS} 
		</if>
	</select>
	
	<!-- 列表(全部) -->
	<select id="listAll" parameterType="pd" resultType="pd">
		select
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include>
	</select>
	
	<!-- 批量删除 -->
	<delete id="deleteAll" parameterType="String">
		delete from
		<include refid="tableName"></include>
		where 
			TIMINGBACKUP_ID in
		<foreach item="item" index="index" collection="array" open="(" separator="," close=")">
                 #{item}
		</foreach>
	</delete>
	
	<!-- 切换状态 -->
	<update id="changeStatus" parameterType="pd">
		update
		<include refid="tableName"></include>
		set 
			STATUS = #{STATUS}
		where 
		TIMINGBACKUP_ID = #{TIMINGBACKUP_ID}
	</update>
	
	<!-- fh313596790qq(青苔) -->
</mapper>