<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.tools.CreateCodeMapper">
	
	<!--表名 -->
	<sql id="tableName">
		SYS_CREATECODE
	</sql>
	
	<!-- 字段 -->
	<sql id="Field">
		PACKAGENAME,	
		OBJECTNAME,	
		TABLENAME,	
		FIELDLIST,	
		CREATETIME,	
		TITLE,	
		CREATECODE_ID,
		FHTYPE
	</sql>
	
	<!-- 字段值 -->
	<sql id="FieldValue">
		#{PACKAGENAME},	
		#{OBJECTNAME},	
		#{TABLENAME},	
		#{FIELDLIST},	
		#{CREATETIME},	
		#{TITLE},	
		#{CREATECODE_ID},
		#{FHTYPE}
	</sql>
	
	<!-- 新增-->
	<insert id="save" parameterType="pd">
		insert into
		<include refid="tableName"></include>
		(
		<include refid="Field"></include>	
		) values (
		<include refid="FieldValue"></include>	
		)
	</insert>
	
	<!-- 删除-->
	<delete id="delete" parameterType="pd">
		delete from
		<include refid="tableName"></include>
		where 
			CREATECODE_ID = #{CREATECODE_ID}
	</delete>
	
	<!-- 通过ID获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select 
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include>
		where 
			CREATECODE_ID = #{CREATECODE_ID}
	</select>
	
	<!-- 列表 -->
	<select id="datalistPage" parameterType="page" resultType="pd">
		select
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include>
		where
			1=1
		<if test="pd.KEYWORDS != null and pd.KEYWORDS != ''"><!-- 检索 -->
			and (
				TITLE LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
				or
				OBJECTNAME LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
				or
				TABLENAME LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
				)
		</if>
		order by CREATETIME desc
	</select>
	
	<!-- 列表(主表) -->
	<select id="listFa" resultType="pd">
		select
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include>
		where
			FHTYPE = 'fathertable'
	</select>
	
	<!-- 批量删除 -->
	<delete id="deleteAll" parameterType="String">
		delete from
	<include refid="tableName"></include>
		where 
			CREATECODE_ID in
		<foreach item="item" index="index" collection="array" open="(" separator="," close=")">
                 #{item}
		</foreach>
	</delete>
	
	<!-- fh313596790qq(青苔) -->
</mapper>