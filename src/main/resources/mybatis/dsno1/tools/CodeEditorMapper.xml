<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.tools.CodeEditorMapper">
	
	<!--表名 -->
	<sql id="tableName">
		SYS_CODEEDITOR
	</sql>
	
	<!-- 字段 -->
	<sql id="Field">
		TYPE,	
		FTLNMAME,	
		CTIME,	
		CODECONTENT,	
		CODEEDITOR_ID
	</sql>
	
	<!-- 字段值 -->
	<sql id="FieldValue">
		#{TYPE},	
		#{FTLNMAME},	
		#{CTIME},	
		#{CODECONTENT},	
		#{CODEEDITOR_ID}
	</sql>
	
	<!-- 新增-->
	<insert id="save" parameterType="pd">
		insert into 
	<include refid="tableName"></include>
		(
	<include refid="Field"></include>
		) values (
	<include refid="FieldValue"></include>
		)
	</insert>
	
	<!-- 删除-->
	<delete id="delete" parameterType="pd">
		delete from
		<include refid="tableName"></include>
		where 
			CODEEDITOR_ID = #{CODEEDITOR_ID}
	</delete>
	
	<!-- 修改 -->
	<update id="edit" parameterType="pd">
		update
		<include refid="tableName"></include>
		set 
			TYPE = #{TYPE},
			FTLNMAME = #{FTLNMAME},
			CODECONTENT = #{CODECONTENT},
			CODEEDITOR_ID = CODEEDITOR_ID
		where 
		CODEEDITOR_ID = #{CODEEDITOR_ID}
	</update>
	
	<!-- 通过ID获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select 
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include>
		where 
			CODEEDITOR_ID = #{CODEEDITOR_ID}
	</select>
	
	<!-- 列表 -->
	<select id="datalistPage" parameterType="page" resultType="pd">
		select
			TYPE,	
			FTLNMAME,	
			CTIME,	
			CODEEDITOR_ID
		from 
		<include refid="tableName"></include>
		where
			TYPE = #{pd.TYPE}
		and
			FTLNMAME = #{pd.FTLNMAME}
		order by CTIME desc
	</select>
	
	<!-- 列表(全部) -->
	<select id="listAll" parameterType="pd" resultType="pd">
		select
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include>
	</select>
	
	<!-- 批量删除 -->
	<delete id="deleteAll" parameterType="String">
		delete from
		<include refid="tableName"></include>
		where 
			CODEEDITOR_ID in
		<foreach item="item" index="index" collection="array" open="(" separator="," close=")">
                 #{item}
		</foreach>
	</delete>
	
	<!-- fh313596790qq(青苔) -->
</mapper>