<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.system.PhotoMapper">
	
	<!--表名 -->
	<sql id="tableName">
		SYS_USERPHOTO
	</sql>
	
	<!-- 字段 -->
	<sql id="Field">
		USERNAME,	
		PHOTO0,	
		PHOTO1,	
		PHOTO2,	
		PHOTO3,	
		USERPHOTO_ID
	</sql>
	
	<!-- 字段值 -->
	<sql id="FieldValue">
		#{USERNAME},	
		#{PHOTO0},	
		#{PHOTO1},	
		#{PHOTO2},	
		#{PHOTO3},	
		#{USERPHOTO_ID}
	</sql>
	
	<!-- 新增-->
	<insert id="save" parameterType="pd">
		insert into 
	<include refid="tableName"></include>
		(
	<include refid="Field"></include>
		) values (
	<include refid="FieldValue"></include>
		)
	</insert>
	
	<!-- 修改 -->
	<update id="edit" parameterType="pd">
		update
		<include refid="tableName"></include>
		set 
		PHOTO0 = #{PHOTO0},
		PHOTO1 = #{PHOTO1},
		PHOTO2 = #{PHOTO2},
		PHOTO3 = #{PHOTO3}
		where 
		USERNAME = #{USERNAME}
	</update>
	
	<!-- 通过ID获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select 
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include>
		where 
			USERNAME = #{USERNAME}
	</select>
	
	<!-- fh313596790qq(青苔) -->
</mapper>