<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.system.FHlogMapper">
	
	<!--表名 -->
	<sql id="tableName">
		SYS_FHLOG
	</sql>
	
	<!-- 字段 -->
	<sql id="Field">
		USERNAME,	
		CZTIME,	
		CONTENT,	
		FHLOG_ID
	</sql>
	
	<!-- 字段值 -->
	<sql id="FieldValue">
		#{USERNAME},	
		#{CZTIME},	
		#{CONTENT},	
		#{FHLOG_ID}
	</sql>
	
	<!-- 新增-->
	<insert id="save" parameterType="pd">
		insert into 
	<include refid="tableName"></include>
		(
	<include refid="Field"></include>
		) values (
	<include refid="FieldValue"></include>
		)
	</insert>
	
	<!-- 删除-->
	<delete id="delete" parameterType="pd">
		delete from
		<include refid="tableName"></include>
		where 
			FHLOG_ID = #{FHLOG_ID}
	</delete>
	
	<!-- 列表 -->
	<select id="datalistPage" parameterType="page" resultType="pd">
		select
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include>
		where 1=1
		<if test="pd.KEYWORDS!= null and pd.KEYWORDS != ''"><!-- 关键词检索 -->
			and
				(
					USERNAME LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
					or
					CONTENT LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
				)
		</if>
		<if test="pd.STRARTTIME!=null and pd.STRARTTIME!=''"><!-- 开始时间检索 -->
			and CZTIME &gt;= #{pd.STRARTTIME} 
		</if>
		<if test="pd.ENDTIME!=null and pd.ENDTIME!=''"><!-- 结束时间检索 -->
			and CZTIME &lt;= #{pd.ENDTIME} 
		</if>
		order by CZTIME desc
	</select>
	
	<!-- 批量删除 -->
	<delete id="deleteAll" parameterType="String">
		delete from
		<include refid="tableName"></include>
		where 
			FHLOG_ID in
		<foreach item="item" index="index" collection="array" open="(" separator="," close=")">
                 #{item}
		</foreach>
	</delete>
	
	<!-- fh313596790qq(青苔) -->
</mapper>