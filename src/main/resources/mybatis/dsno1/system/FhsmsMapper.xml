<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.system.FhsmsMapper">
	
	<!--表名 -->
	<sql id="tableName">
		SYS_FHSMS
	</sql>
	
	<!-- 字段 -->
	<sql id="Field">
		CONTENT,	
		TYPE,	
		TO_USERNAME,	
		FROM_USERNAME,	
		SEND_TIME,	
		STATUS,	
		FHSMS_ID,
		SANME_ID
	</sql>
	
	<!-- 字段值 -->
	<sql id="FieldValue">
		#{CONTENT},	
		#{TYPE},	
		#{TO_USERNAME},	
		#{FROM_USERNAME},	
		#{SEND_TIME},	
		#{STATUS},	
		#{FHSMS_ID},
		#{SANME_ID}
	</sql>
	
	<!-- 新增-->
	<insert id="save" parameterType="pd">
		insert into 
	<include refid="tableName"></include>
		(
	<include refid="Field"></include>
		) values (
	<include refid="FieldValue"></include>
		)
	</insert>
	
	<!-- 删除-->
	<delete id="delete" parameterType="pd">
		delete from
		<include refid="tableName"></include>
		where 
			FHSMS_ID = #{FHSMS_ID}
	</delete>
	
	<!-- 修改 状态-->
	<update id="edit" parameterType="pd">
		update
		<include refid="tableName"></include>
		set 
			STATUS = '1'
		where 
		SANME_ID = #{SANME_ID}
	</update>
	
	<!-- 通过ID获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select 
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include>
		where 
			FHSMS_ID = #{FHSMS_ID}
	</select>
	
	<!-- 列表 -->
	<select id="datalistPage" parameterType="page" resultType="pd">
		select
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include>
		where
			FROM_USERNAME = #{pd.FROM_USERNAME}
		and
			TYPE = #{pd.TYPE}
		<if test="pd.KEYWORDS != null and pd.KEYWORDS != ''"><!-- 关键词检索 -->
			and
				(
					FROM_USERNAME LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
					 or 
					TO_USERNAME LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
					 or 
					CONTENT LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%') 
				)
		</if>
		<if test="pd.STRARTTIME!=null and pd.STRARTTIME!=''"><!-- 开始时间检索 -->
			and SEND_TIME &gt;= #{pd.STRARTTIME} 
		</if>
		<if test="pd.ENDTIME!=null and pd.ENDTIME!=''"><!-- 结束时间检索 -->
			and SEND_TIME &lt;= #{pd.ENDTIME} 
		</if>
		<if test="pd.STATUS != null and pd.STATUS != ''"><!-- 状态检索 -->
			and STATUS = #{pd.STATUS} 
		</if>
		order by STATUS desc
	</select>
	
	<!-- 列表(全部) -->
	<select id="listAll" parameterType="pd" resultType="pd">
		select
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include>
	</select>
	
	<!-- 获取未读总数 -->
	<select id="findFhsmsCount" parameterType="String" resultType="pd">
		select 
			count(FHSMS_ID) fhsmsCount
		from 
		<include refid="tableName"></include>
		where 
			STATUS = '2'
		and
			TYPE = '1'
		and
			FROM_USERNAME = #{USERNAME} 
	</select>
	
	<!-- 批量删除 -->
	<delete id="deleteAll" parameterType="String">
		delete from
		<include refid="tableName"></include>
		where 
			FHSMS_ID in
		<foreach item="item" index="index" collection="array" open="(" separator="," close=")">
                 #{item}
		</foreach>
	</delete>
	
	<!-- fh313596790qq(青苔) -->
</mapper>