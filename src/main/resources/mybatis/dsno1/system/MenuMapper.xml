<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.system.MenuMapper">
	
	<resultMap type="Menu" id="menuResultMap">
		<id column="MENU_ID" property="MENU_ID"/>
		<result column="MENU_NAME" property="MENU_NAME"/>
		<result column="MENU_URL" property="MENU_URL"/>
		<result column="PARENT_ID" property="PARENT_ID"/>
		<result column="MENU_ORDER" property="MENU_ORDER"/>
		<result column="MENU_ICON" property="MENU_ICON"/>
		<result column="MENU_TYPE" property="MENU_TYPE"/>
		<result column="SHIRO_KEY" property="SHIRO_KEY"/>
	</resultMap>
	
	<!-- ztree -->
	<resultMap type="Menu" id="menuZtreeResultMap">
		<id column="MENU_ID" property="MENU_ID"/>
		<result column="MENU_NAME" property="MENU_NAME"/>
		<result column="MENU_URL" property="MENU_URL"/>
		<result column="PARENT_ID" property="PARENT_ID"/>
		<result column="MENU_ORDER" property="MENU_ORDER"/>
		<result column="MENU_ICON" property="MENU_ICON"/>
		<result column="MENU_TYPE" property="MENU_TYPE"/>
		<result column="SHIRO_KEY" property="SHIRO_KEY"/>
		<result column="target" property="target"></result>
	</resultMap>
	
	<!--表名 -->
	<sql id="tableName">
		SYS_MENU
	</sql>
	
	<!-- 字段 -->
	<sql id="Field">
		MENU_ID,
 		MENU_NAME,
 		MENU_URL,
 		PARENT_ID,
 		MENU_ICON,
 		SHIRO_KEY,
 		MENU_ORDER,
 		MENU_STATE,
 		MENU_TYPE
	</sql>
	
	<!-- 字段值 -->
	<sql id="FieldValue">
		#{MENU_ID},
 		#{MENU_NAME},
 		#{MENU_URL},
 		#{PARENT_ID},
 		#{MENU_ICON},
 		#{SHIRO_KEY},
 		#{MENU_ORDER},
 		#{MENU_STATE},
 		#{MENU_TYPE}
	</sql>
	
	<!--新增 -->
	<insert id="addMenu" parameterType="menu">
		insert into 
			<include refid="tableName"></include> 
		(
			<include refid="Field"></include>
		) values (
			<include refid="FieldValue"></include>
		)
	</insert>
	
	<!--编辑 -->
	<update id="edit" parameterType="menu">
		update 
		<include refid="tableName"></include> 
		set 
			MENU_NAME =#{MENU_NAME},
			MENU_URL  =#{MENU_URL} ,
			MENU_ORDER=#{MENU_ORDER},
			MENU_STATE=#{MENU_STATE},
			SHIRO_KEY=#{SHIRO_KEY},
			MENU_TYPE =#{MENU_TYPE}
		where 
			MENU_ID=#{MENU_ID}
	</update>
	
	<!--通过菜单ID获取数据 -->
	<select id="getMenuById" parameterType="pd" resultType="pd">
		select  
			<include refid="Field"></include>
		 from 
			<include refid="tableName"></include>
		 where MENU_ID=#{MENU_ID}
	</select>
	
	<!--取最大ID-->
	<select id="findMaxId" parameterType="pd" resultType="pd">
		select MAX(MENU_ID) MID from 
		<include refid="tableName"></include>
	</select>
	
	<!--通过ID获取其子一级菜单 -->
	<select id="listSubMenuByParentId" parameterType="String" resultMap="menuResultMap">
		select  
			<include refid="Field"></include>
		from 
			<include refid="tableName"></include>
		where PARENT_ID = #{parentId} order by (MENU_ORDER+0) 
	</select>

	<!--删除菜单-->
	<delete id="deleteMenuById" parameterType="String">
		delete from 
		<include refid="tableName"></include> 
		where MENU_ID=#{MENU_ID} 
	</delete>
	
	<!--保存菜单图标  -->
	<update id="editicon" parameterType="pd">
		update 
		<include refid="tableName"></include> 
		set 
			MENU_ICON=#{MENU_ICON}
		where
			MENU_ID=#{MENU_ID}
	</update>

<!--  fh 313 596790qq(青 苔) -->
</mapper>