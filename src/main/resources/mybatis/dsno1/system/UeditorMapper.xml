<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.system.UeditorMapper">
	
	<!--表名 -->
	<sql id="tableName">
		SYS_UEDITOR
	</sql>

	<!-- 字段用于新增 -->
	<sql id="Field2">
		USER_ID,	
		USERNAME,	
		CONTENT,
		CONTENT2,	
		TYPE,	
		UEDITOR_ID
	</sql>
	
	<!-- 字段值 -->
	<sql id="FieldValue">
		#{USER_ID},	
		#{USERNAME},	
		#{CONTENT},
		#{CONTENT2},	
		#{TYPE},	
		#{UEDITOR_ID}
	</sql>
	
	<!-- 通过ID获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select 
		<include refid="Field2"></include>
		from 
		<include refid="tableName"></include>
		where 
			USERNAME = #{USERNAME}
		and
			TYPE = #{TYPE}
	</select>
	
	<!-- SaveOrUpdate -->
	<insert id="edit" parameterType="pd">
	  <selectKey keyProperty="count" resultType="int" order="BEFORE">
	    select count(*) from <include refid="tableName"></include> where USERNAME = #{USERNAME} and TYPE = #{TYPE}
	  </selectKey>
	  <if test="count > 0">
	    update
		<include refid="tableName"></include>
		set 
			CONTENT = #{CONTENT},
			CONTENT2 = #{CONTENT2}
		where 
			USERNAME = #{USERNAME}
		and
			TYPE = #{TYPE}
	  </if>
	  <if test="count==0">
	    insert into 
		<include refid="tableName"></include>
			(
		<include refid="Field2"></include>
			) values (
		<include refid="FieldValue"></include>
			)
	  </if>
	</insert>
	
	<!-- 删除-->
	<delete id="delete" parameterType="pd">
		delete from
		<include refid="tableName"></include>
		where 
			USER_ID = #{USER_ID}
	</delete>
	
	<!-- 批量删除 -->
	<delete id="deleteAll" parameterType="String">
		delete from
		<include refid="tableName"></include>
		where 
			USER_ID in
		<foreach item="item" index="index" collection="array" open="(" separator="," close=")">
                 #{item}
		</foreach>
	</delete>
	
	<!-- fh313596790qq(青苔) -->
</mapper>