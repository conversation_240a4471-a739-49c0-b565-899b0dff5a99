<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.system.DictionariesMapper">
	
	<resultMap type="Dictionaries" id="dictResultMap">
		<id column="DICTIONARIES_ID" property="DICTIONARIES_ID"/>
		<result column="NAME" property="NAME"/>
		<result column="PARENT_ID" property="PARENT_ID"/>
	</resultMap>
	
	<!--表名 -->
	<sql id="tableName">
		SYS_DICTIONARIES
	</sql>
	
	<!-- 字段 -->
	<sql id="Field">
		NAME,	
		NAME_EN,	
		BIANMA,	
		ORDER_BY,	
		PARENT_ID,	
		BZ,	
		TBSNAME,
		TBFIELD,
		YNDEL,
		DICTIONARIES_ID
	</sql>
	
	<!-- 字段值 -->
	<sql id="FieldValue">
		#{NAME},	
		#{NAME_EN},	
		#{BIANMA},	
		#{ORDER_BY},	
		#{PARENT_ID},	
		#{BZ},	
		#{TBSNAME},
		#{TBFIELD},
		#{YNDEL},
		#{DICTIONARIES_ID}
	</sql>
	
	<!-- 新增-->
	<insert id="save" parameterType="pd">
		insert into 
		<include refid="tableName"></include>
		(
		<include refid="Field"></include>
		) values (
		<include refid="FieldValue"></include>
		)
	</insert>
	
	<!-- 修改 -->
	<update id="edit" parameterType="pd">
		update
		<include refid="tableName"></include>
		set 
			NAME = #{NAME},
			NAME_EN = #{NAME_EN},
			ORDER_BY = #{ORDER_BY},
			BZ = #{BZ},
			TBSNAME = #{TBSNAME},
			TBFIELD = #{TBFIELD}
		where 
			DICTIONARIES_ID = #{DICTIONARIES_ID}
	</update>
	
	<!-- 通过ID获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select 
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include>
		where 
			DICTIONARIES_ID = #{DICTIONARIES_ID}
	</select>
	
	<!-- 通过编码获取数据 -->
	<select id="findByBianma" parameterType="pd" resultType="pd">
		select 
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include>
		where 
			BIANMA = #{BIANMA}
	</select>
	
	<!-- 通过ID获取其子级列表 -->
	<select id="listSubDictByParentId" parameterType="String" resultMap="dictResultMap">
		select 
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include>
		where 
			PARENT_ID = #{parentId} order by ORDER_BY 
	</select>
	
	<!-- 列表 -->
	<select id="datalistPage" parameterType="page" resultType="pd">
		select
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include>
		where
			1=1
		<if test="pd.DICTIONARIES_ID!= null and pd.DICTIONARIES_ID != ''"><!-- 检索 -->
		and PARENT_ID = #{pd.DICTIONARIES_ID}
		</if>
		<if test="pd.KEYWORDS != null and pd.KEYWORDS != ''"><!-- 关键词检索 -->
			and
				(
				 NAME LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
				 or 
				 NAME_EN LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%') 
				 or
				 BIANMA LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
				)
		</if>
		order by ORDER_BY
	</select>
	
	<!-- 排查表检查是否被占用 -->
	<select id="findFromTbs" parameterType="pd" resultType="pd">
		select
			count(*) zs
		from 
			${thisTable}
		where
			${TBFIELD} = #{BIANMA}
	</select>
	
	<!-- 删除-->
	<delete id="delete" parameterType="pd">
		delete from
		<include refid="tableName"></include>
		where 
			DICTIONARIES_ID = #{DICTIONARIES_ID}
	</delete>
	
	<!-- fh3135-96790qq(青苔) -->
</mapper>