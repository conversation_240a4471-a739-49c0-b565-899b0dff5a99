<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.system.FhButtonMapper">
	
	<!--表名 -->
	<sql id="tableName">
		SYS_FHBUTTON
	</sql>
	
	<!-- 字段 -->
	<sql id="Field">
		NAME,	
		SHIRO_KEY,	
		BZ,	
		FHBUTTON_ID
	</sql>
	
	<!-- 字段值 -->
	<sql id="FieldValue">
		#{NAME},	
		#{SHIRO_KEY},	
		#{BZ},	
		#{FHBUTTON_ID}
	</sql>
	
	<!-- 列表(全部) -->
	<select id="listAll" parameterType="pd" resultType="pd">
		select
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include>
	</select>
	
	<!-- 列表 -->
	<select id="datalistPage" parameterType="page" resultType="pd">
		select
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include>
		where 1=1
		<if test="pd.KEYWORDS!= null and pd.KEYWORDS != ''"><!-- 关键词检索 -->
		and
			(
				NAME LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
				 or 
				SHIRO_KEY LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%') 
			)
				
		</if>
	</select>
	
	<!-- 新增-->
	<insert id="save" parameterType="pd">
		insert into 
	<include refid="tableName"></include>
		(
	<include refid="Field"></include>
		) values (
	<include refid="FieldValue"></include>
		)
	</insert>
	
	<!-- 删除-->
	<delete id="delete" parameterType="pd">
		delete from
		<include refid="tableName"></include>
		where 
			FHBUTTON_ID = #{FHBUTTON_ID}
	</delete>
	
	<!-- 修改 -->
	<update id="edit" parameterType="pd">
		update
		<include refid="tableName"></include>
		set 
			NAME = #{NAME},
			SHIRO_KEY = #{SHIRO_KEY},
			BZ = #{BZ}
		where 
		FHBUTTON_ID = #{FHBUTTON_ID}
	</update>
	
	<!-- 通过ID获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select 
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include>
		where 
			FHBUTTON_ID = #{FHBUTTON_ID}
	</select>

	<!-- 批量删除 -->
	<delete id="deleteAll" parameterType="String">
		delete from
		<include refid="tableName"></include>
		where 
			FHBUTTON_ID in
		<foreach item="item" index="index" collection="array" open="(" separator="," close=")">
                 #{item}
		</foreach>
	</delete>

<!-- f h 3 13596 790qq(青 苔) -->
</mapper>