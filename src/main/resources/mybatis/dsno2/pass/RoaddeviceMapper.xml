<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno2.pass.RoaddeviceMapper">

<!--    <resultMap id="BaseResultMap" type="Map">-->
<!--        <result column="ROADPARTID" property="ROADPARTID" jdbcType="string" />-->
<!--        <result column="SBBH" property="SBBH" jdbcType="INTEGER" />-->
<!--    </resultMap>-->
    <select id="getDepNumMap" resultType="java.util.HashMap">
        select ROADPARTID,wm_concat(SBBH) AS SBBH from PASLICENSE_ROADDEVICE  group by ROADPARTID
    </select>
<!--    <select id="GetDev" resultType="Map">-->
<!--        select ROADPARTID,count(1)-->
<!--        from PASLICENSE_ROADDEVICE group by ROADPARTID-->
<!--    </select>-->
   </mapper>
