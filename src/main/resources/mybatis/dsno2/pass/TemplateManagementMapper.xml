<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno2.pass.TemplateManagementMapper">
    <!--表名 -->
    <sql id="tableName">
         "PASLICENSE_MOULD"
    </sql>

    <!--数据字典表名 -->
    <sql id="dicTableName">
         "SYS_DICTIONARIES"
    </sql>

    <!-- 字段 -->
    <sql id="Field">
         f."ID",
         f."MBMC",
         f."PX",
         f."TJSJ",
         f."LX",
         f."WEB",
         f."SD1",
         f."SD2",
         f."SD3",
         f."SD4"
    </sql>

    <!-- 字段用于新增 -->
    <sql id="Field2">
          "ID",
          "MBMC",
          "PX",
          "TJSJ",
          "LX",
          "WEB"
    </sql>

    <!--用于分页-->
    <sql id="Field3">
    t2."ID",
    t2."MBMC",
    t2."PX",
    t2."TJSJ",
    t2."LX",
    t2."WEB",
    t2."SD1",
    t2."SD2",
    t2."SD3",
    t2."SD4"
    </sql>

    <!-- 字段值 -->
    <sql id="FieldValue">
         #{ID},
         #{MBMC},
         #{PX},
         #{TJSJ},
         #{LX},
         #{WEB}
    </sql>

    <!-- 新增-->
    <insert id="save" parameterType="pd">
        insert into
        <include refid="tableName">
        </include>
        (
        <include refid="Field2">
        </include>
        ) values (
        <include refid="FieldValue">
        </include>
        )
    </insert>

    <!-- 删除-->
    <delete id="delete" parameterType="pd">
        delete from
        <include refid="tableName">
        </include>
        where
        "ID" = #{ID}
    </delete>
    <!-- 模版道路列表-->
    <select id="listPagelistRoad" parameterType="page" resultType="pd">
        SELECT
        t1.*,
        t.LDMC,
        t.DJ
        FROM
        PASLICENSE_MOULDINFO t1
        LEFT JOIN PASLICENSE_ROADPART t ON t1.ROADPARTID = t.ID
        WHERE
        t1.MBID = #{pd.MBID}
        <if test="pd.KEYWORDS != null and pd.KEYWORDS != ''"><!-- 关键词检索 -->
            and
            (
            (LDMC LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%'))
            or
            (PY LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%'))
            )
        </if>
        <if test="pd.DJ != null and pd.DJ != ''">
            <!-- 关键词检索 道路等级 -->
            and
            t.DJ = #{pd.DJ}
        </if>
        order by t.DJ
    </select>
    <select id="getRoadCount" parameterType="pd" resultType="int">
        SELECT
        COUNT(*)
        FROM
        PASLICENSE_MOULDINFO t1
        LEFT JOIN PASLICENSE_ROADPART t ON t1.ROADPARTID = t.ID
        WHERE
        t1.MBID = #{MBID}
        <if test="LDMC != null and LDMC != ''">
            <!-- 关键词检索 道路名称 -->
            and
            (
            t.LDMC LIKE CONCAT(CONCAT('%', #{LDMC}),'%')
            )
        </if>
        <if test="DJ != null and DJ != ''">
            <!-- 关键词检索 道路等级 -->
            and
            t.DJ = #{DJ}
        </if>
        order by t.DJ
    </select>
    <!-- 修改 -->
    <update id="edit" parameterType="pd">
        update
        <include refid="tableName">
        </include>
        set
        "MBMC" = #{MBMC},
        "PX" = #{PX},
        "LX" = #{LX},
        "WEB" = #{WEB},
        "SD1" = #{SD1},
        "SD2" = #{SD2},
        "SD3" = #{SD3},
        "SD4" = #{SD4}
        where
        "ID" = #{ID}
    </update>

    <!-- 通过ID获取数据 -->
    <select id="findById" parameterType="pd" resultType="pd">
        select
        <include refid="Field">
        </include>
        from
        <include refid="tableName">
        </include>
        f
        where
        f."ID" = #{ID}
    </select>

    <!-- 列表 -->
    <select id="datalistPage" parameterType="page" resultType="pd">
        select
        <include refid="Field"></include>
        from
        <include refid="tableName"></include> f
        where 1=1
        <if test="pd.MBMCKEYWORDS != null and pd.MBMCKEYWORDS != ''"><!-- 关键词检索 账号 -->
            and
            (
                MBMC LIKE CONCAT(CONCAT('%', #{pd.MBMCKEYWORDS}),'%')
            )
        </if>
        <if test="pd.MBLXKEYWORDS != null and pd.MBLXKEYWORDS != ''">
            <!-- 关键词检索 模板类型-->
            and
            (
            LX LIKE CONCAT(CONCAT('%', #{pd.MBLXKEYWORDS}),'%')
            )
        </if>
        <if test="pd.WEBKEYWORDS != null and pd.WEBKEYWORDS != ''">
            <!-- 关键词检索 是否是外网 -->
            and
            (
            WEB LIKE CONCAT(CONCAT('%', #{pd.WEBKEYWORDS}),'%')
            )
        </if>
        <if test="pd.STARTTJSJKEYWORDS != null"><!-- 关键词检索 起始添加时间-->
            and to_char(TJSJ,'yyyy-MM-dd') <![CDATA[ >= ]]> #{pd.STARTTJSJKEYWORDS}
        </if>

        <if test="pd.ENDTJSJKEYWORDS != null"><!-- 关键词检索 终止添加时间-->
            and to_char(TJSJ,'yyyy-MM-dd') <![CDATA[ <= ]]> #{pd.ENDTJSJKEYWORDS}
        </if>
    </select>

    <!-- 列表(全部) -->
    <select id="listAll" parameterType="pd" resultType="pd">
        SELECT
	    f.MBMC,f.ID
        FROM
	    PASLICENSE_MOULD f order by PX
    </select>
    <select id="listById" parameterType="pd" resultType="org.fh.entity.pass.RoadPart">
       SELECT xmlagg(xmlparse(content d.ROADPARTID||',' wellformed) order by d.ROADPARTID).getclobval()   AS ROADPARTID,
        xmlagg(xmlparse(content t.LDMC||',' wellformed) order by t.LDMC).getclobval()  AS LDMC,
           f.SD1,f.SD2,f.SD3,f.SD4
        FROM PASLICENSE_MOULD f
        LEFT JOIN PASLICENSE_MOULDINFO d ON f.ID = d.MBID
        LEFT JOIN PASLICENSE_ROADPART t ON d.ROADPARTID = t.ID
        WHERE
	        f.ID = #{ID}
        GROUP BY
           f.SD1, f.SD2, f.SD3, f.SD4
    </select>

<!--    <select id="listAll" parameterType="page" resultType="pd">-->
<!--        SELECT-->
<!--          d.ROADPARTID,-->
<!--          t.LDMC,-->
<!--          t.DJ-->
<!--            FROM-->
<!--          PASLICENSE_MOULD f-->
<!--          LEFT JOIN PASLICENSE_MOULDINFO d ON f.ID = d.MBID-->
<!--          LEFT JOIN PASLICENSE_ROADPART t ON d.ROADPARTID = t.ID-->
<!--          WHERE-->
<!--          f.ID = {ID}-->
<!--    </select>-->
    <select id="getCount" parameterType="pd" resultType="int">
        select COUNT(*) from(
        select f."ID" from <include refid="tableName"></include> f where 1=1
        <if test="MBMCKEYWORDS != null and MBMCKEYWORDS != ''">
            <!-- 关键词检索 模板名称 -->
            and
            (
            MBMC LIKE CONCAT(CONCAT('%', #{MBMCKEYWORDS}),'%')
            )
        </if>
        <if test="MBLXKEYWORDS != null and MBLXKEYWORDS != ''">
            <!-- 关键词检索 模板类型-->
            and
            (
            LX LIKE CONCAT(CONCAT('%', #{MBLXKEYWORDS}),'%')
            )
        </if>
        <if test="WEBKEYWORDS != null and WEBKEYWORDS != ''">
            <!-- 关键词检索 是否是外网 -->
            and
            (
            WEB LIKE CONCAT(CONCAT('%', #{WEBKEYWORDS}),'%')
            )
        </if>
        <if test="STARTTJSJKEYWORDS != null"><!-- 关键词检索 起始添加时间-->
            and TJSJ <![CDATA[ >= ]]> #{STARTTJSJKEYWORDS}
        </if>
        <if test="ENDTJSJKEYWORDS != null">
            and TJSJ <![CDATA[ <= ]]> #{ENDTJSJKEYWORDS}
        </if>
        )tmp_count
    </select>

    <!-- 批量删除 -->
    <delete id="deleteAll" parameterType="String">
        delete from
        <include refid="tableName">
        </include>
        where
        "ID" in
        <foreach item="item" index="index" collection="array" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- fh313596790qq(青苔) -->
</mapper>
