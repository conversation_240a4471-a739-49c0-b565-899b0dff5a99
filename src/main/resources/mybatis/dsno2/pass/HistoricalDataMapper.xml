<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno2.pass.HistoricalDataMapper">

    <!--表名 -->
    <sql id="tableName">
		"PASLICENSE"
	</sql>

    <!--数据字典表名 -->
    <sql id="dicTableName">
		"SYS_DICTIONARIES"
	</sql>

    <!-- 字段 -->
    <sql id="Field">
		f."XH",
		f."TXZLX",
		f."TXZHM",
		f."HPZL",
		f."HPHM",
		f."CLLX",
		f."SYR",
		f."LX",
		f."YXRQS",
		f."YXRQZ",
		f."J<PERSON>",
		f."GXSJ",
		f."DJRQ",
		f."SPR",
		f."SPBJ",
		f."SPRQ",
		f."GFSD",
		f."YXSJ",
		f."YXRQ",
		f."LDJZ",
		f."BZ",
		f."DYBJ",
		f."ZT",
		f."YLZD1",
		f."YLZD2",
		f."YLZD3",
		f."YLZD4",
		f."ZX",
		f."JUJUE",
		f."JUETEXT",
		f."SPONE",
		f."SPONEPEOPLE",
		f."SPTWO",
		f."SPTWOPEOPLE",
		f."SPTHREE",
		f."SPTHREEPEOPLE",
		f."SPBJ2",
		f."LSSP",
		f."LSSPBJ",
		f."LY",
		f."YSSBSD",
		f."LSTXZ",
		f."DLJB",
		f."TXJX",
		f."ZFZ",
		f."TLPP",
		f."SFYGX",
		f.AUDITING_STATUS
	</sql>


    <!-- 字段用于新增 -->
    <sql id="Field2">
		"XH",
		"TXZLX",
		"TXZHM",
		"HPZL",
		"HPHM",
		"CLLX",
		"SYR",
		"LX",
		"YXRQS",
		"YXRQZ",
		"JBR",
		"GXSJ",
		"DJRQ",
		"SPR",
		"SPBJ",
		"SPRQ",
		"GFSD",
		"YXSJ",
		"YXRQ",
		"LDJZ",
		"BZ",
		"DYBJ",
		"ZT",
		"YLZD1",
		"YLZD2",
		"YLZD3",
		"YLZD4",
		"ZX",
		"JUJUE",
		"JUETEXT",
		"SPONE",
		"SPONEPEOPLE",
		"SPTWO",
		"SPTWOPEOPLE",
		"SPTHREE",
		"SPTHREEPEOPLE",
		"SPBJ2",
		"LSSP",
		"LSSPBJ",
		"YLZD5",
		"LY",
		"YLZD6",
		"YSSBSD",
		"LSTXZ",
		"TXSBBH",
		"DLJB",
		"TXJX",
		"ZFZ",
		"TLPP",
		"SBSD",
		"SFYGX"
	</sql>
	<!-- 用于查询分页字段 -->
	<!-- 字段 -->
    <sql id="Field3">
    t2."XH",
    t2."TXZLX",
    t2."TXZHM",
    t2."HPZL",
    t2."HPHM",
    t2."CLLX",
    t2."SYR",
    t2."LX",
    t2."YXRQS",
    t2."YXRQZ",
    t2."JBR",
    t2."GXSJ",
    t2."DJRQ",
    t2."SPR",
    t2."SPBJ",
    t2."SPRQ",
    t2."GFSD",
    t2."YXSJ",
    t2."YXRQ",
    t2."LDJZ",
    t2."BZ",
    t2."DYBJ",
    t2."ZT",
    t2."YLZD1",
    t2."YLZD2",
    t2."YLZD3",
    t2."YLZD4",
    t2."ZX",
    t2."JUJUE",
    t2."JUETEXT",
    t2."SPONE",
    t2."SPONEPEOPLE",
    t2."SPTWO",
    t2."SPTWOPEOPLE",
    t2."SPTHREE",
    t2."SPTHREEPEOPLE",
    t2."SPBJ2",
    t2."LSSP",
    t2."LSSPBJ",

    t2."LY",

    t2."YSSBSD",
    t2."LSTXZ",
    t2."TXSBBH",
    t2."DLJB",
    t2."TXJX",
    t2."ZFZ",
    t2."TLPP",
    t2."SBSD"
</sql>

    <!-- 字段值 -->
    <sql id="FieldValue">
		#{XH},
		#{TXZLX},
		#{TXZHM},
		#{HPZL},
		#{HPHM},
		#{CLLX},
		#{SYR},
		#{LX},
		#{YXRQS},
		#{YXRQZ},
		#{JBR},
		#{GXSJ},
		#{DJRQ},
		#{SPR},
		#{SPBJ},
		#{SPRQ},
		#{GFSD},
		#{YXSJ},
		#{YXRQ},
		#{LDJZ},
		#{BZ},
		#{DYBJ},
		#{ZT},
		#{YLZD1},
		#{YLZD2},
		#{YLZD3},
		#{YLZD4},
		#{ZX},
		#{JUJUE},
		#{JUETEXT},
		#{SPONE},
		#{SPONEPEOPLE},
		#{SPTWO},
		#{SPTWOPEOPLE},
		#{SPTHREE},
		#{SPTHREEPEOPLE},
		#{SPBJ2},
		#{LSSP},
		#{LSSPBJ},
		#{YLZD5},
		#{LY},
		#{YLZD6},
		#{YSSBSD},
		#{LSTXZ},
		#{TXSBBH},
		#{DLJB},
		#{TXJX},
		#{ZFZ},
		#{TLPP},
		#{SBSD},
		#{SFYGX}
	</sql>

    <!-- 新增-->
    <insert id="save" parameterType="pd">
        insert into
        <include refid="tableName"></include>
        (
        <include refid="Field2"></include>
        ) values (
        <include refid="FieldValue"></include>
        )
    </insert>

    <!-- 注销-->


    <!-- 修改 -->
    <update id="edit" parameterType="pd">
        update
        <include refid="tableName"></include>
        set
        "XH" = #{XH},
        "TXZLX" = #{TXZLX},
        "TXZHM" = #{TXZHM},
        "HPZL" = #{HPZL},
        "HPHM" = #{HPHM},
        "CLLX" = #{CLLX},
        "SYR" = #{SYR},
        "LX" = #{LX},
        "YXRQS" = #{YXRQS},
        "YXRQZ" = #{YXRQZ},
        "JBR" = #{JBR},
        "GXSJ" = #{GXSJ},
        "DJRQ" = #{DJRQ},
        "SPR" = #{SPR},
        "SPBJ" = #{SPBJ},
        "SPRQ" = #{SPRQ},
        "GFSD" = #{GFSD},
        "YXSJ" = #{YXSJ},
        "YXRQ" = #{YXRQ},
        "LDJZ" = #{LDJZ},
        "BZ" = #{BZ},
        "DYBJ" = #{DYBJ},
        "ZT" = #{ZT},
        "YLZD1" = #{YLZD1},
        "YLZD2" = #{YLZD2},
        "YLZD3" = #{YLZD3},
        "YLZD4" = #{YLZD4},
        "ZX" = #{ZX},
        "JUJUE" = #{JUJUE},
        "JUETEXT" = #{JUETEXT},
        "SPONE" = #{SPONE},
        "SPONEPEOPLE" = #{SPONEPEOPLE},
        "SPTWO" = #{SPTWO},
        "SPTWOPEOPLE" = #{SPTWOPEOPLE},
        "SPTHREE" = #{SPTHREE},
        "SPTHREEPEOPLE" = #{SPTHREEPEOPLE},
        "SPBJ2" = #{SPBJ2},
        "LSSP" = #{LSSP},
        "LSSPBJ" = #{LSSPBJ},
        "YLZD5" = #{YLZD5},
        "LY" = #{LY},
        "YLZD6" = #{YLZD6},
        "YSSBSD" = #{YSSBSD},
        "LSTXZ" = #{LSTXZ},
        "TXSBBH" = #{TXSBBH},
        "DLJB" = #{DLJB},
        "TXJX" = #{TXJX},
        "ZFZ" = #{ZFZ},
        "TLPP" = #{TLPP},
        "SBSD" = #{SBSD},
        "SFYGX" = #{SFYGX}
        where
        "XH" = #{XH}
    </update>

    <!-- 通过ID获取数据 -->
    <select id="findById" parameterType="pd" resultType="pd">
        select
        <include refid="Field"></include>
        from
        <include refid="tableName"></include>
        f
        where
        f."XH" = #{XH}
    </select>

    <!-- 列表 -->
    <select id="datalistPage" parameterType="page" resultType="pd">
        select
        <include refid="Field"></include>
        from
        PASLICENSE f
        where
        f.AUDITING_STATUS = 1
        <if test="pd.HPZLKEYWORDS != null and pd.HPZLKEYWORDS != ''"><!-- 关键词检索 号牌种类 -->
            and
            (
            HPZL LIKE CONCAT(CONCAT('%', #{pd.HPZLKEYWORDS}),'%')
            )
        </if>
        <if test="pd.HPHMKEYWORDS != null and pd.HPHMKEYWORDS != ''"><!-- 关键词检索 号牌号码-->
            and
            (
            HPHM LIKE CONCAT(CONCAT('%', #{pd.HPHMKEYWORDS}),'%')
            )
        </if>
        <if test="pd.JBRKEYWORDS != null and pd.JBRKEYWORDS != ''"><!-- 关键词检索 经办人-->
            and
            (
            JBR LIKE CONCAT(CONCAT('%', #{pd.JBRKEYWORDS}),'%')
            )
        </if>
        <if test="pd.JDCSYRKEYWORDS != null and pd.JDCSYRKEYWORDS != ''"><!-- 关键词检索 机动车所有人-->
            and
            (
            SYR LIKE CONCAT(CONCAT('%', #{pd.JDCSYRKEYWORDS}),'%')
            )
        </if>
        <if test="pd.CLLXKEYWORDS != null and pd.CLLXKEYWORDS != ''"><!-- 关键词检索 车辆类型-->
            and
            (
            CLLX LIKE CONCAT(CONCAT('%', #{pd.CLLXKEYWORDS}),'%')
            )
        </if>
        <if test="pd.BZ != null and pd.BZ != ''"><!-- 关键词检索 备注-->
            and
            (
            BZ LIKE CONCAT(CONCAT('%', #{pd.BZ}),'%')
            )
        </if>
        <if test="pd.STARTDJRQKEYWORDS != null"><!-- 关键词检索 起始登记日期-->
            and to_char(DJRQ,'yyyy-MM-dd') <![CDATA[ >= ]]> #{pd.STARTDJRQKEYWORDS}
        </if>
        <if test="pd.ENDDJRQKEYWORDS != null "><!-- 关键词检索 终止登记日期-->
            and to_char(DJRQ,'yyyy-MM-dd') <![CDATA[ <= ]]> #{pd.ENDDJRQKEYWORDS}
        </if>
        <if test="pd.YXQKEYWORDS != null and pd.msg == '有效'"><!-- 关键词检索 是否在有效期-->
            and
            (ZX = 0
             and SYSDATE between YXRQS and YXRQZ)
        </if>
        <if test="pd.YXQKEYWORDS != null and pd.msg == '无效'"><!-- 关键词检索 是否在有效期-->
            and
            (ZX = 1
            or SYSDATE > YXRQZ)
        </if>
        order by DJRQ desc
    </select>

    <!-- 列表(全部) -->
    <select id="listAll" parameterType="pd" resultType="pd">
        select
        <include refid="Field"></include>
        from
        <include refid="tableName"></include>
        f
    </select>

    <!--获取总记录数-->
    <select id="getCount" parameterType="pd" resultType="int">
        select count(*) from (
        select   f."XH" from PASLICENSE F where 1=1
        <if test="HPZLKEYWORDS != null and HPZLKEYWORDS != ''"><!-- 关键词检索 号牌种类 -->
            and
            (
            HPZL LIKE CONCAT(CONCAT('%', #{HPZLKEYWORDS}),'%')
            )
        </if>
        <if test="HPHMKEYWORDS != null and HPHMKEYWORDS != ''"><!-- 关键词检索 号牌号码-->
            and
            (
            HPHM LIKE CONCAT(CONCAT('%', #{HPHMKEYWORDS}),'%')
            )
        </if>
        <if test="JBRKEYWORDS != null and JBRKEYWORDS != ''"><!-- 关键词检索 经办人-->
            and
            (
            JBR LIKE CONCAT(CONCAT('%', #{JBRKEYWORDS}),'%')
            )
        </if>
        <if test="JDCSYRKEYWORDS != null and JDCSYRKEYWORDS != ''"><!-- 关键词检索 机动车所有人-->
            and
            (
            SYR LIKE CONCAT(CONCAT('%', #{JDCSYRKEYWORDS}),'%')
            )
        </if>

        <if test="CLLXKEYWORDS != null and CLLXKEYWORDS != ''"><!-- 关键词检索 车辆类型-->
            and
            (
            CLLX LIKE CONCAT(CONCAT('%', #{CLLXKEYWORDS}),'%')
            )
        </if>

        <if test="STARTDJRQKEYWORDS != null"><!-- 关键词检索 起始登记日期-->
            and  DJRQ <![CDATA[>=]]> #{STARTDJRQKEYWORDS,jdbcType=DATE}
        </if>
        <if test="ENDDJRQKEYWORDS != null"><!-- 关键词检索 终止登记日期-->
            and  DJRQ <![CDATA[ < ]]> #{ENDDJRQKEYWORDS,jdbcType=DATE}
        </if>

        <if test="YXQKEYWORDS != null and msg == '有效'"><!-- 关键词检索 是否在有效期-->
            and YXRQZ <![CDATA[>=]]> #{pd.YXQKEYWORDS,jdbcType=DATE}
         </if>
        <if test="YXQKEYWORDS != null and msg == '无效'"><!-- 关键词检索 是否在有效期-->
            and YXRQZ <![CDATA[<]]> #{YXQKEYWORDS,jdbcType=DATE}
        </if>
        ) tmp_count

    </select>
    <!-- 批量删除 -->
    <update id="deleteAll" parameterType="String">
        update <include refid="tableName"></include>
        set
        ZX = 1
        where
        "XH" in
        <foreach item="item" index="index" collection="array" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <update id="zx" parameterType="pd">
        update
        <include refid="tableName"></include>
        set
        ZX = #{ZX},
        GXSJ=#{GXSJ}
        where
        XH = #{XH}
    </update>

    <!-- 批量注销 -->
    <update id="zx_All" parameterType="String">
        update
        <include refid="tableName"></include>
        set
        ZX = 1,
        GXSJ = #{GXSJ}
        where
        ZX=0
        and
        "XH" in
        <foreach item="item" index="index" collection="ArrayDATA_IDS" open="(" separator="," close=")">
            #{item}
        </foreach>

    </update>

    <!-- fh313596790qq(青苔) -->
</mapper>
