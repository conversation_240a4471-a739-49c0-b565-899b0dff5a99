<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno2.pass.BlacklistMapper">

    <!--表名 -->
    <sql id="tableName">
		"PASBLAWHILIST"
	</sql>

    <!--数据字典表名 -->
    <sql id="dicTableName">
		"SYS_DICTIONARIES"
	</sql>

    <!-- 字段 -->
    <sql id="Field">
		f."XH",
		f."HPZL",
		f."HPHM",
		f."CLLX",
		f."SYR",
		f."ZT",
		f."BZ",
		f."GXRQ",
		f."HMDSX",
		f."MEMBERIDCARD"
	</sql>

    <!-- 字段用于新增 -->
    <sql id="Field2">
		"XH",
		"HPZL",
		"HPHM",
		"CLLX",
		"ZT",
		"BZ",
		"GXRQ",
		"HMDSX"
	</sql>

    <!-- 字段值 -->
    <sql id="FieldValue">
		#{XH},
		#{HPZL},
		#{HPHM},
		#{CLLX},
		#{ZT},
		#{BZ},
		#{GXRQ},
		#{HMDSX}
	</sql>

    <!-- 新增-->
    <insert id="save" parameterType="pd">
        insert into
        <include refid="tableName"></include>
        (
        "XH",
        "HPZL",
        "HPHM",
        "CLLX",
        "ZT",
        "BZ",
        <if test="HMDSX != null"><!-- 关键词检索 起始登记日期-->
            "HMDSX",
        </if>
        "GXRQ"
        ) values (
        #{XH},
        #{HPZL},
        #{HPHM},
        #{CLLX},
        #{ZT},
        #{BZ},
        <if test="HMDSX != null"><!-- 关键词检索 起始登记日期-->
            #{HMDSX},
        </if>
        #{GXRQ}
        )
    </insert>
    <insert id="savesyr" parameterType="pd">
        insert into
        <include refid="tableName"></include>
        (
        "XH",
        "SYR",
        "ZT",
        "BZ",

        <if test="HMDSX != null">
            "HMDSX",
        </if>
        <if test="MEMBERIDCARD != null">
            "MEMBERIDCARD",
        </if>
        "GXRQ"
        ) values (
        #{XH},
        #{SYR},
        #{ZT},
        #{BZ},
        <if test="HMDSX != null">
            #{HMDSX},
        </if>
        <if test="MEMBERIDCARD != null">
            #{MEMBERIDCARD},
        </if>
        #{GXRQ}
        )
    </insert>

    <!-- 删除-->
    <delete id="delete" parameterType="pd">
        delete from
        <include refid="tableName"></include>
        where
        "XH" = #{XH}
    </delete>

    <!-- 修改 -->
    <update id="edit" parameterType="pd">
        update
        <include refid="tableName"></include>
        set
        <if test="HPZL != null">
            "HPZL" = #{HPZL},
        </if>
        <if test="HPHM != null">
            "HPHM" = #{HPHM},
        </if>
        <if test="CLLX != null">
            "CLLX" = #{CLLX},
        </if>
        <if test="SYR != null">
            "SYR" = #{SYR},
        </if>
        <if test="ZT != null">
            "ZT" = #{ZT},
        </if>
        <if test="BZ != null">
            "BZ" = #{BZ},
        </if>
        <if test="HMDSX != null">
            "HMDSX" = #{HMDSX},
        </if>
        <if test="MEMBERIDCARD != null">
            "MEMBERIDCARD" = #{MEMBERIDCARD},
        </if>
        <if test="GXRQ != null">
            "GXRQ" = #{GXRQ}
        </if>
        where
        "XH" = #{XH}
    </update>

    <!-- 通过ID获取数据 -->
    <select id="findById" parameterType="pd" resultType="pd">
        select
        <include refid="Field"></include>
        from
        <include refid="tableName"></include>
        f
        where
        f."XH" = #{XH}
    </select>

    <!-- 列表 -->
    <select id="datalistPage" parameterType="page" resultType="pd">
        select
        <include refid="Field"></include>
        from
        <include refid="tableName"></include>
        f
        where f.ZT = 1
        <if test="pd.HPZLKEYWORDS != null and pd.HPZLKEYWORDS != ''"><!-- 关键词检索 号牌种类 -->
            and
            (
            HPZL LIKE CONCAT(CONCAT('%', #{pd.HPZLKEYWORDS}),'%')
            )
        </if>
        <if test="pd.HPHMKEYWORDS != null and pd.HPHMKEYWORDS != ''"><!-- 关键词检索 号牌号码-->
            and
            (
            HPHM LIKE CONCAT(CONCAT('%', #{pd.HPHMKEYWORDS}),'%')
            )
        </if>
        <if test="pd.BZ != null and pd.BZ != ''"><!-- 关键词检索 备注-->
            and
            (
            BZ LIKE CONCAT(CONCAT('%', #{pd.BZ}),'%')
            )
        </if>
        <if test="pd.SYR != null and pd.SYR != ''"><!-- 关键词检索 备注-->
            and
            (
            SYR LIKE CONCAT(CONCAT('%', #{pd.SYR}),'%')
            )
        </if>

        <if test="pd.STARTTIME != null"><!-- 关键词检索 起始登记日期-->
            and to_char(GXRQ,'yyyy-MM-dd') <![CDATA[ >= ]]> #{pd.STARTTIME}
        </if>
        <if test="pd.ENDTIME != null"><!-- 关键词检索 终止登记日期-->
            and to_char(GXRQ,'yyyy-MM-dd') <![CDATA[ <= ]]> #{pd.ENDTIME}
        </if>
    </select>

    <!-- 列表(全部) -->
    <select id="listAll" parameterType="pd" resultType="pd">
        select
        <include refid="Field"></include>
        from
        <include refid="tableName"></include>
        f
    </select>

    <!-- 批量删除 -->
    <delete id="deleteAll" parameterType="String">
        delete from
        <include refid="tableName"></include>
        where
        "XH" in
        <foreach item="item" index="index" collection="array" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <select id="fandByHPId" parameterType="string" resultType="string">
        select XH from PASBLAWHILIST where  HPHM=#{hphm}
        and HPZL=#{hpzl}
    </select>
    <select id="fandByIdCard" parameterType="string" resultType="string">
        select XH from PASBLAWHILIST where  MEMBERIDCARD=#{syridcard}
    </select>
    <select id="fandBySYR" parameterType="string" resultType="string">
        select
        XH from PASBLAWHILIST where  SYR=#{SYR} and HPHM IS NULL and HPZL is null
    </select>


    <select id="highstate" parameterType="string" resultType="map">

        	SELECT YXRQZ,ZX FROM (SELECT * FROM PASLICENSE_VEH t ORDER BY DJRQ DESC)
        	 PASLICENSE_VEH WHERE  hphm =#{hphm}  and hpzl=#{hpzl}
            and  lstxz=#{lstxz}
		  		    and AUDITING_STATUS=1 and ZX = 0
                and rownum=1

    </select>

    <select id="overdue" parameterType="string" resultType="map">

        SELECT YXRQZ,ZX
        FROM (SELECT * FROM PASLICENSE_VEH t ORDER BY DJRQ DESC)
        PASLICENSE_VEH
        WHERE hphm =#{hphm}
        and hpzl =#{hpzl}
        and lstxz &lt;>2
        and AUDITING_STATUS=1
        and ZX = 0
        and ZFZ = 1
        and rownum=1

    </select>

</mapper>
