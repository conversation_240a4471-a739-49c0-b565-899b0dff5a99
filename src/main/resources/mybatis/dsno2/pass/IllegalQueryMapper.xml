<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno2.pass.IllegalQueryMapper">
	
	<!--表名 -->
	<sql id="tableName">
		"PASLICENSE_ILLEGAL_INQUIRY"
	</sql>
	
	<!--数据字典表名 -->
	<sql id="dicTableName">
		"SYS_DICTIONARIES"
	</sql>
	
	<!-- 字段 -->
	<sql id="Field">
		f."ID",	
		f."HPHM",	
		f."HPZL",	
		f."WFTS",	
		f."CJSJ",	
		f."TJSJ"
	</sql>
	
	<!-- 字段用于新增 -->
	<sql id="Field2">
		"ID",	
		"HPHM",	
		"HPZL",	
		"WFTS",	
		"CJSJ",	
		"TJSJ"
	</sql>
	
	<!-- 字段值 -->
	<sql id="FieldValue">
		#{ID},	
		#{HPHM},	
		#{HPZL},	
		#{WFTS},	
		#{CJSJ},	
		#{TJSJ}
	</sql>
	
	<!-- 新增-->
	<insert id="save" parameterType="pd">
		insert into
		<include refid="tableName"></include>
		(
		<include refid="Field2"></include>
		) values (
		<include refid="FieldValue"></include>
		)
	</insert>
	
	<!-- 删除-->
	<delete id="delete" parameterType="pd">
		delete from
		<include refid="tableName"></include>
		where
			"ID" = #{ID}
	</delete>
	
	<!-- 修改 -->
	<update id="edit" parameterType="pd">
		update
		<include refid="tableName"></include>
			set 
				"ID" = #{ID},	
				"HPHM" = #{HPHM},	
				"HPZL" = #{HPZL},	
				"WFTS" = #{WFTS},	
				"CJSJ" = #{CJSJ},	
				"TJSJ" = #{TJSJ}
			where
				"ID" = #{ID}
	</update>
	
	<!-- 通过号牌号码和号牌种类获取数据 -->
	<select id="findByHPHMAndHPZL" parameterType="pd" resultType="pd">
		select
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include> f
		where 
			f."ID" = #{ID}
	</select>
	
	<!-- 列表 -->
	<select id="datalistPage" parameterType="page" resultType="pd">
		select
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include> f
		where 1=1
		<if test="pd.KEYWORDS != null and pd.KEYWORDS != ''"><!-- 关键词检索 -->
			and
				(
				<!--	根据需求自己加检索条件
					字段1 LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
					 or 
					字段2 LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%') 
				-->
				)
		</if>
	</select>
	
	<!-- 列表(全部) -->
	<select id="listAll" parameterType="pd" resultType="pd">
		select
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include> f
	</select>
	
	<!-- 批量删除 -->
	<delete id="deleteAll" parameterType="String">
		delete from
		<include refid="tableName"></include>
		where 
			"ID" in
		<foreach item="item" index="index" collection="array" open="(" separator="," close=")">
                 #{item}
		</foreach>
	</delete>
	
	<!-- fh313596790qq(青苔) -->
</mapper>