<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno2.pass.TxzMapper">

    <!--表名 -->
    <sql id="tableName">
		PASLICENSE
	</sql>

    <!--数据字典表名 -->
    <sql id="dicTableName">
		SYS_DICTIONARIES
	</sql>

    <!-- 字段 -->
    <sql id="Field">
		f.XH,
		f.TXZLX,
		f.TXZHM,
		f.HPZL,
		f.HPHM,
		f.CLLX,
		f.SYR,
		f.LX,
		f.YXRQS,
		f.YXRQZ,
		f.JBR,
		f.GXSJ,
		f.DJRQ,
		f.SPR,
		f.SPBJ,
		f.SPRQ,
		f.<PERSON>,
		f.<PERSON>,
		f.YX<PERSON>,
		f.<PERSON>,
		f.B<PERSON>,
		f.DYBJ,
		f.ZT,
		f.YLZD1,
		f.YLZD2,
		f.YLZD3,
		f.YLZD4,
		f.ZX,
		f.JUJUE,
		f.JUETEXT,
		f.SPONE,
		f.SPONEPEOPLE,
		f.SPTWO,
		f.SPTWOPEOPLE,
		f.SPTHREE,
		f.SPTHREEPEOPLE,
		f.SPBJ2,
		f.LSSP,
		f.LSSPBJ,
		f.LY,
		f.YSSBSD,
		f.LSTXZ,
		f.TXJX,
		f.ZFZ,
		f.TLPP,
		f.SFYGX,
		f.AUDITING_STATUS
	</sql>

    <!-- 字段用于新增 -->
    <sql id="Field2">
		XH,
		TXZLX,
		TXZHM,
		HPZL,
		HPHM,
		CLLX,
		SYR,
		LX,
		YXRQS,
		YXRQZ,
		JBR,
		GXSJ,
		DJRQ,
		SPR,
		SPBJ,
		SPRQ,
		GFSD,
		YXSJ,
		YXRQ,
		LDJZ,
		BZ,
		DYBJ,
		ZT,
		YLZD1,
		YLZD2,
		YLZD3,
		YLZD4,
		ZX,
		JUJUE,
		JUETEXT,
		SPONE,
		SPONEPEOPLE,
		SPTWO,
		SPTWOPEOPLE,
		SPTHREE,
		SPTHREEPEOPLE,
		SPBJ2,
		LSSP,
		LSSPBJ,
		YLZD5,
		LY,
		YLZD6,
		YSSBSD,
		LSTXZ,
		TXSBBH,
		DLJB,
		TXJX,
		ZFZ,
		TLPP,
		SBSD,
		SFYGX,
		AUDITING_STATUS
	</sql>

    <!-- 字段值 -->
    <sql id="FieldValue">
	#{XH},
	#{TXZLX},
	#{TXZHM},
	#{HPZL},
	#{HPHM},
	#{CLLX},
	#{SYR},
	#{LX},
	#{YXRQS},
	#{YXRQZ},
	#{JBR},
	#{GXSJ},
	#{DJRQ},
	#{SPR},
	#{SPBJ},
	#{SPRQ},
	#{GFSD},
	#{YXSJ},
	#{YXRQ},
	#{LDJZ},
	#{BZ},
	#{DYBJ},
	#{ZT},
	#{YLZD1},
	#{YLZD2},
	#{YLZD3},
	#{YLZD4},
	#{ZX},
	#{JUJUE},
	#{JUETEXT},
	#{SPONE},
	#{SPONEPEOPLE},
	#{SPTWO},
	#{SPTWOPEOPLE},
	#{SPTHREE},
	#{SPTHREEPEOPLE},
	#{SPBJ2},
	#{LSSP},
	#{LSSPBJ},
	#{YLZD5},
	#{LY},
	#{YLZD6},
	#{YSSBSD},
	#{LSTXZ},
	#{TXSBBH},
	#{DLJB},
	#{TXJX},
	#{ZFZ},
	#{TLPP},
	#{SBSD},
	#{SFYGX},
	#{AUDITING_STATUS}
	</sql>
    <!-- 新增-->
    <insert id="save" parameterType="pd">
        insert into
        <include refid="tableName"></include>
        (
        <include refid="Field2"></include>
        ) values (
        <include refid="FieldValue"></include>
        )
    </insert>

    <!-- 删除-->
    <delete id="delete" parameterType="pd">
        delete from
        <include refid="tableName"></include>
        where
        XH = #{XH}
    </delete>

    <!-- 修改 -->
    <update id="edit" parameterType="pd">
        update
        <include refid="tableName"></include>
        set
        <if test="AUDITING_STATUS != null and AUDITING_STATUS != ''">
            AUDITING_STATUS = #{AUDITING_STATUS},
        </if>
        <if test="TXZLX != null and TXZLX != ''">
            TXZLX = #{TXZLX},
        </if>
        <if test="HPZL != null and HPZL != ''">
            HPZL = #{HPZL},
        </if>
        <if test="HPHM != null and HPHM != ''">
            HPHM = #{HPHM},
        </if>
        <if test="CLLX != null and CLLX != ''">
            CLLX = #{CLLX},
        </if>
        <if test="SYR != null and SYR != ''">
            SYR = #{SYR},
        </if>
        <if test="YLZD5 != null and YLZD5 != ''">
            YLZD5 = #{YLZD5},
        </if>
        <if test="YLZD6 != null and YLZD6 != ''">
            YLZD6 = #{YLZD6},
        </if>
        <if test="YXRQS != null and YXRQS != ''">
            YXRQS = #{YXRQS},
        </if>
        <if test="YXRQZ != null and YXRQZ != ''">
            YXRQZ = #{YXRQZ},
        </if>
        <if test="YXRQ != null and YXRQ != ''">
            YXRQ = #{YXRQ},
        </if>
        <if test="dljb != null and dljb != ''">
            dljb = #{dljb},
        </if>
        <if test="TXJX != null and TXJX != ''">
            TXJX = #{TXJX},
        </if>
        <if test="LDJZ != null and LDJZ != ''">
            LDJZ = #{LDJZ},
        </if>
        <if test="BZ != null and BZ != ''">
            BZ = #{BZ},
        </if>
        <if test="YSSBSD != null and YSSBSD != ''">
            YSSBSD = #{YSSBSD}
        </if>
        where
        XH = #{XH}
    </update>

    <!-- 修改 -->
    <update id="update" parameterType="pd">
        update
        <include refid="tableName"></include>
        set
        <if test="YXRQS != null and YXRQS != ''">
            "YXRQS" = #{YXRQS},
        </if>
        <if test="YXRQZ != null and YXRQZ != ''">
            "YXRQZ" = #{YXRQZ},
        </if>
        <if test="YXRQ != null and YXRQ != ''">
            "YXRQ" = #{YXRQ},
        </if>
        <if test="YLZD5 != null and YLZD5 != ''">
            "YLZD5" = #{YLZD5},
        </if>
        <if test="YLZD6 != null and YLZD6 != ''">
            "YLZD6" = #{YLZD6},
        </if>
        <if test="YSSBSD != null and YSSBSD != ''">
            "YSSBSD" = #{YSSBSD},
        </if>
        <if test="LDJZ != null and LDJZ != ''">
            "LDJZ" = #{LDJZ},
        </if>
        <if test="BZ != null and BZ != ''">
            "BZ" = #{BZ},
        </if>
        <if test="JBR != null and JBR != ''">
            "JBR" = #{JBR},
        </if>
        where
        HPHM = #{HPHM}
        and
        HPZL = #{HPZL}
    </update>

    <!-- 通过ID获取数据 -->
    <select id="findById" parameterType="pd" resultType="pd">
        select
        <include refid="Field"></include>
        from
        <include refid="tableName"></include>
        f
        where
        f.XH = #{XH}
    </select>
    <!--获取电子通行证列表-->
    <select id="listPageselectList" parameterType="page" resultType="pd">
        select
        XH,TXZHM,HPHM,HPZL,YXRQS,YXRQZ,LSTXZ,AUDITING_STATUS
        from
        PASLICENSE_VEH where XH in(
        select
        f.XH
        from
        PASLICENSE_VEH
        f
        LEFT JOIN
        PASLICENSE_ENTERPRISE_VEH t2 on f.HPZL = t2.VEHTYPE and f.HPHM = t2.VEHNUM
        where 1=1
        <if test="pd.MEMBERIDCARD != null and pd.MEMBERIDCARD != ''">
            and
            (t2.ACTIDCARD = #{pd.MEMBERIDCARD} or t2.MEMBERIDCARD= #{pd.MEMBERIDCARD})
        </if>
        <if test="pd.ENTERPRISEID != null and pd.ENTERPRISEID != ''">
            and
            t2.ENTERPRISEID = #{pd.ENTERPRISEID}
        </if>
        <if test="pd.HPHM != null and pd.HPHM != ''">
            and
            (
            f.HPHM LIKE CONCAT(CONCAT('%', #{pd.HPHM}),'%')
            )
        </if>
        and f.AUDITING_STATUS = 1
        and f.ZX = 0
        and f.YXRQZ > SYSDATE
        group by f.XH)

    </select>
    <!--获取未审批通行证列表-->
    <select id="listPageselectByList" parameterType="page" resultType="pd">
        select XH,TXZHM,HPHM,HPZL,YXRQS,YXRQZ,LSTXZ,AUDITING_STATUS  from
        PASLICENSE_VEH where XH in (
        select
        f.XH
        from
        PASLICENSE_VEH
        f
        LEFT JOIN
        PASLICENSE_ENTERPRISE_VEH t2 on f.HPZL = t2.VEHTYPE and f.HPHM = t2.VEHNUM
        where 1=1
        <if test="pd.MEMBERIDCARD != null and pd.MEMBERIDCARD != ''">
            and
            (t2.ACTIDCARD = #{pd.MEMBERIDCARD} or t2.MEMBERIDCARD= #{pd.MEMBERIDCARD} )
        </if>
        <if test="pd.ENTERPRISEID != null and pd.ENTERPRISEID != ''">
            and
            t2.ENTERPRISEID = #{pd.ENTERPRISEID}
        </if>
        <if test="pd.HPHM != null and pd.HPHM != ''">
            and
            (
            f.HPHM LIKE CONCAT(CONCAT('%', #{pd.HPHM}),'%')
            )
        </if>
        and f.AUDITING_STATUS = 0 group by f.XH )
    </select>
    <!-- 列表 -->
    <select id="datalistPage" parameterType="page" resultType="pd">
        select
        <include refid="Field"></include>
        from
        <include refid="tableName"></include>
        f
        where AUDITING_STATUS = 1
        <if test="pd.HPZLKEYWORDS != null and pd.HPZLKEYWORDS != ''"><!-- 关键词检索 号牌种类 -->
            and
            (
            HPZL LIKE CONCAT(CONCAT('%', #{pd.HPZLKEYWORDS}),'%')
            )
        </if>
        <if test="pd.HPHMKEYWORDS != null and pd.HPHMKEYWORDS != ''"><!-- 关键词检索 号牌号码-->
            and
            (
            HPHM LIKE CONCAT(CONCAT('%', #{pd.HPHMKEYWORDS}),'%')
            )
        </if>
        <if test="pd.JBRKEYWORDS != null and pd.JBRKEYWORDS != ''"><!-- 关键词检索 经办人-->
            and
            (
            JBR LIKE CONCAT(CONCAT('%', #{pd.JBRKEYWORDS}),'%')
            )
        </if>
        <if test="pd.JDCSYRKEYWORDS != null and pd.JDCSYRKEYWORDS != ''"><!-- 关键词检索 机动车所有人-->
            and
            (
            SYR LIKE CONCAT(CONCAT('%', #{pd.JDCSYRKEYWORDS}),'%')
            )
        </if>
        <if test="pd.CLLXKEYWORDS != null and pd.CLLXKEYWORDS != ''"><!-- 关键词检索 车辆类型-->
            and
            (
            CLLX LIKE CONCAT(CONCAT('%', #{pd.CLLXKEYWORDS}),'%')
            )
        </if>
        <if test="pd.STARTDJRQKEYWORDS != null"><!-- 关键词检索 起始登记日期-->
            and DJRQ <![CDATA[>=]]> #{pd.STARTDJRQKEYWORDS,jdbcType=DATE}
        </if>
        <if test="pd.ENDDJRQKEYWORDS != null"><!-- 关键词检索 终止登记日期-->
            and DJRQ <![CDATA[ < ]]> #{pd.ENDDJRQKEYWORDS,jdbcType=DATE}
        </if>

        <if test="pd.YXQKEYWORDS != null and pd.msg == '有效'"><!-- 关键词检索 是否在有效期-->
            and
            (ZX = 0
            and YXRQZ <![CDATA[>=]]> #{pd.YXQKEYWORDS,jdbcType=DATE})
        </if>
        <if test="pd.YXQKEYWORDS != null and pd.msg == '无效'"><!-- 关键词检索 是否在有效期-->
            and
            (ZX = 1
            or YXRQZ <![CDATA[<]]> #{pd.YXQKEYWORDS,jdbcType=DATE})
        </if>
    </select>

    <!-- 列表(全部) -->
    <select id="listAll" parameterType="pd" resultType="pd">
        select
        <include refid="Field"></include>
        from
        <include refid="tableName"></include>
        f
    </select>

    <!-- 批量删除 -->
    <delete id="deleteAll" parameterType="String">
        delete from
        <include refid="tableName"></include>
        where
        XH in
        <foreach item="item" index="index" collection="array" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- 批量审批 -->
    <update id="updateAll" parameterType="String">
        update
        <include refid="tableName"></include>
        set
        <if test="SPR != null and SPR != ''">
            "SPR" = #{SPR},
        </if>
        <if test="SPRQ != null">
            "SPRQ" = #{SPRQ},
        </if>
        AUDITING_STATUS = 1,
        GXSJ = #{GXSJ}
        where
        "XH" in
        <foreach item="item" index="index" collection="ArrayDATA_IDS" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <!-- 一键审批 -->
    <update id="updateById" parameterType="pd">
        update
        <include refid="tableName"></include>
        set
        <if test="SPR != null and SPR != ''">
            "SPR" = #{SPR},
        </if>
        <if test="SPRQ != null">
            "SPRQ" = #{SPRQ},
        </if>
        AUDITING_STATUS = 1,
        GXSJ = #{GXSJ}
        where
        "XH" = #{XH}
    </update>

    <select id="fandByHPZL" resultType="map">
  (select HPZL name, count(HPZL)  VALUE from PASLICENSE group by HPZL)
   order by HPZL Asc
  </select>

    <select id="fandBySPZT" resultType="map">
SELECT AUDITING_STATUS name ,count(AUDITING_STATUS) VALUE from PASLICENSE group by AUDITING_STATUS
  </select>


    <!--审批数据-->
    <select id="GetSPZT" parameterType="String" resultType="map">
        SELECT AUDITING_STATUS as ZT,COUNT(AUDITING_STATUS)as sps
        FROM PASLICENSE
        WHERE 1=1
        <if test="YSQS != null and YSQS.length()>0 ">
            and YXRQS &gt;=to_date (#{YSQS},'yyyy-mm-dd')
        </if>
        <if test="YXQZ != null and YXQZ.length()>0 ">
            and YXRQZ &lt;= to_date(#{YXQZ},'yyyy-mm-dd')
        </if>
        <if test="JBR != null and JBR.length()>0 ">
            and JBR=#{JBR}
        </if>
        GROUP BY AUDITING_STATUS
    </select>

    <!--通行证数据统计-->
    <select id="GetTXZ" parameterType="String" resultType="map">
        SELECT LSTXZ,COUNT(LSTXZ)as LSTXZS
        FROM PASLICENSE
        WHERE 1=1
        <if test="YSQS != null and YSQS.length()>0 ">
            and DJRQ &gt;=to_date (#{YSQS},'yyyy-mm-dd')
        </if>
        <if test="YXQZ != null and YXQZ.length()>0 ">
            and DJRQ &lt;= to_date(#{YXQZ},'yyyy-mm-dd')
        </if>
        <if test="JBR != null and JBR.length()>0 ">
            and JBR=#{JBR}
        </if>
        GROUP BY LSTXZ
    </select>
    <select id="GetCLLX" parameterType="String" resultType="map">
        SELECT HPZL,COUNT(HPZL) as HPZLS
        FROM PASLICENSE
        WHERE 1=1
        <if test="YSQS != null and YSQS.length()>0 ">
            and DJRQ &gt;=to_date (#{YSQS},'yyyy-mm-dd')
        </if>
        <if test="YXQZ != null and YXQZ.length()>0 ">
            and DJRQ &lt;= to_date(#{YXQZ},'yyyy-mm-dd')
        </if>
        <if test="JBR != null and JBR.length()>0 ">
            and JBR=#{JBR}
        </if>
        GROUP BY HPZL
    </select>
    <select id="GetRoad" parameterType="String" resultType="java.lang.String">
        select YLZD6 from PASLICENSE
        WHERE 1=1
        <if test="YSQS != null and YSQS.length()>0 ">
            and DJRQ &gt;=to_date (#{YSQS},'yyyy-mm-dd')
        </if>
        <if test="YXQZ != null and YXQZ.length()>0 ">
            and DJRQ &lt;= to_date(#{YXQZ},'yyyy-mm-dd')
        </if>
        <if test="JBR != null and JBR.length()>0 ">
            and JBR=#{JBR}
        </if>
    </select>

    <select id="datalist" parameterType="page" resultType="map">
        select
        count (1) as ZS,JBR
        from
        <include refid="tableName"></include>
        f
        where 1 = 1
        <if test="pd.HPZLKEYWORDS != null and pd.HPZLKEYWORDS != ''"><!-- 关键词检索 号牌种类 -->
            and
            (
            HPZL LIKE CONCAT(CONCAT('%', #{pd.HPZLKEYWORDS}),'%')
            )
        </if>
        <if test="pd.HPHMKEYWORDS != null and pd.HPHMKEYWORDS != ''"><!-- 关键词检索 号牌号码-->
            and
            (
            HPHM LIKE CONCAT(CONCAT('%', #{pd.HPHMKEYWORDS}),'%')
            )
        </if>
        <if test="pd.JBRKEYWORDS != null and pd.JBRKEYWORDS != ''"><!-- 关键词检索 经办人-->
            and
            (
            JBR LIKE CONCAT(CONCAT('%', #{pd.JBRKEYWORDS}),'%')
            )
        </if>
        <if test="pd.JDCSYRKEYWORDS != null and pd.JDCSYRKEYWORDS != ''"><!-- 关键词检索 机动车所有人-->
            and
            (
            SYR LIKE CONCAT(CONCAT('%', #{pd.JDCSYRKEYWORDS}),'%')
            )
        </if>
        <if test="pd.CLLXKEYWORDS != null and pd.CLLXKEYWORDS != ''"><!-- 关键词检索 车辆类型-->
            and
            (
            CLLX LIKE CONCAT(CONCAT('%', #{pd.CLLXKEYWORDS}),'%')
            )
        </if>
        <if test="pd.STARTDJRQKEYWORDS != null"><!-- 关键词检索 起始登记日期-->
            and DJRQ <![CDATA[>=]]> TO_DATE(#{pd.STARTDJRQKEYWORDS,jdbcType=TIMESTAMP}, 'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="pd.ENDDJRQKEYWORDS != null"><!-- 关键词检索 终止登记日期-->
            and DJRQ <![CDATA[ < ]]> TO_DATE(#{pd.ENDDJRQKEYWORDS,jdbcType=TIMESTAMP}, 'YYYY-MM-DD HH24:MI:SS')
        </if>

        <if test="pd.YXQKEYWORDS != null and pd.msg == '有效'"><!-- 关键词检索 是否在有效期-->
            and
            (ZX = 0
             and YXRQZ <![CDATA[>=]]> #{pd.YXQKEYWORDS,jdbcType=DATE})
        </if>
        <if test="pd.YXQKEYWORDS != null and pd.msg == '无效'"><!-- 关键词检索 是否在有效期-->
            and
            (ZX = 1
            or YXRQZ <![CDATA[<]]> #{pd.YXQKEYWORDS,jdbcType=DATE})
        </if>
        GROUP BY JBR  ORDER BY ZS DESC
    </select>

</mapper>
