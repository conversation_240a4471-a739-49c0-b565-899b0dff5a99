<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno2.pass.RoadMapper">

	<!--表名 -->
	<sql id="tableName">
		"PASLICENSE_ROADPART"
	</sql>

	<!--数据字典表名 -->
	<sql id="dicTableName">
		"SYS_DICTIONARIES"
	</sql>

	<!-- 字段 -->
	<sql id="Field">
		f."ID",
		f."LDMC",
		f."PX",
		f."LHY",
		f."PY",
		f."DJ",
		f."XS",
		f."SJID",
		f."ZT",
		f."JH",
		f."GXSJ"
	</sql>

	<!-- 字段用于新增 -->
	<sql id="Field2">
		"ID",
		"LDM<PERSON>",
		"PX",
		"LHY",
		"PY",
		"DJ",
		"XS",
		"SJ<PERSON>",
		"ZT",
		"JH",
		"GXSJ"
	</sql>

	<!-- 字段值 -->
	<sql id="FieldValue">
		#{ID},
		#{LDMC},
		#{PX},
		#{LHY},
		#{PY},
		#{DJ},
		#{XS},
		#{SJID},
		#{ZT},
		#{JH},
		#{GXSJ}
	</sql>

	<!-- 新增-->
	<insert id="save" parameterType="pd">
		insert into
		<include refid="tableName"></include>
		(
		<include refid="Field2"></include>
		) values (
		<include refid="FieldValue"></include>
		)
	</insert>

	<!-- 删除-->
	<delete id="delete" parameterType="pd">
		delete from
		<include refid="tableName"></include>
		where
			"ID" = #{ID}
	</delete>

	<!-- 修改 -->
	<update id="edit" parameterType="pd">
		update
		<include refid="tableName"></include>
			set
				"ID" = #{ID},
				"LDMC" = #{LDMC},
				"PX" = #{PX},
				"LHY" = #{LHY},
				"PY" = #{PY},
				"DJ" = #{DJ},
				"XS" = #{XS},
				"SJID" = #{SJID},
				"ZT" = #{ZT},
				"JH" = #{JH},
				"GXSJ" = #{GXSJ}
			where
				"ID" = #{ID}
	</update>

	<!-- 通过ID获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select
		<include refid="Field"></include>
		from
		<include refid="tableName"></include> f
		where
			f."ID" = #{ID}
	</select>
	<!-- 列表djs -->
	<select id="listDJ" parameterType="java.util.HashMap" resultType="org.fh.entity.pass.RoadVo">
		select * from PASLICENSE_ROADPART where
		XS = '1' and ZT = '0'
		<if test="ids != null"><!-- 关键词检索 -->
			and DJ in
			<foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="LDMC != null and LDMC != ''"><!-- 关键词检索 -->
			and
			(
			LDMC LIKE CONCAT(CONCAT('%', #{LDMC}),'%')
			)
		</if>

	</select>

	<!-- 列表 -->
	<select id="datalistPage" parameterType="page" resultType="pd">
		select
		<include refid="Field"></include>
		from
		<include refid="tableName"></include> f
		where 1=1
		<if test="pd.LDMC != null and pd.LDMC != ''"><!-- 关键词检索 -->
			and
				(
				LDMC LIKE CONCAT(CONCAT('%', #{pd.LDMC}),'%')
				)
		</if>
		<if test="pd.DJ != null and pd.DJ != ''"><!-- 关键词检索 -->
			and
			f.DJ = #{pd.DJ}
		</if>
		and XS = '1' and ZT = '0'
	</select>

	<!-- 列表(全部) -->
	<select id="listAll" parameterType="pd" resultType="pd">
		select
		<include refid="Field"></include>
		from
		<include refid="tableName"></include> f
	</select>

	<!-- 批量删除 -->
	<delete id="deleteAll" parameterType="String">
		delete from
		<include refid="tableName"></include>
		where
			"ID" in
		<foreach item="item" index="index" collection="array" open="(" separator="," close=")">
                 #{item}
		</foreach>
	</delete>

</mapper>
