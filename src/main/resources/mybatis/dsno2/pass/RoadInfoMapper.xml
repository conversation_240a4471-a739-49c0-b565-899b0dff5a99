<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno2.pass.RoadInfoMapper">

	<!--表名 -->
	<sql id="tableName">
		"PASLICENSE_ROADPART"
	</sql>

	<!--数据字典表名 -->
	<sql id="dicTableName">
		"SYS_DICTIONARIES"
	</sql>

	<!-- 字段 -->
	<sql id="Field">
		f."ID",
		f."LDMC",
		f."PX",
		f."LHY",
		f."PY",
		f."DJ",
		f."XS",
		f."SJID",
		f."ZT",
		f."JH",
		f."GXSJ",
		f."DLHS"
	</sql>


	<!-- 字段用于新增 -->
	<sql id="Field2">
		"ID",
		"LDMC",
		"PX",
		"LHY",
		"PY",
		"DJ",
		"XS",
		"SJID",
		"ZT",
		"JH",
		"GXSJ",
		"DLHS"
	</sql>

	<!--用于分页-->
	<sql id="Field3">
    t2."ID",
    t2."LDMC",
    t2."PX",
    t2."LHY",
    t2."PY",
    t2."DJ",
    t2."XS",
    t2."SJID",
    t2."ZT",
    t2."JH",
    t2."GXSJ",
	t2."DLHS"
	</sql>

	<!-- 字段值 -->
	<sql id="FieldValue">
		#{ID},
		#{LDMC},
		#{PX},
		#{LHY},
		#{PY},
		#{DJ},
		#{XS},
		#{SJID},
		#{ZT},
		#{JH},
		#{GXSJ},
		#{DLHS}
	</sql>

	<!-- 新增-->
	<insert id="save" parameterType="pd">
		insert into
		<include refid="tableName"></include>
		(
		<include refid="Field2"></include>
		) values (
		<include refid="FieldValue"></include>
		)
	</insert>



	<!-- 删除-->
	<delete id="delete" parameterType="pd">
		delete from
		PASLICENSE_MOULDINFO
		where
		"ID" = #{ID}
	</delete>

	<!-- 删除-->
	<delete id="Delete" parameterType="pd">
		delete from
		<include refid="tableName"></include>
		where
		"ID" = #{ID}
	</delete>

	<!-- 批量删除 -->
	<delete id="DeleteAll" parameterType="String">
		delete from
		<include refid="tableName"></include>
		where
		"ID" in
		<foreach item="item" index="index" collection="array" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 修改 -->
	<update id="edit" parameterType="pd">
		update
		<include refid="tableName"></include>
			set
				"LDMC" = #{LDMC},
				"PX" = #{PX},
				"LHY" = #{LHY},
				"PY" = #{PY},
				"DJ" = #{DJ},
				"XS" = #{XS},
				"SJID" = #{SJID},
				"ZT" = #{ZT},
				"JH" = #{JH},
				"GXSJ" = #{GXSJ},
				"DLHS" = #{DLHS}
		where
		       "ID" = #{ID}
	</update>

	<!-- 通过ID获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select
		<include refid="Field"></include>
		from
		<include refid="tableName"></include> f
		where
			f."ID" = #{ID}
	</select>

	<select id="getRoadCount" parameterType="pd" resultType="int">
		SELECT
		COUNT(1)
		FROM
		<include refid="tableName"></include> f
		WHERE 1=1

		<if test="LDMC != null and LDMC != ''"><!-- 路段名称 -->
			and
			(LDMC LIKE CONCAT(CONCAT('%', #{LDMC}),'%'))
		</if>
		<if test="DJ != null and DJ != ''"><!-- 等级 -->
			and
			DJ = #{DJ}
		</if>
		and ZT = '0' and XS = '1'
		order by DJ
	</select>

	<select id="selectPage" parameterType="page" resultType="pd">
		select <include refid="Field"></include>
		from
		<include refid="tableName"></include> f
		where 1=1
		<if test="pd.LDMC != null and pd.LDMC != ''"><!-- 关键词检索 -->
			and
			(LDMC LIKE CONCAT(CONCAT('%', #{pd.LDMC}),'%'))
		</if>
		<if test="pd.DJ != null and pd.DJ != ''"><!-- 关键词检索 -->
			and
			DJ = #{pd.DJ}
		</if>
		and ZT = '0' and XS = '1'
		order by DJ

	</select>

	<select id="datalistPage" parameterType="page" resultType="pd">

		select <include refid="Field"></include>
		 from
		 <include refid="tableName"></include> f
		  where 1=1
		<if test="pd.KEYWORDS != null and pd.KEYWORDS != ''"><!-- 关键词检索 -->
			and
			(
			(LDMC LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%'))
			or
			(PY LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%'))
			)
		</if>
		<if test="pd.DLJBKEYWORDS != null and pd.DLJBKEYWORDS != ''"><!-- 关键词检索 -->
			and

			(DJ LIKE CONCAT(CONCAT('%', #{pd.DLJBKEYWORDS}),'%'))
		</if>
		<if test="pd.ids != null and pd.ids.size()>0 "><!-- 关键词检索 -->
			and ID in
			<foreach collection="pd.ids" index="index" item="id" open="(" separator="," close=")">
				${id}
			</foreach>
		</if>
		<if test="pd.unboundIds != null and pd.unboundIds.size()>0 "><!-- 关键词检索 -->
			and ID not in
			<foreach collection="pd.unboundIds" index="index" item="id" open="(" separator="," close=")">
				${id}
			</foreach>
		</if>
		order by nlssort(LDMC,'NLS_SORT=SCHINESE_PINYIN_M')

	</select>

	<select id="rowlistPage" parameterType="page" resultType="pd">
		select
		<include refid="Field"></include>
		from
		<include refid="tableName"></include> f
		where ZT = '0' and XS = '1'
		<if test="pd.KEYWORDS != null and pd.KEYWORDS != ''"><!-- 关键词检索 -->
		and
			(
				(LDMC LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%'))
			or
				(PY LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%'))
			)
		</if>
		<if test="pd.DLJBKEYWORDS != null and pd.DLJBKEYWORDS != ''"><!-- 关键词检索 -->
			and

			(DJ LIKE CONCAT(CONCAT('%', #{pd.DLJBKEYWORDS}),'%'))
		</if>
		order by DJ
	</select>



	<!-- 列表(全部) -->
	<select id="listAll" parameterType="pd" resultType="pd">
		select
		<include refid="Field"></include>
		from
		<include refid="tableName"></include> f
	</select>

	<!--获取总记录数-->
	<select id="getCount" parameterType="pd" resultType="int">
		select count(*) from (
			select f."ID" from <include refid="tableName"></include> f where 1=1
			<if test="LDMCKEYWORDS != null and DLDMCKEYWORDS != ''"><!-- 关键词检索 -->
				and
				(LDMC LIKE CONCAT(CONCAT('%', #{LDMCKEYWORDS}),'%'))
			</if>
			<if test="DLJBKEYWORDS != null and DLJBKEYWORDS != ''"><!-- 关键词检索 -->
				and
				(DJ LIKE CONCAT(CONCAT('%', #{DLJBKEYWORDS}),'%'))
			</if>
			<if test="ids != null and ids.size()>0 "><!-- 关键词检索 -->
				and ID in
				<foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
						${id}
				</foreach>
			</if>
			<if test="unboundIds != null and unboundIds.size()>0 "><!-- 关键词检索 -->
				and ID not in
				<foreach collection="unboundIds" index="index" item="id" open="(" separator="," close=")">
					${id}
				</foreach>
			</if>
		)tmp_count
	</select>



	<!-- 批量删除 -->
	<delete id="deleteAll" parameterType="String">
		delete from
		PASLICENSE_MOULDINFO
		where
		"ID" in
		<foreach item="item" index="index" collection="array" open="(" separator="," close=")">
                 #{item}
		</foreach>
	</delete>

	<!--<select id="fndByRoad" parameterType="string" resultType="org.fh.entity.pass.Road">-->
<!--SELECT MBMC NAME,wm_concat (mo.DJ)as DJ	,wm_concat (mo.ID)as ID,  r.ID AS IGUID,wm_concat ( mo.LDMC )AS roadList-->
         <!--FROM PASLICENSE_MOULD r-->
	<!--left join  PASLICENSE_MOULDINFO m  on r.ID=m.MBID-->
	<!--left JOIN PASLICENSE_ROADPART mo 	on m.ROADPARTID=mo.ID WHERE  r.MBMC-->
	 <!--LIKE concat(concat('%',#{name}),'%')  GROUP BY MBMC,r.ID-->

	<!--</select>-->

	<select id="fndByRoad" parameterType="String" resultMap="resmap">
		SELECT MBMC NAME,	  r.ID
		FROM PASLICENSE_MOULD r
		left join  PASLICENSE_MOULDINFO m  on r.ID=m.MBID
		left JOIN PASLICENSE_ROADPART mo 	on m.ROADPARTID=mo.ID WHERE r.WEB = 1
		<if test="name != null and  name.length()>0">
			and  r.MBMC like concat(concat('%',#{name}),'%')
		</if>
			group  by MBMC, r.ID,r.PX
			ORDER  BY r.PX
	</select>
	<resultMap id="resmap" type="org.fh.entity.pass.Road">
		<id column="ID" property="id"/>
		<result property="NAME" column="NAME"/>
		<collection property="roadLists" column="NAME"
					select="org.fh.mapper.dsno2.pass.RoadInfoMapper.fandNMB">
		</collection>
	</resultMap>
	<select id="fandNMB" parameterType="String" resultType="org.fh.entity.pass.RoadList">
	SELECT mo.DJ as DJ,	mo.ID  ,mo.LDMC AS NAME
         FROM PASLICENSE_MOULD r
	left join  PASLICENSE_MOULDINFO m  on r.ID=m.MBID
	left JOIN PASLICENSE_ROADPART mo 	on m.ROADPARTID=mo.ID WHERE 1=1 and   r.MBMC   LIKE concat(concat('%',#{name}),'%')

	group  by  mo.DJ,mo.ID,mo.LDMC



</select>






</mapper>
