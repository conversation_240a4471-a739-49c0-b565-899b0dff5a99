<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno2.pass.AccountManagementMapper">

    <!--表名 -->
    <sql id="tableName">
		"PASLICENSE_ENTERPRISE"
	</sql>

    <!--数据字典表名 -->
    <sql id="dicTableName">
		"SYS_DICTIONARIES"
	</sql>

    <!-- 字段 -->
    <sql id="Field">
		f."ID",	
		f."ACCOUNT",	
		f."PASSWORD",	
		f."ENTERPRISENAME",	
		f."TJSJ",	
		f."ORGANIZATIONCODE",	
		f."OWNERNAME",	
		f."OWNERPHONE",	
		f."MANAGERNAME",	
		f."MANAGERPHONE",	
		f."ENTERPRISEADDRESS",	
		--f."ORGANIZATIONPIC",
		--f."APPLICATIONPIC",
		--f."UNITORCONTRACTPIC",
		f."SHZT",	
		f."SHSJ",	
		f."SHR",	
		f."SHYJ",	
		f."XGSJ",	
		f."ZHLX",	
		f."SWITCH",	
		f."SWITCHTIME",	
		f."TJBM",	
		f."TJR"
	</sql>

    <!-- 字段用于新增 -->
    <sql id="Field2">
		"ID",	
		"ACCOUNT",	
		"PASSWORD",	
		"ENTERPRISENAME",
		"ZHLX",
		"TJBM",	
		"TJR",
		"TJSJ",
		"SHZT"
	</sql>
    <!-- 列表(全部) -->
    <select id="findByStringId" parameterType="String" resultType="org.fh.entity.pass.PaslicenseEnterpris">
        select
        SHSJ,XGSJ
        from
        <include refid="tableName"></include>
        where
        ID = #{ID}
    </select>
    <!--用于分页查询-->
    <!-- 字段 -->
    <sql id="Field3">
    t2."ID",
    t2."ACCOUNT",
    t2."PASSWORD",
    t2."ENTERPRISENAME",
    t2."TJSJ",
    t2."ORGANIZATIONCODE",
    t2."OWNERNAME",
    t2."OWNERPHONE",
    t2."MANAGERNAME",
    t2."MANAGERPHONE",
    t2."ENTERPRISEADDRESS",
    --t2."ORGANIZATIONPIC",
    --t2."APPLICATIONPIC",
    --t2."UNITORCONTRACTPIC",
    t2."SHZT",
    t2."SHSJ",
    t2."SHR",
    t2."SHYJ",
    t2."XGSJ",
    t2."ZHLX",
    t2."SWITCH",
    t2."SWITCHTIME",
    t2."TJBM",
    t2."TJR"
</sql>
    <!-- 字段 -->
    <sql id="Field4">
		f."ID",
		f."ACCOUNT",
		f."PASSWORD",
		f."ENTERPRISENAME",
		f."TJSJ",
		f."ORGANIZATIONCODE",
		f."OWNERNAME",
		f."OWNERPHONE",
		f."MANAGERNAME",
		f."MANAGERPHONE",
		f."ENTERPRISEADDRESS",
	    f."ORGANIZATIONPIC",
		f."APPLICATIONPIC",
		f."UNITORCONTRACTPIC",
		f."SHZT",
		f."SHSJ",
		f."SHR",
		f."SHYJ",
		f."XGSJ",
		f."ZHLX",
		f."SWITCH",
		f."SWITCHTIME",
		f."TJBM",
		f."TJR"
	</sql>


    <!-- 字段值 -->
    <sql id="FieldValue">
		#{ID},	
		#{ACCOUNT},	
		#{PASSWORD},	
		#{ENTERPRISENAME},
		#{ZHLX},
		#{TJBM},	
		#{TJR},
		#{TJSJ},
		#{SHZT}
	</sql>
    <!-- 字段6 -->
    <sql id="Field6">
		f."ID",
		f."ACCOUNT",
		f."PASSWORD",
		f."ENTERPRISENAME",
		f."TJSJ",
		f."ORGANIZATIONCODE",
		f."OWNERNAME",
		f."OWNERPHONE",
		f."MANAGERNAME",
		f."MANAGERPHONE",
		f."ENTERPRISEADDRESS",
		f."ORGANIZATIONPIC",
		f."APPLICATIONPIC",
		f."UNITORCONTRACTPIC",
		f."SHZT",
		f."SHSJ",
		f."SHR",
		f."SHYJ",
		f."XGSJ",
		f."ZHLX",
		f."SWITCH",
		f."SWITCHTIME",
		f."TJBM",
		f."TJR"
	</sql>
    <!-- 新增-->
    <insert id="save" parameterType="pd">
        insert into
        <include refid="tableName"></include>
        (
        <include refid="Field2"></include>
        ) values (
        <include refid="FieldValue"></include>
        )
    </insert>

    <!-- 删除-->
    <delete id="delete" parameterType="pd">
        delete from
        <include refid="tableName"></include>
        where
        "ID" = #{ID}
    </delete>

    <!-- 修改 -->
    <update id="edit" parameterType="pd">
        update
        <include refid="tableName"></include>
        <trim prefix="SET" suffixOverrides=",">
        <if test="ENTERPRISENAME != null and ENTERPRISENAME != ''">
            "ENTERPRISENAME" = #{ENTERPRISENAME},
        </if>
        <if test="PASSWORD != null and PASSWORD != ''">
            "PASSWORD" = #{PASSWORD},
        </if>
        <if test="SHZT != null and SHZT != ''">
            "SHZT" = #{SHZT},
        </if>
            <if test="ZHLX != null and ZHLX != ''">
                "ZHLX" = #{ZHLX},
            </if>
        <if test="SHR != null and SHR != ''">
            "SHR" = #{SHR},
        </if>
        <if test="SHYJ != null and SHYJ != ''">
            "SHYJ" = #{SHYJ},
        </if>
        <if test="SHSJ != null">
            "SHSJ" = #{SHSJ},
        </if>
        <if test="XGSJ != null">
            "XGSJ" = #{XGSJ},
        </if>
        </trim >
        where
        "ID" = #{ID}
    </update>

    <update id="updatePaslicenseEnterpris" parameterType="org.fh.entity.pass.PaslicenseEnterpris">
        update
        <include refid="tableName"></include>
        <trim prefix="SET" suffixOverrides=",">
            <if test="ENTERPRISENAME != null">ENTERPRISENAME = #{ENTERPRISENAME},</if>
            <if test="ORGANIZATIONCODE != null">ORGANIZATIONCODE = #{ORGANIZATIONCODE},</if>
            <if test="OWNERNAME != null">OWNERNAME = #{OWNERNAME},</if>
            <if test="OWNERPHONE != null">OWNERPHONE = #{OWNERPHONE},</if>
            <if test="MANAGERNAME != null">MANAGERNAME = #{MANAGERNAME},</if>
            <if test="MANAGERPHONE != null">MANAGERPHONE = #{MANAGERPHONE},</if>
            <if test="ENTERPRISEADDRESS != null">ENTERPRISEADDRESS = #{ENTERPRISEADDRESS},</if>
            <if test="ORGANIZATIONPIC != null">ORGANIZATIONPIC = #{ORGANIZATIONPIC},</if>
            <if test="SHZT != null">SHZT = #{SHZT},</if>
            <if test="XGSJ != null">XGSJ = #{XGSJ},</if>
            <if test="APPLICATIONPIC != null">APPLICATIONPIC = #{APPLICATIONPIC},</if>
            <if test="UNITORCONTRACTPIC != null">UNITORCONTRACTPIC = #{UNITORCONTRACTPIC},</if>
        </trim>
        where ACCOUNT = #{ACCOUNT}
    </update>

    <update id="editPwd" parameterType="pd">
        update
        <include refid="tableName"></include>
        <trim prefix="SET" suffixOverrides=",">
            <if test="PASSWORD != null">PASSWORD = #{PASSWORD},</if>
            <if test="ENTERPRISENAME != null">ENTERPRISENAME = #{ENTERPRISENAME},</if>
            <if test="ZHLX != null">ZHLX = #{ZHLX},</if>

        </trim>
        where ACCOUNT = #{ACCOUNT}
    </update>

    <!-- 通过ID获取数据 -->
    <select id="findPass" parameterType="pd" resultType="pd">
        select
        <include refid="Field6"></include>
        from
        <include refid="tableName"></include>
        f
        where
        f."ACCOUNT" = #{ACCOUNT}
        AND
        f."PASSWORD" = #{PASSWORD}
    </select>

    <!-- 通过ID获取数据 -->
    <select id="selectId" parameterType="string" resultType="string">
        select SHZT from
        PASLICENSE_ENTERPRISE
        where
        ID = #{ID}
        and
        SHZT = 1
    </select>

    <select id="selectById" parameterType="string" resultType="string">
        select ENTERPRISENAME from
        PASLICENSE_ENTERPRISE
        where
                ID = #{ENTERPRISEID}
    </select>

    <!--查询已通过面签账号列表-->
    <select id="selectAll" parameterType="page" resultType="pd">
        select   <include refid="Field"></include> from <include refid="tableName"></include> f where 1=1

        <if test="pd.ACCOUNT != null and pd.ACCOUNT != ''"><!-- 关键词检索 账号-->
            and
            (
            ACCOUNT LIKE CONCAT(CONCAT('%', #{pd.ACCOUNT}),'%')
            )
        </if>
        <if test="pd.ENTERPRISENAME != null and pd.ENTERPRISENAME != ''"><!-- 关键词检索 企业名称-->
            and
            (
            ENTERPRISENAME LIKE CONCAT(CONCAT('%', #{pd.ENTERPRISENAME}),'%')
            )
        </if>
        <if test="pd.ZHLX != null and pd.ZHLX != ''"><!-- 关键词检索 账号类型-->
            and
            (
            ZHLX LIKE CONCAT(CONCAT('%', #{pd.ZHLX}),'%')
            )
        </if>
        <if test="pd.TJBM != null and pd.TJBM != ''"><!-- 关键词检索 添加部门-->
            and
            (
            TJBM LIKE CONCAT(CONCAT('%', #{pd.TJBM}),'%')
            )
        </if>
        <if test="pd.SHZT != null and pd.SHZT != ''">
            and
            (
            SHZT LIKE CONCAT(CONCAT('%', #{pd.SHZT}),'%')
            )
        </if>
    </select>
    <!--查询面签账号名下已通过车辆列表-->
    <select id="listPageselectCar" parameterType="page" resultType="pd">
        select
        VEHTYPE as HPZL,VEHNUM as HPHM
        from
        PASLICENSE_ENTERPRISE_VEH f
        where
        ENTERPRISEID = #{pd.ENTERPRISEID}
        <if test="pd.VEHTYPE != null and pd.VEHTYPE != ''"><!-- 关键词检索 添加部门-->
            and
            (
            VEHTYPE LIKE CONCAT(CONCAT('%', #{pd.VEHTYPE}),'%')
            )
        </if>
        <if test="pd.VEHNUM != null and pd.VEHNUM != ''">
            and
            (
            VEHNUM LIKE CONCAT(CONCAT('%', #{pd.VEHNUM}),'%')
            )
        </if>
        and
        SHZT = 1
    </select>
    <select id="carCount" parameterType="pd" resultType="int">
        select
        count(*)
        from
        PASLICENSE_ENTERPRISE_VEH f
        where SHZT = 1
        <if test="pd.ENTERPRISEID != null and pd.ENTERPRISEID != ''">
            and
            ENTERPRISEID = #{pd.ENTERPRISEID}
        </if>

        <if test="pd.VEHTYPE != null and pd.VEHTYPE != ''">
            and
            (
            VEHTYPE LIKE CONCAT(CONCAT('%', #{pd.VEHTYPE}),'%')
            )
        </if>
        <if test="pd.VEHNUM != null and pd.VEHNUM != ''">
            and
            (
            VEHNUM LIKE CONCAT(CONCAT('%', #{pd.VEHNUM}),'%')
            )
        </if>

    </select>

    <!--查询总记录数-->
    <select id="getRoadCount" parameterType="pd" resultType="int">
        select
        count(*)
        from
        <include refid="tableName"></include> f
        where
        SHZT = '1'
        <if test="ACCOUNT != null and ACCOUNT != ''"><!-- 关键词检索 账号-->
            and
            (
            ACCOUNT LIKE CONCAT(CONCAT('%', #{ACCOUNT}),'%')
            )
        </if>
        <if test="ENTERPRISENAME != null and ENTERPRISENAME != ''"><!-- 关键词检索 企业名称-->
            and
            (
            ENTERPRISENAME LIKE CONCAT(CONCAT('%', #{ENTERPRISENAME}),'%')
            )
        </if>
        <if test="ZHLX != null and ZHLX != ''"><!-- 关键词检索 账号类型-->
            and
            (
            ZHLX LIKE CONCAT(CONCAT('%', #{ZHLX}),'%')
            )
        </if>
        <if test="TJBM != null and TJBM != ''"><!-- 关键词检索 添加部门-->
            and
            (
            TJBM LIKE CONCAT(CONCAT('%', #{TJBM}),'%')
            )
        </if>
    </select>
    <select id="findById" parameterType="pd" resultType="pd">
        select
        <include refid="Field"></include>
        from
        <include refid="tableName"></include>
        f
        where
        f."ID" = #{ID}
    </select>
    <!-- 通过account获取数据 -->
    <select id="getInfoByAccount" parameterType="String" resultType="org.fh.entity.pass.PaslicenseEnterpris">
        select
        <include refid="Field6"></include>
        from
        <include refid="tableName"></include>
        f
        where
        f."ACCOUNT" = #{ACCOUNT}
    </select>
    <select id="getInfoByAccountID" parameterType="String" resultType="org.fh.entity.pass.PaslicenseEnterpris">
        select
        <include refid="Field6"></include>
        from
        <include refid="tableName"></include>
        f
        where
        f."ID" = #{ID} and f."ACCOUNT" = #{ACCOUNT}
    </select>
    <resultMap id="ImageResultMap" type="org.fh.entity.pass.AccountImageBean">
        <result column="ORGANIZATIONPIC" jdbcType="CLOB" property="ORGANIZATIONPIC"/>
        <result column="APPLICATIONPIC" jdbcType="CLOB" property="APPLICATIONPIC"/>
        <result column="UNITORCONTRACTPIC" jdbcType="CLOB" property="UNITORCONTRACTPIC"/>
    </resultMap>
    <!-- 通过ID获取acccount表中图片-->
    <select id="getImageById" parameterType="pd" resultMap="ImageResultMap">
        select
        <if test="msg==1"><!-- 组织机构代码证图片 -->
            ORGANIZATIONPIC
        </if>
        <if test="msg==2"><!-- 申请书图片 -->
            APPLICATIONPIC
        </if>
        <if test="msg==3"><!-- 单位证明 -->
            UNITORCONTRACTPIC
        </if>
        from
        <include refid="tableName"></include>
        f
        where
        f."ID" = #{ID}
    </select>

    <!-- 列表 -->
    <select id="datalistPage" parameterType="page" resultType="pd">
        select   <include refid="Field"></include> from <include refid="tableName"></include> f where 1=1
        <if test="pd.ACCOUNTKEYWORDS != null and pd.ACCOUNTKEYWORDS != ''"><!-- 关键词检索 账号 -->
            and
            (
            ACCOUNT LIKE CONCAT(CONCAT('%', #{pd.ACCOUNTKEYWORDS}),'%')
            )
        </if>
        <if test="pd.ENTERPRISENAMEKEYWORDS != null and pd.ENTERPRISENAMEKEYWORDS != ''"><!-- 关键词检索 企业名称 用户名-->
            and
            (
            ENTERPRISENAME LIKE CONCAT(CONCAT('%', #{pd.ENTERPRISENAMEKEYWORDS}),'%')
            )
        </if>
        <if test="pd.ZHLXKEYWORDS != null and pd.ZHLXKEYWORDS != ''"><!-- 关键词检索 账号类型-->
            and
            (
            ZHLX LIKE CONCAT(CONCAT('%', #{pd.ZHLXKEYWORDS}),'%')
            )
        </if>
        <if test="pd.TJRKEYWORDS != null and pd.TJRKEYWORDS != ''"><!-- 关键词检索 添加人-->
            and
            (
            TJR LIKE CONCAT(CONCAT('%', #{pd.TJRKEYWORDS}),'%')
            )
        </if>
        <if test="pd.TJBMKEYWORDS != null and pd.TJBMKEYWORDS != ''"><!-- 关键词检索 添加部门-->
            and
            (
            TJBM LIKE CONCAT(CONCAT('%', #{pd.TJBMKEYWORDS}),'%')
            )
        </if>
        <if test="pd.SHZTEYWORDS != null and pd.SHZTEYWORDS != ''"><!-- 关键词检索 审核状态-->
            and
            (
            SHZT LIKE CONCAT(CONCAT('%', #{pd.SHZTEYWORDS}),'%')
            )
        </if>
        <if test="pd.SHZT != null and pd.SHZT != ''">
            and
            (
            SHZT LIKE CONCAT(CONCAT('%', #{pd.SHZT}),'%')
            )
        </if>
    </select>
    <!-- 列表(全部) -->


    <!--获取总记录数-->
    <select id="getCount" parameterType="pd" resultType="int">
        select count(*) from
        (select  f."ID" from <include refid="tableName"></include> F where 1=1
        <if test="ACCOUNTKEYWORDS != null and ACCOUNTKEYWORDS != ''"><!-- 关键词检索 账号 -->
            and
            (
            ACCOUNT LIKE CONCAT(CONCAT('%', #{ACCOUNTKEYWORDS}),'%')
            )
        </if>
        <if test="ENTERPRISENAMEKEYWORDS != null and ENTERPRISENAMEKEYWORDS != ''"><!-- 关键词检索 企业名称 用户名-->
            and
            (
            ENTERPRISENAME LIKE CONCAT(CONCAT('%', #{ENTERPRISENAMEKEYWORDS}),'%')
            )
        </if>
        <if test="ZHLXKEYWORDS != null and ZHLXKEYWORDS != ''"><!-- 关键词检索 账号类型-->
            and
            (
            ZHLX LIKE CONCAT(CONCAT('%', #{ZHLXKEYWORDS}),'%')
            )
        </if>
        <if test="TJRKEYWORDS != null and TJRKEYWORDS != ''"><!-- 关键词检索 添加人-->
            and
            (
            TJR LIKE CONCAT(CONCAT('%', #{TJRKEYWORDS}),'%')
            )
        </if>
        <if test="TJBMKEYWORDS != null and TJBMKEYWORDS != ''"><!-- 关键词检索 添加部门-->
            and
            (
            TJBM LIKE CONCAT(CONCAT('%', #{TJBMKEYWORDS}),'%')
            )
        </if>
        <if test="SHZTEYWORDS != null and SHZTEYWORDS != ''"><!-- 关键词检索 审核状态-->
            and
            (
            SHZT LIKE CONCAT(CONCAT('%', #{SHZTEYWORDS}),'%')
            )
        </if>
        ) tmp_count
    </select>

    <!-- 批量删除 -->
    <delete id="deleteAll" parameterType="String">
        delete from
        <include refid="tableName"></include>
        where
        "ID" in
        <foreach item="item" index="index" collection="array" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <select id="fandByMQ" resultType="map">
   (SELECT ZHLX name,COUNT(ZHLX) VALUE FROM "PASLICENSE_ENTERPRISE"  GROUP BY ZHLX) ORDER BY ZHLX asc
</select>
    <!-- fh313596790qq(青苔) -->
</mapper>