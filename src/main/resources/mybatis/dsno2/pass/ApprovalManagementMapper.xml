<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno2.pass.ApprovalManagementMapper">
	
	<!--表名 -->
	<sql id="tableName">
		"PASLICENSE"
	</sql>
	
	<!--数据字典表名 -->
	<sql id="dicTableName">
		"SYS_DICTIONARIES"
	</sql>
	
	<!-- 字段 -->
	<sql id="Field">
		f."XH",	
		f."TXZLX",	
		f."TXZHM",	
		f."HPZL",	
		f."HPHM",	
		f."CLLX",	
		f."SYR",	
		f."LX",	
		f."YXRQS",	
		f."YXRQZ",	
		f."J<PERSON>",	
		f."GXSJ",	
		f."DJRQ",	
		f."SPR",	
		f."SPBJ",	
		f."SPRQ",	
		f."GFSD",	
		f."YXSJ",	
		f."YXRQ",	
		f."LDJZ",	
		f."BZ",	
		f."DYBJ",	
		f."ZT",	
		f."YLZD1",	
		f."YLZD2",	
		f."YLZD3",	
		f."YLZD4",	
		f."ZX",	
		f."JUJUE",	
		f."JUETEXT",	
		f."SPONE",	
		f."SPONEPEOPLE",	
		f."SPTWO",	
		f."SPTWOPEOPLE",	
		f."SPTHREE",	
		f."SPTHREEPEOPLE",	
		f."SPBJ2",	
		f."LSSP",	
		f."LSSPBJ",
		f."LY",
		f."YSSBSD",	
		f."LSTXZ",
		f."DLJB",	
		f."TXJX",	
		f."ZFZ",	
		f."TLPP",
		f."SFYGX",
		f."AUDITING_STATUS"
	</sql>
	
	<!-- 字段用于新增 -->
	<sql id="Field2">
		"XH",	
		"TXZLX",	
		"TXZHM",	
		"HPZL",	
		"HPHM",	
		"CLLX",	
		"SYR",	
		"LX",	
		"YXRQS",	
		"YXRQZ",	
		"JBR",	
		"GXSJ",	
		"DJRQ",	
		"SPR",	
		"SPBJ",	
		"SPRQ",	
		"GFSD",	
		"YXSJ",	
		"YXRQ",	
		"LDJZ",	
		"BZ",	
		"DYBJ",	
		"ZT",	
		"YLZD1",	
		"YLZD2",	
		"YLZD3",	
		"YLZD4",	
		"ZX",	
		"JUJUE",	
		"JUETEXT",	
		"SPONE",	
		"SPONEPEOPLE",	
		"SPTWO",	
		"SPTWOPEOPLE",	
		"SPTHREE",	
		"SPTHREEPEOPLE",	
		"SPBJ2",	
		"LSSP",	
		"LSSPBJ",	
		"YLZD5",
		"LY",	
		"YLZD6",
		"YSSBSD",	
		"LSTXZ",	
		"TXSBBH",	
		"DLJB",	
		"TXJX",	
		"ZFZ",	
		"TLPP",	
		"SBSD",	
		"SFYGX",
		"AUDITING_STATUS"
	</sql>
	<!-- 分页查询 -->
	<sql id="Field3">
    t2."XH",
    t2."TXZLX",
    t2."TXZHM",
    t2."HPZL",
    t2."HPHM",
    t2."CLLX",
    t2."SYR",
    t2."LX",
    t2."YXRQS",
    t2."YXRQZ",
    t2."JBR",
    t2."GXSJ",
    t2."DJRQ",
    t2."SPR",
    t2."SPBJ",
    t2."SPRQ",
    t2."GFSD",
    t2."YXSJ",
    t2."YXRQ",
    t2."LDJZ",
    t2."BZ",
    t2."DYBJ",
    t2."ZT",
    t2."YLZD1",
    t2."YLZD2",
    t2."YLZD3",
    t2."YLZD4",
    t2."ZX",
    t2."JUJUE",
    t2."JUETEXT",
    t2."SPONE",
    t2."SPONEPEOPLE",
    t2."SPTWO",
    t2."SPTWOPEOPLE",
    t2."SPTHREE",
    t2."SPTHREEPEOPLE",
    t2."SPBJ2",
    t2."LSSP",
    t2."LSSPBJ",

    t2."LY",

    t2."YSSBSD",
    t2."LSTXZ",
    t2."TXSBBH",
    t2."DLJB",
    t2."TXJX",
    t2."ZFZ",
    t2."TLPP",
    t2."SBSD",
    t2."SFYGX",
    t2."AUDITING_STATUS"
</sql>
	
	<!-- 字段值 -->
	<sql id="FieldValue">
		#{XH},	
		#{TXZLX},	
		#{TXZHM},	
		#{HPZL},	
		#{HPHM},	
		#{CLLX},	
		#{SYR},	
		#{LX},	
		#{YXRQS},	
		#{YXRQZ},	
		#{JBR},	
		#{GXSJ},	
		#{DJRQ},	
		#{SPR},	
		#{SPBJ},	
		#{SPRQ},	
		#{GFSD},	
		#{YXSJ},	
		#{YXRQ},	
		#{LDJZ},	
		#{BZ},	
		#{DYBJ},	
		#{ZT},	
		#{YLZD1},	
		#{YLZD2},	
		#{YLZD3},	
		#{YLZD4},	
		#{ZX},	
		#{JUJUE},	
		#{JUETEXT},	
		#{SPONE},	
		#{SPONEPEOPLE},	
		#{SPTWO},	
		#{SPTWOPEOPLE},	
		#{SPTHREE},	
		#{SPTHREEPEOPLE},	
		#{SPBJ2},	
		#{LSSP},	
		#{LSSPBJ},	
		#{YLZD5},	
		#{LY},	
		#{YLZD6},	
		#{YSSBSD},	
		#{LSTXZ},	
		#{TXSBBH},	
		#{DLJB},	
		#{TXJX},	
		#{ZFZ},	
		#{TLPP},	
		#{SBSD},	
		#{SFYGX},
		#{AUDITING_STATUS}
	</sql>
	
	<!-- 新增-->
	<insert id="save" parameterType="pd">
		insert into
		<include refid="tableName"></include>
		(
		<include refid="Field2"></include>
		) values (
		<include refid="FieldValue"></include>
		)
	</insert>
	
	<!-- 删除-->
	<delete id="delete" parameterType="pd">
		delete from
		<include refid="tableName"></include>
		where
		"XH" = #{XH}
	</delete>
	
	<!-- 修改 -->
	<update id="edit" parameterType="pd">
		update
		<include refid="tableName"></include>
			set
				"SHSJ" = #{SHSJ},
				"AUDITING_STATUS"=#{AUDITING_STATUS}

			where
				"XH" = #{XH}
	</update>
	
	<!-- 通过ID获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include> f
		where 
			f."XH" = #{XH}
	</select>
	
	<!-- 列表 -->
	<select id="datalistPage" parameterType="page" resultType="pd">
		select
		<include refid="Field"></include>
		from
		<include refid="tableName"></include> f
		where 1=1
		<if test="pd.HPZLKEYWORDS != null and pd.HPZLKEYWORDS != ''"><!-- 关键词检索 号牌种类 -->
			and
			(
			HPZL LIKE CONCAT(CONCAT('%', #{pd.HPZLKEYWORDS}),'%')
			)
		</if>
		<if test="pd.HPHMKEYWORDS != null and pd.HPHMKEYWORDS != ''"><!-- 关键词检索 号牌号码-->
			and
			(
			HPHM LIKE CONCAT(CONCAT('%', #{pd.HPHMKEYWORDS}),'%')
			)
		</if>
		<if test="pd.JBRKEYWORDS != null and pd.JBRKEYWORDS != ''"><!-- 关键词检索 经办人-->
			and
			(
			JBR LIKE CONCAT(CONCAT('%', #{pd.JBRKEYWORDS}),'%')
			)
		</if>
		<if test="pd.JDCSYRKEYWORDS != null and pd.JDCSYRKEYWORDS != ''"><!-- 关键词检索 机动车所有人-->
			and
			(
			SYR LIKE CONCAT(CONCAT('%', #{pd.JDCSYRKEYWORDS}),'%')
			)
		</if>
		<if test="pd.SHZTKEYWORDS != null and pd.SHZTKEYWORDS != ''"><!-- 关键词检索 审核状态-->
			and
			(
			AUDITING_STATUS LIKE CONCAT(CONCAT('%', #{pd.SHZTKEYWORDS}),'%')
			)
		</if>
		<if test="pd.STARTDJRQKEYWORDS != null"><!-- 关键词检索 起始登记日期-->
			and to_char(DJRQ,'yyyy-MM-dd') <![CDATA[ >= ]]> #{pd.STARTDJRQKEYWORDS}
		</if>
		<if test="pd.ENDDJRQKEYWORDS != null"><!-- 关键词检索 终止登记日期-->
			and to_char(DJRQ,'yyyy-MM-dd') <![CDATA[ <= ]]> #{pd.ENDDJRQKEYWORDS}
		</if>
		<if test="pd.CLLXKEYWORDS != null and pd.CLLXKEYWORDS != ''"><!-- 关键词检索 车辆类型-->
			and
			(
			CLLX LIKE CONCAT(CONCAT('%', #{pd.CLLXKEYWORDS}),'%')
			)
		</if>
		order by DJRQ desc

	</select>
	
	<!-- 列表(全部) -->
	<select id="listAll" parameterType="pd" resultType="pd">
		select
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include> f
	</select>

	<!--获取总记录数-->
	<select id="getCount" parameterType="pd" resultType="int">
	select count(*) from(
	select f."XH" from <include refid="tableName"></include> f where 1=1
		<if test="HPZLKEYWORDS != null and HPZLKEYWORDS != ''"><!-- 关键词检索 号牌种类 -->
			and
			(
			HPZL LIKE CONCAT(CONCAT('%', #{HPZLKEYWORDS}),'%')
			)
		</if>
		<if test="HPHMKEYWORDS != null and HPHMKEYWORDS != ''"><!-- 关键词检索 号牌号码-->
			and
			(
			HPHM LIKE CONCAT(CONCAT('%', #{HPHMKEYWORDS}),'%')
			)
		</if>
		<if test="JBRKEYWORDS != null and JBRKEYWORDS != ''"><!-- 关键词检索 经办人-->
			and
			(
			JBR LIKE CONCAT(CONCAT('%', #{JBRKEYWORDS}),'%')
			)
		</if>
		<if test="JDCSYRKEYWORDS != null and JDCSYRKEYWORDS != ''"><!-- 关键词检索 机动车所有人-->
			and
			(
			SYR LIKE CONCAT(CONCAT('%', #{JDCSYRKEYWORDS}),'%')
			)
		</if>
		<if test="SHZTKEYWORDS != null and SHZTKEYWORDS != ''"><!-- 关键词检索 审核状态-->
			and
			(
			AUDITING_STATUS LIKE CONCAT(CONCAT('%', #{SHZTKEYWORDS}),'%')
			)
		</if>
		<if test="STARTDJRQKEYWORDS != null"><!-- 关键词检索 起始登记日期-->
			and DJRQ <![CDATA[>=]]> #{STARTDJRQKEYWORDS,jdbcType=DATE}
		</if>
		<if test="ENDDJRQKEYWORDS != null"><!-- 关键词检索 终止登记日期-->
			and DJRQ <![CDATA[ < ]]> #{ENDDJRQKEYWORDS,jdbcType=DATE}
		</if>
		<if test="YXQKEYWORDS != null and msg == '有效'"><!-- 关键词检索 是否在有效期-->
			and YXRQZ <![CDATA[>=]]> #{YXQKEYWORDS,jdbcType=DATE}
		</if>
		<if test="YXQKEYWORDS != null and msg == '无效'"><!-- 关键词检索 是否在有效期-->
			and YXRQZ <![CDATA[<]]> #{YXQKEYWORDS,jdbcType=DATE}
		</if>

	)tmp_count
	</select>
	<!-- 批量删除 -->
	<delete id="deleteAll" parameterType="String">
		delete from
		<include refid="tableName"></include>
		where
		"XH" in
		<foreach item="item" index="index" collection="array" open="(" separator="," close=")">
                 #{item}
		</foreach>
	</delete>

</mapper>