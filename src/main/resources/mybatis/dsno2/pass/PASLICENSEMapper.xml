<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno2.pass.PASLICENSEMapper">
    <resultMap id="BaseResultMap" type="org.fh.entity.pass.PASLICENSE">
        <id column="XH" jdbcType="VARCHAR" property="xh"/>
        <result column="TXZLX" jdbcType="VARCHAR" property="txzlx"/>
        <result column="TXZHM" jdbcType="VARCHAR" property="txzhm"/>
        <result column="HPZL" jdbcType="CHAR" property="hpzl"/>
        <result column="HPHM" jdbcType="VARCHAR" property="hphm"/>
        <result column="CLLX" jdbcType="CHAR" property="cllx"/>
        <result column="SYR" jdbcType="VARCHAR" property="syr"/>
        <result column="LX" jdbcType="VARCHAR" property="lx"/>
        <result column="YXRQS" jdbcType="TIMESTAMP" property="yxrqs"/>
        <result column="YXRQZ" jdbcType="TIMESTAMP" property="yxrqz"/>
        <result column="JBR" jdbcType="VARCHAR" property="jbr"/>
        <result column="GXSJ" jdbcType="TIMESTAMP" property="gxsj"/>
        <result column="DJRQ" jdbcType="TIMESTAMP" property="djrq"/>
        <result column="SPR" jdbcType="VARCHAR" property="spr"/>
        <result column="SPBJ" jdbcType="CHAR" property="spbj"/>
        <result column="SPRQ" jdbcType="TIMESTAMP" property="sprq"/>
        <result column="GFSD" jdbcType="VARCHAR" property="gfsd"/>
        <result column="YXSJ" jdbcType="VARCHAR" property="yxsj"/>
        <result column="YXRQ" jdbcType="VARCHAR" property="yxrq"/>
        <result column="LDJZ" jdbcType="VARCHAR" property="ldjz"/>
        <result column="BZ" jdbcType="VARCHAR" property="bz"/>
        <result column="DYBJ" jdbcType="CHAR" property="dybj"/>
        <result column="ZT" jdbcType="CHAR" property="zt"/>
        <result column="YLZD1" jdbcType="VARCHAR" property="ylzd1"/>
        <result column="YLZD2" jdbcType="VARCHAR" property="ylzd2"/>
        <result column="YLZD3" jdbcType="VARCHAR" property="ylzd3"/>
        <result column="YLZD4" jdbcType="VARCHAR" property="ylzd4"/>
        <result column="ZX" jdbcType="VARCHAR" property="zx"/>
        <result column="JUJUE" jdbcType="VARCHAR" property="jujue"/>
        <result column="JUETEXT" jdbcType="VARCHAR" property="juetext"/>
        <result column="SPONE" jdbcType="VARCHAR" property="spone"/>
        <result column="SPONEPEOPLE" jdbcType="VARCHAR" property="sponepeople"/>
        <result column="SPTWO" jdbcType="VARCHAR" property="sptwo"/>
        <result column="SPTWOPEOPLE" jdbcType="VARCHAR" property="sptwopeople"/>
        <result column="SPTHREE" jdbcType="VARCHAR" property="spthree"/>
        <result column="SPTHREEPEOPLE" jdbcType="VARCHAR" property="spthreepeople"/>
        <result column="SPBJ2" jdbcType="VARCHAR" property="spbj2"/>
        <result column="LSSP" jdbcType="VARCHAR" property="lssp"/>
        <result column="LSSPBJ" jdbcType="VARCHAR" property="lsspbj"/>
        <result column="LY" jdbcType="VARCHAR" property="ly"/>
        <result column="YSSBSD" jdbcType="VARCHAR" property="yssbsd"/>
        <result column="LSTXZ" jdbcType="VARCHAR" property="lstxz"/>
        <result column="DLJB" jdbcType="VARCHAR" property="dljb"/>
        <result column="TXJX" jdbcType="VARCHAR" property="txjx"/>
        <result column="ZFZ" jdbcType="VARCHAR" property="zfz"/>
        <result column="TLPP" jdbcType="VARCHAR" property="tlpp"/>
        <result column="SFYGX" jdbcType="VARCHAR" property="sfygx"/>
        <result column="AUDITING_STATUS" jdbcType="VARCHAR" property="auditing_status"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="org.fh.entity.pass.PASLICENSEWithBLOBs">
        <result column="YLZD5" jdbcType="CLOB" property="ylzd5"/>
        <result column="YLZD6" jdbcType="CLOB" property="ylzd6"/>
        <result column="TXSBBH" jdbcType="CLOB" property="txsbbh"/>
        <result column="SBSD" jdbcType="CLOB" property="sbsd"/>
    </resultMap>
    <sql id="Base_Column_List">
    XH, TXZLX, TXZHM, HPZL, HPHM, CLLX, SYR, LX, YXRQS, YXRQZ, JBR, GXSJ, DJRQ, SPR,
    SPBJ, SPRQ, GFSD, YXSJ, YXRQ, LDJZ, BZ, DYBJ, ZT, YLZD1, YLZD2, YLZD3, YLZD4, ZX,
    JUJUE, JUETEXT, SPONE, SPONEPEOPLE, SPTWO, SPTWOPEOPLE, SPTHREE, SPTHREEPEOPLE, SPBJ2,
    LSSP, LSSPBJ, LY, YSSBSD, LSTXZ, DLJB, TXJX, ZFZ, TLPP, SFYGX,AUDITING_STATUS,YLZD5, YLZD6, TXSBBH, SBSD
    </sql>
    <sql id="AllList">
    XH, TXZLX, TXZHM, HPZL, HPHM, CLLX, SYR, YXRQS, YXRQZ, JBR, GXSJ, DJRQ, SPR,
    SPRQ,YXRQ, LDJZ, BZ, DYBJ,YLZD1, ZX,YSSBSD, LSTXZ, DLJB, TXJX, ZFZ,YLZD5,YLZD6,TXSBBH
    </sql>
    <!-- 字段 -->
    <sql id="Field">
		f.XH,
		f.TXZLX,
		f.TXZHM,
		f.HPZL,
		f.HPHM,
		f.CLLX,
		f.SYR,
		f.LX,
		f.YXRQS,
		f.YXRQZ,
		f.JBR,
		f.GXSJ,
		f.DJRQ,
		f.SPR,
		f.SPBJ,
		f.SPRQ,
		f.GFSD,
		f.YXSJ,
		f.YXRQ,
		f.LDJZ,
		f.BZ,
		f.DYBJ,
		f.ZT,
		f.YLZD1,
		f.YLZD2,
		f.YLZD3,
		f.YLZD4,
		f.ZX,
		f.JUJUE,
		f.JUETEXT,
		f.SPONE,
		f.SPONEPEOPLE,
		f.SPTWO,
		f.SPTWOPEOPLE,
		f.SPTHREE,
		f.SPTHREEPEOPLE,
		f.SPBJ2,
		f.LSSP,
		f.LSSPBJ,
		f.LY,
		f.YSSBSD,
		f.LSTXZ,
		f.DLJB,
		f.TXJX,
		f.ZFZ,
		f.TLPP,
		f.SFYGX,
		f.AUDITING_STATUS
	</sql>
    <sql id="List">
    XH,TXZLX,TXZHM,HPZL,HPHM,CLLX,SYR,YXRQS,YXRQZ,YXRQ,LDJZ,BZ,YLZD5,YLZD6,YSSBSD,LSTXZ,TXSBBH,DLJB,TXJX,ZFZ
  </sql>
    <!--获取电子通行证详细-->
    <select id="selectListById" parameterType="java.lang.String" resultType="org.fh.entity.pass.PASLICENSEWithBLOBs">
    select
    *
    from PASLICENSE_VEH
    WHERE XH = #{XH,jdbcType=VARCHAR}
  </select>
    <!-- 查询有效通行证列表 -->
    <select id="selectListIdCar" parameterType="org.fh.entity.pass.PASLICENSEWithBLOBs" resultType="org.fh.entity.pass.PASLICENSEWithBLOBs">
        select
        XH,HPZL,HPHM,YXRQS,YXRQZ,YSSBSD,DLJB,YLZD3,YLZD6
        from
        PASLICENSE_VEH
        where
        HPHM = #{hphm,jdbcType=VARCHAR}
        <if test="hpzl != null and hpzl != ''"><!-- 关键词检索 号牌种类 -->
            and
            HPZL = #{hpzl,jdbcType=VARCHAR}
        </if>
        and AUDITING_STATUS = 1
        and ZX = 0
        and YXRQZ > SYSDATE
    </select>
    <select id="selectTest" parameterType="org.fh.entity.pass.PASLICENSEWithBLOBs" resultType="org.fh.entity.pass.PASLICENSEWithBLOBs">
        select
        *
        from
        PASLICENSE
        where AUDITING_STATUS = 1
        and ZX = 0
        and YXRQZ > SYSDATE
    </select>
    <update id="updateMap" parameterType="org.fh.entity.pass.PASLICENSEWithBLOBs">
        update PASLICENSE set
                YXRQZ = #{yxrqz,jdbcType=TIMESTAMP}
        where XH = #{xh,jdbcType=VARCHAR}
    </update>
    <!--警务通查询接口-->
    <select id="selectByIdHphm" parameterType="java.lang.String" resultType="org.fh.entity.pass.PASLICENSEWithBLOBs">
        select
        <include refid="AllList"/>
        from  PASLICENSE_VEH
        where AUDITING_STATUS = 1
        <if test="hpzl != null and hpzl != ''"><!-- 关键词检索 号牌种类-->
            and
            (
            HPZL LIKE CONCAT(CONCAT('%', #{hpzl,jdbcType=VARCHAR}),'%')
            )
        </if>
        <if test="hphm != null and hphm != ''"><!-- 关键词检索 号牌号码-->
            and
            (
            HPHM LIKE CONCAT(CONCAT('%', #{hphm,jdbcType=VARCHAR}),'%')
            )
        </if>
        <if test="txzhm != null and txzhm != ''"><!-- 关键词检索 通行证号码-->
            and
            (
            TXZHM LIKE CONCAT(CONCAT('%', #{txzhm,jdbcType=VARCHAR}),'%')
            )
        </if>
        and ZX = 0
        and YXRQZ > SYSDATE
    </select>
    <!--條件序號查詢-->
    <select id="select" parameterType="java.lang.String" resultType="org.fh.entity.pass.PASLICENSEWithBLOBs">
        select
        <include refid="List"/>
        from PASLICENSE
        where XH = #{xh,jdbcType=VARCHAR}
    </select>
    <update id="update" parameterType="org.fh.entity.pass.PASLICENSEWithBLOBs">
    update PASLICENSE set
    YXRQS = #{yxrqs,jdbcType=TIMESTAMP} ,
    YXRQZ = #{yxrqz,jdbcType=TIMESTAMP},
    YXRQ = #{yxrq,jdbcType=VARCHAR},
    ZX = #{zx,jdbcType=VARCHAR},
    GXSJ = #{gxsj,jdbcType=TIMESTAMP}
    where XH = #{xh,jdbcType=VARCHAR}
    </update>

    <!--查詢最新一條通行證-->
    <select id="selectById" parameterType="java.lang.String" resultType="org.fh.entity.pass.PASLICENSEWithBLOBs">
    SELECT t1.*,ROWNUM FROM
    (SELECT * FROM PASLICENSE WHERE AUDITING_STATUS = 1 ORDER BY GXSJ DESC) t1,
    PASLICENSE_ENTERPRISE_VEH t2 WHERE t2.VEHNUM = #{hphm,jdbcType=VARCHAR} and t2. VEHTYPE = #{hpzl,jdbcType=VARCHAR}
    <if test="MEMBERIDCARD != null and MEMBERIDCARD != ''">
      and  (t2.MEMBERIDCARD = #{MEMBERIDCARD,jdbcType=VARCHAR} OR t2.ACTIDCARD = #{MEMBERIDCARD,jdbcType=VARCHAR})
    </if>
    <if test="ENTERPRISEID != null and ENTERPRISEID != ''">
        and  t2.ENTERPRISEID = #{ENTERPRISEID}
    </if>
    and t1.HPZL = t2. VEHTYPE and t1.HPHM = t2.VEHNUM AND ROWNUM =1
  </select>

    <select id="selectXh" parameterType="java.lang.String" resultType="java.lang.String">
    select
    t1.XH,ROWNUM
    from (SELECT * FROM PASLICENSE t ORDER BY DJRQ DESC) t1
    WHERE rownum = 1
    and HPZL = #{hpzl,jdbcType=VARCHAR}
    and HPHM = #{hphm,jdbcType=VARCHAR}
    and YXRQZ > SYSDATE and ZX = 0
    and t1.AUDITING_STATUS = 1
  </select>
    <insert id="insertList" parameterType="org.fh.entity.pass.PASLICENSEWithBLOBs">
        INSERT ALL
        <foreach collection="list" item="TXZ" index="index">
            into
            PASLICENSE(XH,TXZHM,TXZLX,HPZL,HPHM,CLLX,SYR,DLJB,YLZD3,GXSJ,DJRQ,YXRQS,YXRQZ,YXRQ,YSSBSD,JBR,LSTXZ,AUDITING_STATUS,YLZD5,YLZD6,TXSBBH)
            values
            (#{TXZ.xh,jdbcType=VARCHAR},
            #{TXZ.txzhm,jdbcType=VARCHAR},
            #{TXZ.txzlx,jdbcType=VARCHAR},
            #{TXZ.hpzl,jdbcType=VARCHAR},
            #{TXZ.hphm,jdbcType=VARCHAR},
            #{TXZ.cllx,jdbcType=VARCHAR},
            #{TXZ.syr,jdbcType=VARCHAR},
            #{TXZ.dljb,jdbcType=VARCHAR},
            #{TXZ.ylzd3,jdbcType=VARCHAR},
            #{TXZ.gxsj},
            #{TXZ.djrq},
            #{TXZ.yxrqs},
            #{TXZ.yxrqz},
            #{TXZ.yxrq,jdbcType=VARCHAR},
            #{TXZ.yssbsd,jdbcType=VARCHAR},
            #{TXZ.jbr,jdbcType=VARCHAR},
            #{TXZ.lstxz,jdbcType=VARCHAR},
            #{TXZ.auditing_status,jdbcType=VARCHAR},
            #{TXZ.ylzd5,jdbcType=CLOB},
            #{TXZ.ylzd6,jdbcType=CLOB},
            #{TXZ.txsbbh,jdbcType=CLOB})
        </foreach>
        SELECT 1 FROM DUAL
    </insert>

    <select id="trafficStatus" parameterType="string" resultType="map">
       SELECT LSTXZ
       FROM  (SELECT * FROM PASLICENSE  ORDER BY LSTXZ DESC)
       PASLICENSE
       WHERE hpzl=#{hpzl}
       and hphm =#{hphm}
       and lstxz=#{txzlx}
       and YXRQZ BETWEEN SYSDATE AND SYSDATE + 20
       and AUDITING_STATUS=1
       and rownum=1
    </select>

    <select id="trafficStatus1" parameterType="string" resultType="Integer">
      SELECT  count(hphm) as zs
	  FROM PASLICENSE
      WHERE hpzl=#{hpzl}
	  and hphm =#{hphm}
      and lstxz=#{txzlx}
      and YXRQS BETWEEN SYSDATE AND SYSDATE + 20
      and AUDITING_STATUS=1
      GROUP BY hphm
    </select>

    <update id="updateZx" parameterType="org.fh.entity.pass.PASLICENSEWithBLOBs">
    update PASLICENSE set
    ZX = 1,
    GXSJ = #{gxsj,jdbcType=TIMESTAMP}
    where HPZL = #{hpzl,jdbcType=VARCHAR}
    and HPHM = #{hphm,jdbcType=VARCHAR}
    and AUDITING_STATUS=1
    and ZX = 0
    and YXRQZ > SYSDATE
    </update>

    <insert id="insert" parameterType="org.fh.entity.pass.PASLICENSEWithBLOBs">
        insert into PASLICENSE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="xh != null">
                XH,
            </if>
            <if test="txzlx != null">
                TXZLX,
            </if>
            <if test="txzhm != null">
                TXZHM,
            </if>
            <if test="hpzl != null">
                HPZL,
            </if>
            <if test="hphm != null">
                HPHM,
            </if>
            <if test="cllx != null">
                CLLX,
            </if>
            <if test="syr != null">
                SYR,
            </if>
            <if test="lx != null">
                LX,
            </if>
            <if test="yxrqs != null">
                YXRQS,
            </if>
            <if test="yxrqz != null">
                YXRQZ,
            </if>
            <if test="jbr != null">
                JBR,
            </if>
            <if test="gxsj != null">
                GXSJ,
            </if>
            <if test="djrq != null">
                DJRQ,
            </if>
            <if test="spr != null">
                SPR,
            </if>
            <if test="spbj != null">
                SPBJ,
            </if>
            <if test="sprq != null">
                SPRQ,
            </if>
            <if test="gfsd != null">
                GFSD,
            </if>
            <if test="yxsj != null">
                YXSJ,
            </if>
            <if test="yxrq != null">
                YXRQ,
            </if>
            <if test="ldjz != null">
                LDJZ,
            </if>
            <if test="bz != null">
                BZ,
            </if>
            <if test="dybj != null">
                DYBJ,
            </if>
            <if test="zt != null">
                ZT,
            </if>
            <if test="ylzd1 != null">
                YLZD1,
            </if>
            <if test="ylzd2 != null">
                YLZD2,
            </if>
            <if test="ylzd3 != null">
                YLZD3,
            </if>
            <if test="ylzd4 != null">
                YLZD4,
            </if>
            <if test="zx != null">
                ZX,
            </if>
            <if test="jujue != null">
                JUJUE,
            </if>
            <if test="juetext != null">
                JUETEXT,
            </if>
            <if test="spone != null">
                SPONE,
            </if>
            <if test="sponepeople != null">
                SPONEPEOPLE,
            </if>
            <if test="sptwo != null">
                SPTWO,
            </if>
            <if test="sptwopeople != null">
                SPTWOPEOPLE,
            </if>
            <if test="spthree != null">
                SPTHREE,
            </if>
            <if test="spthreepeople != null">
                SPTHREEPEOPLE,
            </if>
            <if test="spbj2 != null">
                SPBJ2,
            </if>
            <if test="lssp != null">
                LSSP,
            </if>
            <if test="lsspbj != null">
                LSSPBJ,
            </if>
            <if test="ly != null">
                LY,
            </if>
            <if test="yssbsd != null">
                YSSBSD,
            </if>
            <if test="lstxz != null">
                LSTXZ,
            </if>
            <if test="dljb != null">
                DLJB,
            </if>
            <if test="txjx != null">
                TXJX,
            </if>
            <if test="zfz != null">
                ZFZ,
            </if>
            <if test="tlpp != null">
                TLPP,
            </if>
            <if test="sfygx != null">
                SFYGX,
            </if>
            <if test="ylzd5 != null">
                YLZD5,
            </if>
            <if test="ylzd6 != null">
                YLZD6,
            </if>
            <if test="txsbbh != null">
                TXSBBH,
            </if>
            <if test="sbsd != null">
                SBSD,
            </if>
            <if test="auditing_status != null">
                AUDITING_STATUS,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="xh != null">
                #{xh,jdbcType=VARCHAR},
            </if>
            <if test="txzlx != null">
                #{txzlx,jdbcType=VARCHAR},
            </if>
            <if test="txzhm != null">
                #{txzhm,jdbcType=VARCHAR},
            </if>
            <if test="hpzl != null">
                #{hpzl,jdbcType=CHAR},
            </if>
            <if test="hphm != null">
                #{hphm,jdbcType=VARCHAR},
            </if>
            <if test="cllx != null">
                #{cllx,jdbcType=CHAR},
            </if>
            <if test="syr != null">
                #{syr,jdbcType=VARCHAR},
            </if>
            <if test="lx != null">
                #{lx,jdbcType=VARCHAR},
            </if>
            <if test="yxrqs != null">
                #{yxrqs,jdbcType=TIMESTAMP},
            </if>
            <if test="yxrqz != null">
                #{yxrqz,jdbcType=TIMESTAMP},
            </if>
            <if test="jbr != null">
                #{jbr,jdbcType=VARCHAR},
            </if>
            <if test="gxsj != null">
                #{gxsj,jdbcType=TIMESTAMP},
            </if>
            <if test="djrq != null">
                #{djrq,jdbcType=TIMESTAMP},
            </if>
            <if test="spr != null">
                #{spr,jdbcType=VARCHAR},
            </if>
            <if test="spbj != null">
                #{spbj,jdbcType=CHAR},
            </if>
            <if test="sprq != null">
                #{sprq,jdbcType=TIMESTAMP},
            </if>
            <if test="gfsd != null">
                #{gfsd,jdbcType=VARCHAR},
            </if>
            <if test="yxsj != null">
                #{yxsj,jdbcType=VARCHAR},
            </if>
            <if test="yxrq != null">
                #{yxrq,jdbcType=VARCHAR},
            </if>
            <if test="ldjz != null">
                #{ldjz,jdbcType=VARCHAR},
            </if>
            <if test="bz != null">
                #{bz,jdbcType=VARCHAR},
            </if>
            <if test="dybj != null">
                #{dybj,jdbcType=CHAR},
            </if>
            <if test="zt != null">
                #{zt,jdbcType=CHAR},
            </if>
            <if test="ylzd1 != null">
                #{ylzd1,jdbcType=VARCHAR},
            </if>
            <if test="ylzd2 != null">
                #{ylzd2,jdbcType=VARCHAR},
            </if>
            <if test="ylzd3 != null">
                #{ylzd3,jdbcType=VARCHAR},
            </if>
            <if test="ylzd4 != null">
                #{ylzd4,jdbcType=VARCHAR},
            </if>
            <if test="zx != null">
                #{zx,jdbcType=VARCHAR},
            </if>
            <if test="jujue != null">
                #{jujue,jdbcType=VARCHAR},
            </if>
            <if test="juetext != null">
                #{juetext,jdbcType=VARCHAR},
            </if>
            <if test="spone != null">
                #{spone,jdbcType=VARCHAR},
            </if>
            <if test="sponepeople != null">
                #{sponepeople,jdbcType=VARCHAR},
            </if>
            <if test="sptwo != null">
                #{sptwo,jdbcType=VARCHAR},
            </if>
            <if test="sptwopeople != null">
                #{sptwopeople,jdbcType=VARCHAR},
            </if>
            <if test="spthree != null">
                #{spthree,jdbcType=VARCHAR},
            </if>
            <if test="spthreepeople != null">
                #{spthreepeople,jdbcType=VARCHAR},
            </if>
            <if test="spbj2 != null">
                #{spbj2,jdbcType=VARCHAR},
            </if>
            <if test="lssp != null">
                #{lssp,jdbcType=VARCHAR},
            </if>
            <if test="lsspbj != null">
                #{lsspbj,jdbcType=VARCHAR},
            </if>
            <if test="ly != null">
                #{ly,jdbcType=VARCHAR},
            </if>
            <if test="yssbsd != null">
                #{yssbsd,jdbcType=VARCHAR},
            </if>
            <if test="lstxz != null">
                #{lstxz,jdbcType=VARCHAR},
            </if>
            <if test="dljb != null">
                #{dljb,jdbcType=VARCHAR},
            </if>
            <if test="txjx != null">
                #{txjx,jdbcType=VARCHAR},
            </if>
            <if test="zfz != null">
                #{zfz,jdbcType=VARCHAR},
            </if>
            <if test="tlpp != null">
                #{tlpp,jdbcType=VARCHAR},
            </if>
            <if test="sfygx != null">
                #{sfygx,jdbcType=VARCHAR},
            </if>
            <if test="ylzd5 != null">
                #{ylzd5,jdbcType=CLOB},
            </if>
            <if test="ylzd6 != null">
                #{ylzd6,jdbcType=CLOB},
            </if>
            <if test="txsbbh != null">
                #{txsbbh,jdbcType=CLOB},
            </if>
            <if test="sbsd != null">
                #{sbsd,jdbcType=CLOB},
            </if>
            <if test="auditing_status != null">
                #{auditing_status,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <!--  <update id="update_txz" parameterType="org.fh.entity.pass.PASLICENSEWithBLOBs">
          update PASLICENSE
          set
          <if test="AUDITING_STATUS != null and AUDITING_STATUS != ''">
              AUDITING_STATUS,#{AUDITING_STATUS ,jdbcType=VARCHAR},
          </if>
          <if test="TXZLX != null and TXZLX != ''">
              TXZLX,#{TXZLX,jdbcType=VARCHAR},
          </if>
          <if test="HPZL != null and HPZL != ''">
              HPZL,#{HPZL,jdbcType=VARCHAR},
          </if>
          <if test="HPHM != null and HPHM != ''">
              HPHM,#{HPHM,jdbcType=VARCHAR},
          </if>

          <if test="CLLX != null and CLLX != ''">
              CLLX,#{CLLX,jdbcType=VARCHAR},
          </if>
          <if test="SYR != null and SYR != ''">
              SYR,#{SYR,jdbcType=VARCHAR},
          </if>
          <if test="YLZD5 != null and YLZD5 != ''">
              YLZD5,#{YLZD5,jdbcType=CLOB},
          </if>
          <if test="YLZD6 != null and YLZD6 != ''">
              YLZD6,#{YLZD6,jdbcType=CLOB},
          </if>
          <if test="YXRQS != null and YXRQS != ''">
              YXRQS,#{YXRQS,jdbcType=TIMESTAMP},
          </if>
          <if test="YXRQZ != null and YXRQZ != ''">
              YXRQZ,#{YXRQZ,jdbcType=TIMESTAMP},
          </if>
          <if test="YXRQ != null and YXRQ != ''">
              YXRQ,#{YXRQZ,jdbcType=VARCHAR},
          </if>
          <if test="dljb != null and dljb != ''">
              dljb,#{YXRQZ,jdbcType=VARCHAR},
          </if>
          <if test="TXJX != null and TXJX != ''">
              TXJX,#{YXRQZ,jdbcType=VARCHAR},
          </if>
          <if test="LDJZ != null and LDJZ != ''">
              LDJZ,#{YXRQZ,jdbcType=VARCHAR},
          </if>
          <if test="BZ != null and BZ != ''">
              BZ,#{YXRQZ,jdbcType=VARCHAR},
          </if>
          <if test="YSSBSD != null and YSSBSD != ''">
              YSSBSD,#{YXRQZ,jdbcType=VARCHAR},
          </if>
          where
          XH=#{XH,jdbcType=VARCHAR}
      </update>
  -->

    <update id="update_txz" parameterType="org.fh.entity.pass.PASLICENSEWithBLOBs">
        update PASLICENSE
        set

        <if test="auditing_status != null and auditing_status != ''">
            AUDITING_STATUS=#{auditing_status,jdbcType=VARCHAR},
        </if>
        <if test="txzlx != null and txzlx != ''">
            TXZLX=#{txzlx,jdbcType=VARCHAR},
        </if>
        <if test="hpzl != null and hpzl != ''">
            HPZL=#{hpzl,jdbcType=VARCHAR},
        </if>
        <if test="hphm != null and hphm != ''">
            HPHM=#{hphm,jdbcType=VARCHAR},
        </if>
        <if test="cllx != null and cllx != ''">
            CLLX=#{cllx,jdbcType=VARCHAR},
        </if>
        <if test="syr != null and syr != ''">
            SYR=#{syr,jdbcType=VARCHAR},
        </if>
        <if test="yxrqs != null  ">
            YXRQS=#{yxrqs,jdbcType=TIMESTAMP},
        </if>
        <if test="yxrqz != null ">
            YXRQZ=#{yxrqz,jdbcType=TIMESTAMP},
        </if>
        <if test="yxrq != null and yxrq != ''">
            YXRQ=#{yxrq,jdbcType=VARCHAR},
        </if>
        <if test="dljb != null and dljb != ''">
            DLJB=#{dljb,jdbcType=VARCHAR},
        </if>
        <if test="txjx != null and txjx != ''">
            TXJX=#{txjx,jdbcType=VARCHAR},
        </if>
        <if test="ldjz != null and ldjz != ''">
            LDJZ=#{ldjz,jdbcType=VARCHAR},
        </if>
        <if test="bz != null and bz != ''">
            BZ=#{bz,jdbcType=VARCHAR},
        </if>
        <if test="yssbsd != null and yssbsd != ''">
            YSSBSD=#{yssbsd,jdbcType=VARCHAR},
        </if>
        <if test="zx != null and zx != ''">
            ZX=#{zx,jdbcType=VARCHAR},
        </if>
        <if test="spr != null and spr != ''">
            SPR=#{spr,jdbcType=VARCHAR},
        </if>
        <if test="ylzd3 != null and ylzd3 != ''">
            YLZD3=#{ylzd3,jdbcType=VARCHAR},
        </if>
        <if test="sprq != null">
            SPRQ=#{sprq,jdbcType=TIMESTAMP},
        </if>
        <if test="ylzd5 != null and ylzd5 != ''">
            YLZD5=#{ylzd5,jdbcType=CLOB},
        </if>
        <if test="ylzd6 != null and ylzd6 != ''">
            YLZD6=#{ylzd6,jdbcType=CLOB},
        </if>
        GXSJ=#{gxsj,jdbcType=TIMESTAMP}
        where
        XH=#{xh,jdbcType=VARCHAR}
    </update>
    <!-- 通过ID获取数据 -->
    <select id="getId" parameterType="java.lang.String" resultType="org.fh.entity.pass.PASLICENSE">
        select
        <include refid="Field"></include>
        from
        PASLICENSE
        f
        where
        f.XH = #{XH}
    </select>

</mapper>
