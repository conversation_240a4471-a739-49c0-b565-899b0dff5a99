<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno2.pass.VehicleApprovalMapper">
    <!--表名 -->
    <sql id="tableName">
        "PASLICENSE_ENTERPRISE_VEH"
    </sql>

    <!--数据字典表名 -->
    <sql id="dicTableName">
        "SYS_DICTIONARIES"
    </sql>
    <!-- 字段 -->
    <sql id="Field">
        f."ID",
        f."ENTERPRISEID",
        f."OWNERTYPE",
        f."VEHTYPE",
        f."VEHNUM",
        f."DRIVERNAME",
        f."DRIVERCARD",
        f."DRIVERPHONE",
        f."SHZT",
        f."SHSJ",
        f."SHR",
        f."SHYJ",
        f."TJSJ",
        f."XGSJ",
        f."OWNERNAME",
        f."OWNERIDCARD",
        f."OWNERPHONE",
        f."ACTOWNER",
        f."ACTIDCARD",
        f."ACTPHONE",
        f."SWITCH",
        f."SWITCHTIME",
        f."MAXPASSLEVEL",
        f."CLLX",
        f."SYXZ",
        f."CLSYR",
        f."CJH",
        f."FDJH",
        f."MEMBERIDCARD",
        f."WSCL",
        f."NSYXQ"
    </sql>

    <!-- 字段用于新增 -->
    <sql id="Field2">
        "ID",
        "ENTERPRISEID",
        "OWNERTYPE",
        "VEHTYPE",
        "VEHNUM",
        "DRIVERNAME",
        "DRIVERCARD",
        "DRIVERPHONE",
        "VEHICLELICENSEPIC",
        "SHZT",
        "SHSJ",
        "SHR",
        "SHYJ",
        "TJSJ",
        "XGSJ",
        "OWNERNAME",
        "OWNERIDCARD",
        "OWNERPHONE",
        "ACTOWNER",
        "ACTIDCARD",
        "ACTPIC",
        "DRIVERPIC",
        "ACTPHONE",
        "SWITCH",
        "SWITCHTIME",
        "MAXPASSLEVEL",
        "VEHPIC",
        "VEHFB",
        "CLLX",
        "SYXZ",
        "CLSYR",
        "CLWTS",
        "CJH",
        "FDJH",
        "MEMBERIDCARD",
        "WSCL",
        "NSYXQ"
    </sql>
    <!-- 用于分页查询字段 -->
    <sql id="Field3">
        t2."ID",
        t2."ENTERPRISEID",
        t2."OWNERTYPE",
        t2."VEHTYPE",
        t2."VEHNUM",
        t2."DRIVERNAME",
        t2."DRIVERCARD",
        t2."DRIVERPHONE",

        t2."SHZT",
        t2."SHSJ",
        t2."SHR",
        t2."SHYJ",
        t2."TJSJ",
        t2."XGSJ",
        t2."OWNERNAME",
        t2."OWNERIDCARD",
        t2."OWNERPHONE",
        t2."ACTOWNER",
        t2."ACTIDCARD",

        t2."ACTPHONE",
        t2."SWITCH",
        t2."SWITCHTIME",
        t2."MAXPASSLEVEL",

        t2."CLLX",
        t2."SYXZ",
        t2."CLSYR",
        t2."MEMBERIDCARD",
        t2."WSCL",
        t2."NSYXQ"
    </sql>
    <!-- 字段 -->
    <sql id="Field4">
        f."ID",
        f."ENTERPRISEID",
        f."OWNERTYPE",
        f."VEHTYPE",
        f."VEHNUM",
        f."DRIVERNAME",
        f."DRIVERCARD",
        f."DRIVERPHONE",
        f."VEHICLELICENSEPIC",
        f."SHZT",
        f."SHSJ",
        f."SHR",
        f."SHYJ",
        f."TJSJ",
        f."XGSJ",
        f."OWNERNAME",
        f."OWNERIDCARD",
        f."OWNERPHONE",
        f."ACTOWNER",
        f."ACTIDCARD",
        f."ACTPIC",
        f."DRIVERPIC",
        f."ACTPHONE",
        f."SWITCH",
        f."SWITCHTIME",
        f."MAXPASSLEVEL",
        f."VEHPIC",
        f."VEHFB",
        f."CLLX",
        f."SYXZ",
        f."CLSYR",
        f."CLWTS",
        f."MEMBERIDCARD",
        f."WSCL",
        f."NSYXQ"
    </sql>

    <!-- 字段值 -->
    <sql id="FieldValue">
        #{ID,jdbcType=VARCHAR},
        #{ENTERPRISEID},
        #{OWNERTYPE},
        #{VEHTYPE},
        #{VEHNUM},
        #{DRIVERNAME},
        #{DRIVERCARD},
        #{DRIVERPHONE},
        #{VEHICLELICENSEPIC},
        #{SHZT},
        #{SHSJ},
        #{SHR},
        #{SHYJ},
        #{TJSJ},
        #{XGSJ},
        #{OWNERNAME},
        #{OWNERIDCARD},
        #{OWNERPHONE},
        #{ACTOWNER},
        #{ACTIDCARD},
        #{ACTPIC},
        #{DRIVERPIC},
        #{ACTPHONE},
        #{SWITCH},
        #{SWITCHTIME},
        #{MAXPASSLEVEL},
        #{VEHPIC},
        #{VEHFB},
        #{CLLX},
        #{SYXZ},
        #{CLSYR},
        #{CLWTS},
        #{CJH},
        #{FDJH},
        #{MEMBERIDCARD},
        #{WSCL},
        #{NSYXQ}
    </sql>

    <!-- 新增-->
    <insert id="save" parameterType="pd">
        insert into
        <include refid="tableName">
        </include>
        (
        <include refid="Field2">
        </include>
        ) values (
        <include refid="FieldValue">
        </include>
        )
    </insert>
    <insert id="saveInfo" parameterType="org.fh.entity.pass.PaslicenseEnterprisVeh">
        insert into PASLICENSE_ENTERPRISE_VEH
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ID != null">ID,</if>
            <if test="VEHTYPE != null">VEHTYPE,</if>
            <if test="VEHNUM != null">VEHNUM,</if>
            <if test="ENTERPRISEID != null">ENTERPRISEID,</if>
            <if test="TJSJ != null">TJSJ,</if>
            <if test="CLLX != null">CLLX,</if>
            <if test="DRIVERNAME != null">DRIVERNAME,</if>
            <if test="DRIVERCARD != null">DRIVERCARD,</if>
            <if test="DRIVERPHONE != null">DRIVERPHONE,</if>
            <if test="OWNERNAME != null">OWNERNAME,</if>
            <if test="OWNERIDCARD != null">OWNERIDCARD,</if>
            <if test="OWNERPHONE != null">OWNERPHONE,</if>
            <if test="ACTOWNER != null">ACTOWNER,</if>
            <if test="ACTIDCARD != null">ACTIDCARD,</if>
            <if test="ACTPHONE != null">ACTPHONE,</if>
            <if test="ACTPIC != null">ACTPIC,</if>
            <if test="VEHFB != null">VEHFB,</if>
            <if test="VEHICLELICENSEPIC != null">VEHICLELICENSEPIC,</if>
            <if test="VEHPIC != null">VEHPIC,</if>
            <if test="SHZT != null">SHZT,</if>
            <if test="SHR != null">SHR,</if>
            <if test="SHSJ != null">SHSJ,</if>
            <if test="CJH != null">CJH,</if>
            <if test="FDJH != null">FDJH,</if>
            <if test="MEMBERIDCARD != null">MEMBERIDCARD,</if>
            <if test="WSCL != null">WSCL,</if>
            <if test="NSYXQ != null">NSYXQ,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ID != null">#{ID},</if>
            <if test="VEHTYPE != null">#{VEHTYPE},</if>
            <if test="VEHNUM != null">#{VEHNUM},</if>
            <if test="ENTERPRISEID != null">#{ENTERPRISEID},</if>
            <if test="TJSJ != null">#{TJSJ},</if>
            <if test="CLLX != null">#{CLLX},</if>
            <if test="DRIVERNAME != null">#{DRIVERNAME},</if>
            <if test="DRIVERCARD != null">#{DRIVERCARD},</if>
            <if test="DRIVERPHONE != null">#{DRIVERPHONE},</if>
            <if test="OWNERNAME != null">#{OWNERNAME},</if>
            <if test="OWNERIDCARD != null">#{OWNERIDCARD},</if>
            <if test="OWNERPHONE != null">#{OWNERPHONE},</if>
            <if test="ACTOWNER != null">#{ACTOWNER},</if>
            <if test="ACTIDCARD != null">#{ACTIDCARD},</if>
            <if test="ACTPHONE != null">#{ACTPHONE},</if>
            <if test="ACTPIC != null">#{ACTPIC},</if>
            <if test="VEHFB != null">#{VEHFB},</if>
            <if test="VEHICLELICENSEPIC != null">#{VEHICLELICENSEPIC},</if>
            <if test="VEHPIC != null">#{VEHPIC},</if>
            <if test="SHZT != null">#{SHZT},</if>
            <if test="SHR != null">#{SHR},</if>
            <if test="SHSJ != null">#{SHSJ},</if>
            <if test="CJH != null">#{CJH},</if>
            <if test="FDJH != null">#{FDJH},</if>
            <if test="MEMBERIDCARD != null">#{MEMBERIDCARD},</if>
            <if test="WSCL != null">#{WSCL},</if>
            <if test="NSYXQ != null">#{NSYXQ},</if>
        </trim>
    </insert>

    <!-- 删除-->
    <delete id="delete" parameterType="pd">
        delete from
        <include refid="tableName">
        </include>
        where
        "ID" = #{ID}
        and
        "VEHNUM" = #{VEHNUM}
    </delete>
    <!-- 修改 -->
    <update id="edit" parameterType="org.fh.entity.pass.PaslicenseEnterpris">
        update
        <include refid="tableName"></include>
        <trim prefix="SET" suffixOverrides=",">
            <if test="VEHTYPE != null and VEHTYPE != ''">
                "VEHTYPE" = #{VEHTYPE},
            </if>
            <if test="VEHNUM != null and VEHNUM != ''">
                "VEHNUM" = #{VEHNUM},
            </if>
            <if test="ENTERPRISEID != null and ENTERPRISEID != ''">
                "ENTERPRISEID" = #{ENTERPRISEID},
            </if>
            <if test="DRIVERNAME != null and DRIVERNAME != ''">
                "DRIVERNAME" = #{DRIVERNAME},
            </if>
            <if test="DRIVERCARD != null and DRIVERCARD != ''">
                "DRIVERCARD" = #{DRIVERCARD},
            </if>
            <if test="DRIVERPHONE != null and DRIVERPHONE != ''">
                "DRIVERPHONE" = #{DRIVERPHONE},
            </if>
            <if test="VEHICLELICENSEPIC != null and VEHICLELICENSEPIC != ''">
                "VEHICLELICENSEPIC" = #{VEHICLELICENSEPIC},
            </if>
            <if test="SHZT != null and SHZT != ''">
                "SHZT" = #{SHZT},
            </if>
            <if test="SHSJ != null">
                "SHSJ" = #{SHSJ},
            </if>
            <if test="SHR != null and SHR != ''">
                "SHR" = #{SHR},
            </if>
            <if test="SHYJ != null and SHYJ != ''">
                "SHYJ" = #{SHYJ},
            </if>
            <if test="TJSJ != null">
                "TJSJ" = #{TJSJ},
            </if>
            <if test="XGSJ != null">
                "XGSJ" = #{XGSJ},
            </if>
            <if test="OWNERNAME != null and OWNERNAME != ''">
                "OWNERNAME" = #{OWNERNAME},
            </if>
            <if test="OWNERIDCARD != null and OWNERIDCARD != ''">
                "OWNERIDCARD" = #{OWNERIDCARD},
            </if>
            <if test="OWNERPHONE != null and OWNERPHONE != ''">
                "OWNERPHONE" = #{OWNERPHONE},
            </if>
            <if test="OWNERTYPE != null and OWNERTYPE != ''">
                "OWNERTYPE" = #{OWNERTYPE},
            </if>
            <if test="ACTOWNER != null and ACTOWNER != ''">
                "ACTOWNER" = #{ACTOWNER},
            </if>
            <if test="ACTIDCARD != null and ACTIDCARD != ''">
                "ACTIDCARD" = #{ACTIDCARD},
            </if>
            <if test="ACTPIC != null and ACTPIC != ''">
                "ACTPIC" = #{ACTPIC},
            </if>
            <if test="DRIVERPIC != null and DRIVERPIC != ''">
                "DRIVERPIC" = #{DRIVERPIC},
            </if>
            <if test="ACTPHONE != null and ACTPHONE != ''">
                "ACTPHONE" = #{ACTPHONE},
            </if>
            <if test="SWITCH != null and SWITCH != ''">
                "SWITCH" = #{SWITCH},
            </if>
            <if test="SWITCHTIME != null">
                "SWITCHTIME" = #{SWITCHTIME},
            </if>
            <if test="MAXPASSLEVEL != null and MAXPASSLEVEL != ''">
                "MAXPASSLEVEL" = #{MAXPASSLEVEL},
            </if>
            <if test="VEHPIC != null and VEHPIC != ''">
                "VEHPIC" = #{VEHPIC},
            </if>
            <if test="VEHFB != null and VEHFB != ''">
                "VEHFB" = #{VEHFB},
            </if>
            <if test="CLLX != null and CLLX != ''">
                "CLLX" = #{CLLX},
            </if>
            <if test="SYXZ != null and SYXZ != ''">
                "SYXZ" = #{SYXZ},
            </if>
            <if test="CLSYR != null and CLSYR != ''">
                "CLSYR" = #{CLSYR},
            </if>
            <if test="CLWTS != null and CLWTS != ''">
                "CLWTS" = #{CLWTS},
            </if>
            <if test="CJH != null and CJH != ''">
                "CJH" = #{CJH},
            </if>
            <if test="FDJH != null and FDJH != ''">
                "FDJH" = #{FDJH},
            </if>
            <if test="MEMBERIDCARD != null and MEMBERIDCARD != ''">
                "MEMBERIDCARD" = #{MEMBERIDCARD},
            </if>
            <if test="NSYXQ != null">
                "NSYXQ" = #{NSYXQ},
            </if>
        </trim>
        where
        "ID" = #{ID}
    </update>
    <!-- 通过ID获取数据 -->
    <select id="findByInfo" parameterType="org.fh.entity.pass.PaslicenseEnterprisVeh"
            resultType="org.fh.entity.pass.PaslicenseEnterprisVeh">
        select
        <include refid="Field">
        </include>
        from
        <include refid="tableName">
        </include>
        f
        where
        f."ID" = #{ID}
    </select>
    <!-- 通过ID获取数据 -->
    <select id="findById" parameterType="pd" resultType="pd">
        select
        <include refid="Field">
        </include>
        from
        <include refid="tableName">
        </include>
        f
        where
        f."ID" = #{ID}
    </select>
    <select id="findByID" parameterType="String" resultType="org.fh.entity.pass.PaslicenseEnterprisVeh">
        select
        <include refid="Field2">
        </include>
        from
        <include refid="tableName">
        </include>
        f
        where
        f."ID" = #{ID}
    </select>

    <resultMap id="ImageResultMap" type="org.fh.entity.pass.VehicleImageBean">
        <result column="ACTPIC" jdbcType="CLOB" property="ACTPIC"/>
        <result column="VEHICLELICENSEPIC" jdbcType="CLOB" property="VEHICLELICENSEPIC"/>
        <result column="VEHPIC" jdbcType="CLOB" property="VEHPIC"/>
        <result column="VEHFB" jdbcType="CLOB" property="VEHFB"/>
    </resultMap>
    <!-- 通过ID获取acccount表中图片-->
    <select id="getImageById" parameterType="pd" resultMap="ImageResultMap">
        select
        <if test="msg==1"><!-- 实际所有人身份证图片 -->
            ACTPIC
        </if>
        <if test="msg==2"><!-- 行驶证图片 -->
            VEHICLELICENSEPIC
        </if>
        <if test="msg==3"><!-- 行驶证车辆照片 -->
            VEHPIC
        </if>
        <if test="msg==4"><!-- 行驶证副本 -->
            VEHFB
        </if>
        <!--        <if test="msg=='实际所有人身份证图片'">&lt;!&ndash; 实际所有人身份证图片 &ndash;&gt;-->
        <!--            ACTPIC-->
        <!--        </if>-->
        <!--        <if test="msg==行驶证图片">&lt;!&ndash; 行驶证图片 &ndash;&gt;-->
        <!--            VEHICLELICENSEPIC-->
        <!--        </if>-->
        <!--        <if test="msg==行驶证车辆照片">&lt;!&ndash; 行驶证车辆照片 &ndash;&gt;-->
        <!--            VEHPIC-->
        <!--        </if>-->
        <!--        <if test="msg==行驶证副本">&lt;!&ndash; 行驶证副本 &ndash;&gt;-->
        <!--            VEHFB-->
        <!--        </if>-->
        from
        <include refid="tableName"></include>
        f
        where
        f."ID" = #{ID}
    </select>


    <!-- 列表(全部) -->
    <select id="listAll" parameterType="pd" resultType="pd">
        select
        <include refid="Field">
        </include>
        from
        <include refid="tableName">
        </include>
        f
    </select>

    <!-- 列表 (关联表PASLICENSE_ENTERPRISE ,)-->
    <select id="datalistPage" parameterType="page" resultType="pd">
        SELECT B.ENTERPRISEID, B.OWNERTYPE, B.VEHTYPE,
        B.VEHNUM, B.OWNERNAME,B.OWNERIDCARD,B.OWNERPHONE,B.SHZT,B.SHSJ,B.SHR,B.XGSJ,B.CLLX,B.ACTOWNER,B.ID,
        B.ACTIDCARD,B.ACTPHONE,B.WSCL,B.NSYXQ
        ,A.TJBM,A.ENTERPRISENAME FROM PASLICENSE_ENTERPRISE_VEH B LEFT JOIN PASLICENSE_ENTERPRISE A ON B.ENTERPRISEID =
        A.ID
        where 1=1
<!--        <if test="pd.ENTERPRISEID != null and pd.ENTERPRISEID != ''">&lt;!&ndash; 关键词检索 &ndash;&gt;-->
<!--            and-->
<!--            (-->
<!--            ENTERPRISEID LIKE CONCAT(CONCAT('%', #{pd.ENTERPRISEID}),'%')-->
<!--            )-->
<!--        </if>-->
        <if test="pd.HPHMKEYWORDS != null and pd.HPHMKEYWORDS != ''"><!-- 关键词检索 -->
            and
            (
            B.VEHNUM LIKE CONCAT(CONCAT('%', #{pd.HPHMKEYWORDS}),'%')
            )
        </if>
        <if test="pd.SHRKEYWORDS != null and pd.SHRKEYWORDS != ''"><!-- 关键词检索 -->
            and
            (
            B.SHR LIKE CONCAT(CONCAT('%', #{pd.SHRKEYWORDS}),'%')
            )
        </if>
        <if test="pd.SHZTKEYWORDS != null and pd.SHZTKEYWORDS != ''"><!-- 关键词检索 -->
            and
            (
            B.SHZT LIKE CONCAT(CONCAT('%', #{pd.SHZTKEYWORDS}),'%')
            )
        </if>
        <if test="pd.ENTERPRISENAMEKEYWORDS != null and pd.ENTERPRISENAMEKEYWORDS != ''"><!-- 关键词检索 -->
            and
            (
            A.ENTERPRISENAME LIKE CONCAT(CONCAT('%', #{pd.ENTERPRISENAMEKEYWORDS}),'%')
            )
        </if>
        <if test="pd.HPZLKEYWORDS != null and pd.HPZLKEYWORDS != ''"><!-- 关键词检索 号牌种类 -->
            and
            (
            B.VEHTYPE LIKE CONCAT(CONCAT('%', #{pd.HPZLKEYWORDS}),'%')
            )
        </if>
<!--        <if test="pd.WSCL != null and pd.WSCL != ''">-->
<!--            and-->
<!--            B.WSCL = #{pd.WSCL}-->
<!--        </if>-->
        <choose>
            <when test="pd.WSCL != null and pd.WSCL != ''"><!-- 关键词检索 外省车辆 -->
                and
                B.WSCL = #{pd.WSCL}
            </when>
            <otherwise>
                and B.ENTERPRISEID is not null
            </otherwise>
        </choose>
        <if test="pd.TJBMKEYWORDS == '小程序端'.toString()">
            and A.TJBM is null
        </if>
        <if test="pd.ENTEID == '否'.toString()">
            and B.ENTERPRISEID is null
        </if>
        <if test="pd.ENTEID == '是'.toString()">
            and B.ENTERPRISEID is not null
        </if>
        <if test="pd.TJBMKEYWORDS != null and pd.TJBMKEYWORDS != '' and pd.TJBMKEYWORDS !='小程序端'.toString() ">
            and
            (
            A.TJBM LIKE CONCAT(CONCAT('%', #{pd.TJBMKEYWORDS}),'%')
            )
        </if>

    </select>

    <select id="getCount" parameterType="pd" resultType="int">
        select count(*) from (

        SELECT * FROM PASLICENSE_ENTERPRISE_VEH B LEFT JOIN PASLICENSE_ENTERPRISE A ON B.ENTERPRISEID = A.ID
        where 1=1
        <if test="ENTERPRISENAMEKEYWORDS != null and ENTERPRISENAMEKEYWORDS != ''"><!-- 关键词检索 -->
            and
            (
            ENTERPRISENAME LIKE CONCAT(CONCAT('%', #{ENTERPRISENAMEKEYWORDS}),'%')
            )
        </if>
        <if test="TJBMKEYWORDS == '小程序端'.toString()">
            and TJBM is null
        </if>
        <if test="TJBMKEYWORDS != null and TJBMKEYWORDS != '' and TJBMKEYWORDS !='小程序端'.toString() ">
            and
            (
            A.TJBM LIKE CONCAT(CONCAT('%', #{TJBMKEYWORDS}),'%')
            )
        </if>
        <if test="ENTERPRISEID != null and ENTERPRISEID != ''"><!-- 关键词检索 -->
            and
            (
            ENTERPRISEID LIKE CONCAT(CONCAT('%', #{ENTERPRISEID}),'%')
            )
        </if>
        <if test="HPHMKEYWORDS != null and HPHMKEYWORDS != ''"><!-- 关键词检索 -->
            and
            (
            VEHNUM LIKE CONCAT(CONCAT('%', #{HPHMKEYWORDS}),'%')
            )
        </if>
        <if test="SHRKEYWORDS != null and SHRKEYWORDS != ''"><!-- 关键词检索 -->
            and
            (
            SHR LIKE CONCAT(CONCAT('%', #{SHRKEYWORDS}),'%')
            )
        </if>

        )tmp_count
    </select>

    <!-- 批量删除 -->
    <delete id="deleteAll" parameterType="String">
        delete from
        <include refid="tableName">
        </include>
        where
        "ID" in
        <foreach item="item" index="index" collection="array" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
    <select id="findByCar" parameterType="page" resultType="pd">
        select
        <include refid="Field">
        </include>
        from
        <include refid="tableName">
        </include>
        f
        where
        f.ACTIDCARD = #{pd.ACTIDCARD}
        <if test="pd.VEHNUM != null and pd.VEHNUM != ''">
            and
            (
            f.VEHNUM LIKE CONCAT(CONCAT('%', #{pd.VEHNUM}),'%')
            )
        </if>
        <if test="pd.ENTERPRISEID != null and pd.ENTERPRISEID != ''">
            and
            f.ENTERPRISEID = #{pd.ENTERPRISEID}
        </if>
    </select>

    <!-- 判断该车辆是否存在该企业下-->
    <select id="findByEN" parameterType="page" resultType="pd">
        select
        <include refid="Field">
        </include>
        from
        <include refid="tableName">
        </include>
        f
        where
        f.ENTERPRISEID = #{pd.ENTERPRISEID}
        <if test="pd.VEHNUM != null and pd.VEHNUM != ''">
            and
            f.VEHNUM = #{pd.VEHNUM}
        </if>
        <if test="pd.VEHTYPE != null and pd.VEHTYPE != ''">
            and
            f.VEHTYPE = #{pd.VEHTYPE}
        </if>
    </select>

    <!-- 判断该车辆是否存在该身份证下-->
    <select id="findByAHE" parameterType="page" resultType="pd">
        select
        <include refid="Field">
        </include>
        from
        <include refid="tableName">
        </include>
        f
        where 1=1
        <if test="pd.VEHNUM != null and pd.VEHNUM != ''">
            and
            f.VEHNUM = #{pd.VEHNUM}
        </if>
        <if test="pd.VEHTYPE != null and pd.VEHTYPE != ''">
            and
            f.VEHTYPE = #{pd.VEHTYPE}
        </if>
        <if test="pd.MEMBERIDCARD != null and pd.MEMBERIDCARD != ''">
            and (f.MEMBERIDCARD = #{pd.MEMBERIDCARD} or f.ACTIDCARD = #{pd.MEMBERIDCARD})and f.ENTERPRISEID is null
        </if>
        <if test="pd.ENTERPRISEID != null and pd.ENTERPRISEID != ''">
            and
            f.ENTERPRISEID = #{pd.ENTERPRISEID}
        </if>
    </select>
    <!-- 判断该车辆是否存在该身份证下列表分页-->
    <select id="findByAHElistPage" parameterType="page" resultType="pd">
        select
        <include refid="Field">
        </include>
        from
        <include refid="tableName">
        </include>
        f
        where 1=1
        <if test="pd.MEMBERIDCARD != null and pd.MEMBERIDCARD != ''">
            and (
            f.MEMBERIDCARD = #{pd.MEMBERIDCARD} or f.ACTIDCARD = #{pd.MEMBERIDCARD})
        </if>
        <if test="pd.VEHNUM != null and pd.VEHNUM != ''">
            and
            (
                VEHNUM LIKE CONCAT(CONCAT('%', #{pd.VEHNUM}),'%')
            )

        </if>
        <if test="pd.VEHTYPE != null and pd.VEHTYPE != ''">
            and
            f.VEHTYPE = #{pd.VEHTYPE}
        </if>
        <if test="pd.ENTERPRISEID != null and pd.ENTERPRISEID != ''">
            and
            f.ENTERPRISEID = #{pd.ENTERPRISEID}
        </if>
    </select>
    <!-- 根据车辆查询实际所有人身份证-->
    <select id="selectIdCard"  parameterType="String" resultType="String">
        select
        ACTIDCARD
        from
        (SELECT * FROM PASLICENSE_ENTERPRISE_VEH ORDER BY TJSJ DESC)
        PASLICENSE_ENTERPRISE_VEH
        where
        rownum = 1
        and
        SHZT = 1
        and
        VEHNUM = #{VEHNUM}
        and
        VEHTYPE = #{VEHTYPE}
        and
        (MEMBERIDCARD = #{MEMBERIDCARD} or ACTIDCARD = #{MEMBERIDCARD})
    </select>
    <select id="selectCard"  parameterType="String" resultType="String">
        select
        ACTIDCARD
        from
        (SELECT * FROM PASLICENSE_ENTERPRISE_VEH ORDER BY TJSJ DESC)
        PASLICENSE_ENTERPRISE_VEH
        where
        rownum = 1
        and
        SHZT = 1
        and
        VEHNUM = #{VEHNUM}
        and
        VEHTYPE = #{VEHTYPE}
        and
        ENTERPRISEID = #{ENTERPRISEID}
    </select>

    <select id="selectByIdCard"  parameterType="String" resultType="String">
        select
                SHZT
        from
                (SELECT * FROM PASLICENSE_ENTERPRISE_VEH ORDER BY TJSJ DESC)
                        PASLICENSE_ENTERPRISE_VEH
        where
                VEHNUM = #{VEHNUM}
          and
                VEHTYPE = #{VEHTYPE}
          and
                rownum = 1
    </select>

    <select id="GetIdCard"  parameterType="String" resultType="String">
        select
                ENTERPRISEID
        from
                (SELECT * FROM PASLICENSE_ENTERPRISE_VEH ORDER BY TJSJ DESC)
                        PASLICENSE_ENTERPRISE_VEH
        where
                VEHNUM = #{VEHNUM}
          and
                VEHTYPE = #{VEHTYPE}
          and
                SHZT = 1
          and
                rownum = 1
    </select>

    <select id="GetByIdCard"  parameterType="String" resultType="String">
        select
                ACTIDCARD
        from
                (SELECT * FROM PASLICENSE_ENTERPRISE_VEH ORDER BY TJSJ DESC)
                        PASLICENSE_ENTERPRISE_VEH
        where
                VEHNUM = #{VEHNUM}
          and
                VEHTYPE = #{VEHTYPE}
          and
                SHZT = 1
          and
                rownum = 1
    </select>

    <!-- 根据ID查询车辆详细信息-->
    <select id="GetInfoById" parameterType="String" resultType="org.fh.entity.pass.PaslicenseEnterprisVeh">
        select
        *
        from
        <include refid="tableName">
        </include>
        where
        "ID" = #{ID}
    </select>

</mapper>
