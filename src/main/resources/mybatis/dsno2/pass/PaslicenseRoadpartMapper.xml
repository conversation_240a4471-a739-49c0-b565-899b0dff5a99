<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno2.pass.PaslicenseRoadpartMapper">
  <resultMap id="BaseResultMap" type="org.fh.entity.pass.PaslicenseRoadpart">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="LDMC" jdbcType="VARCHAR" property="ldmc" />
    <result column="PX" jdbcType="VARCHAR" property="px" />
    <result column="LHY" jdbcType="VARCHAR" property="lhy" />
    <result column="PY" jdbcType="VARCHAR" property="py" />
    <result column="DJ" jdbcType="VARCHAR" property="dj" />
    <result column="XS" jdbcType="VARCHAR" property="xs" />
    <result column="SJID" jdbcType="VARCHAR" property="sjid" />
    <result column="ZT" jdbcType="VARCHAR" property="zt" />
    <result column="JH" jdbcType="VARCHAR" property="jh" />
    <result column="DLHS" jdbcType="VARCHAR" property="dlhs" />
    <result column="GXSJ" jdbcType="TIMESTAMP" property="gxsj" />
  </resultMap>
  <cache
          eviction="FIFO"
          flushInterval="60000"
          size="512"
          readOnly="true"/>

  <sql id="Base_Column_List">
    ID, LDMC, PX, LHY, PY, DJ, XS, SJID, ZT, JH, DLHS, GXSJ
  </sql>
  <sql id="List">
    ID,LDMC,DJ,DLHS
  </sql>

  <select id="select" parameterType="java.lang.String" resultType="org.fh.entity.pass.PaslicenseRoadpart">
    select
    <include refid="List"/>, PY
    from PASLICENSE_ROADPART
    where DJ = #{dj,jdbcType=VARCHAR} and XS = '1' and ZT = '0'
    order by PY
  </select>

  <select id="selectMap" resultType="java.util.HashMap">
    select
    ID,DLHS
    from PASLICENSE_ROADPART
    where XS = '1' and ZT = '0'
    order by PY
  </select>


  <select id="selectByDj"  resultType="java.lang.String">
    select
    ID
    from PASLICENSE_ROADPART
    where DJ in ('1','2') and XS = '1' and ZT = '0'
    order by PY
  </select>

  <select id="selectAll"  resultType="org.fh.entity.pass.PaslicenseRoadpart">
    select
    <include refid="List"/>
    from PASLICENSE_ROADPART
    where XS = '1' and ZT = '0' order by PY
  </select>
</mapper>
