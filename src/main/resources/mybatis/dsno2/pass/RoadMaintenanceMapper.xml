<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno2.pass.RoadMaintenanceMapper">

    <!--表名 -->
    <sql id="tableName">
		"PASLICENSE_ROADMAINTENANCE"
	</sql>

    <!--数据字典表名 -->
    <sql id="dicTableName">
		"SYS_DICTIONARIES"
	</sql>

    <!-- 字段 -->
    <sql id="Field">
		f."ID",
		f."ROADID",
		f."LDMC",
		f."IS_WHWC",
		f."DJRQ"
	</sql>

    <!-- 字段用于新增 -->
    <sql id="Field2">
		"ID",
		"ROADID",
		"LDMC",
		"IS_WHWC",
		"DJRQ"
	</sql>

    <!--用于分页 字段 -->
    <sql id="Field3">
       t2."ID",
       t2."ROADID",
       t2."LDMC",
       t2."IS_WHWC",
       t2."DJRQ"
    </sql>

    <!-- 字段值 -->
    <sql id="FieldValue">
		#{ID,jdbcType=VARCHAR},
		#{ROADID,jdbcType=VARCHAR},
		#{LDMC,jdbcType=VARCHAR},
		#{IS_WHWC,jdbcType=VARCHAR},
		#{DJRQ,jdbcType=DATE}
	</sql>

    <!-- 新增-->
    <insert id="save" parameterType="pd">
        insert into
        <include refid="tableName"></include>
        (
        <include refid="Field2"></include>
        ) values (
        <include refid="FieldValue"></include>
        )
    </insert>

    <!-- 删除-->
    <delete id="delete" parameterType="pd">
        delete from
        <include refid="tableName"></include>
        where
        "ID" = #{ID}
    </delete>

    <!-- 修改 -->
    <update id="edit" parameterType="pd">
        update
        <include refid="tableName"></include>
        set
        "ROADID" = #{ROADID,jdbcType=VARCHAR},
        "LDMC" = #{LDMC,jdbcType=VARCHAR},
        "IS_WHWC" = #{IS_WHWC,jdbcType=VARCHAR},
        "DJRQ" = #{DJRQ,jdbcType=DATE}
        where
        "ID" = #{ID,jdbcType=VARCHAR}
    </update>

    <!-- 通过ID获取数据 -->
    <select id="findById" parameterType="pd" resultType="pd">
        select
        <include refid="Field"></include>
        from
        <include refid="tableName"></include>
        f
        where
        f."ID" = #{ID}
    </select>

    <!-- 列表 -->
    <select id="datalistPage" parameterType="page" resultType="pd">

        select <include refid="Field"></include> from <include refid="tableName"></include> f where 1=1
        <if test="pd.KEYWORDS != null and pd.KEYWORDS != ''"><!-- 关键词检索 -->
            and
            (
            <!--	根据需求自己加检索条件
                字段1 LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
                 or
                字段2 LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
            -->
            )
        </if>


    </select>

    <!-- 列表(全部) -->
    <select id="listAll" parameterType="pd" resultType="pd">
        select
        <include refid="Field"></include>
        from
        <include refid="tableName"></include>
        f
    </select>

    <select id="getCount" parameterType="pd" resultType="int">
        select Count(*) from (
        select f."ID"  from <include refid="tableName"></include> f where 1=1
<!--        <if test="KEYWORDS != null and KEYWORDS != ''">&lt;!&ndash; 关键词检索 &ndash;&gt;-->
<!--            and-->
<!--            (-->
<!--            &lt;!&ndash;	根据需求自己加检索条件-->
<!--                字段1 LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')-->
<!--                 or-->
<!--                字段2 LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')-->
<!--            &ndash;&gt;-->
<!--            )-->
<!--        </if>-->
        )tmp_count
    </select>

    <!-- 批量删除 -->
    <delete id="deleteAll" parameterType="String">
        delete from
        <include refid="tableName"></include>
        where
        "ID" in
        <foreach item="item" index="index" collection="array" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- fh313596790qq(青苔) -->
</mapper>