<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno2.pass.PaslicenseMouldInfoMapper">

	<!--表名  -->
	<sql id="tableName">
		"PASLICENSE_MOULDINFO"
	</sql>

	<!--数据字典表名 -->
	<sql id="dicTableName">
		"SYS_DICTIONARIES"
	</sql>

	<!-- 字段 -->
	<sql id="Field">
		f."ID",
		f."MBID",
		f."ROADPARTID"
	</sql>

	<!-- 字段用于新增 -->
	<sql id="Field2">
		"ID",
		"MBID",
		"ROADPARTID"
	</sql>

	<!-- 字段值 -->
	<sql id="FieldValue">
		#{ID,jdbcType=VARCHAR},
		#{MBID,jdbcType=VARCHAR},
		#{ROADPARTID,jdbcType=VARCHAR}

	</sql>

	<!-- 新增-->
	<insert id="save" parameterType="pd">
		insert into
		<include refid="tableName"></include>
		(
		<include refid="Field2"></include>
		) values (
		<include refid="FieldValue"></include>
		)
	</insert>

	<!--批量新增-->
	<insert id="addBatch" parameterType="list" useGeneratedKeys="false">
		INSERT ALL
		<foreach item="item" index="index" collection="list">
		into
		<include refid="tableName"></include>
		(
		<include refid="Field2"></include>
		)values (
			#{item.ID,jdbcType=VARCHAR},
			#{item.MBID,jdbcType=VARCHAR},
			#{item.ROADPARTID,jdbcType=VARCHAR}
			)
		</foreach>
				SELECT 1 FROM DUAL
	</insert>

<!--	&lt;!&ndash;批量新增&ndash;&gt;-->
<!--	<insert id="addBatch" parameterType="list" useGeneratedKeys="false">-->

<!--		insert into-->
<!--		<include refid="tableName"></include>-->
<!--		(-->
<!--		<include refid="Field2"></include>-->
<!--		)-->
<!--		<foreach collection="list" item="item" separator="union"  open="select ID,MBID,ROADPARTID from(" close=")" >-->
<!--			select #{item.ID,jdbcType=VARCHAR} ID,#{item.MBID,jdbcType=VARCHAR} MBID,#{item.ROADPARTID,jdbcType=VARCHAR} ROADPARTID from dual-->
<!--		</foreach>-->
<!--	</insert>-->

	<!-- 删除-->
	<delete id="delete" parameterType="pd">
		delete from
		<include refid="tableName"></include>
		where
			"ID" = #{ID}
	</delete>

	<!-- 修改 -->
	<update id="edit" parameterType="pd">
		update
		<include refid="tableName"></include>
			set
				"ID" = #{ID},
				"MBID" = #{MBID},
				"ROADPARTID" = #{ROADPARTID}
			where
				"ID" = #{ID}
	</update>

	<!-- 通过模板ID获取路段id-->
	<select id="findByMBID" parameterType="String" resultType="String">
		select
		f."ROADPARTID"
		from
		<include refid="tableName"></include> f
		where
		f."MBID" = #{MBID}
	</select>

	<!-- 通过道路ID获取数据 -->
	<select id="getById" parameterType="pd" resultType="String">
		select
		<include refid="Field"></include>
		from
		<include refid="tableName"></include> f
		where
		f."ROADPARTID" = #{ROADPARTID}
		and
		f."MBID" = #{MBID}
	</select>

	<!-- 列表 -->
	<select id="datalistPage" parameterType="page" resultType="pd">
		select
		<include refid="Field"></include>
		from
		<include refid="tableName"></include> f
		where 1=1
		<if test="pd.KEYWORDS != null and pd.KEYWORDS != ''"><!-- 关键词检索 -->
			and
				(
				<!--	根据需求自己加检索条件
					字段1 LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
					 or
					字段2 LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
				-->
				)
		</if>
	</select>

	<!-- 列表(全部) -->
	<select id="listAll" parameterType="pd" resultType="pd">
		select
		<include refid="Field"></include>
		from
		<include refid="tableName"></include> f
	</select>

	<!-- 批量删除 -->
	<delete id="deleteAll" parameterType="String">
		delete from
		<include refid="tableName"></include>
		where
			"PASLICENSEMOULDINFO_ID" in
		<foreach item="item" index="index" collection="array" open="(" separator="," close=")">
                 #{item}
		</foreach>
	</delete>

	<!-- fh313596790qq(青苔) -->
</mapper>
