<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD SQL Map Config 3.0//EN"  
	"http://mybatis.org/dtd/mybatis-3-config.dtd">

<configuration>
	<settings>
		<setting name="cacheEnabled" value="true"/>
		<setting name="callSettersOnNulls" value="true"/>
	</settings>

	<typeAliases>
		<typeAlias type="org.fh.entity.PageData" alias="pd"/>
		<typeAlias type="org.fh.entity.Page" alias="page"/>
		<typeAlias type="org.fh.entity.system.User" alias="user"/>
		<typeAlias type="org.fh.entity.system.Menu" alias="menu"/>
		<typeAlias type="org.fh.entity.system.Role" alias="role"/>
		<typeAlias type="org.fh.entity.system.Dictionaries" alias="dictionaries"/>
	</typeAliases>

	<plugins>
		<plugin interceptor="org.fh.plugins.PagePlugin">
			<property name="dialect" value="oracle"/>
			<property name="pageSqlId" value=".*listPage.*"/>
		</plugin>
	</plugins>

</configuration>