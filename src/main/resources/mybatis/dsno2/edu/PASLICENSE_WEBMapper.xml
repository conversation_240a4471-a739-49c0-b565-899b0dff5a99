<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno2.edu.PASLICENSE_WEBMapper">
	
	<!--表名 -->
	<sql id="tableName">
		"PASLICENSE_WEB"
	</sql>
	
	<!--数据字典表名 -->
	<sql id="dicTableName">
		"SYS_DICTIONARIES"
	</sql>
	
	<!-- 字段 -->
	<sql id="Field">
		f."XH",	
		f."TXZLX",	
		f."TXZHM",	
		f."HPZL",	
		f."HPHM",	
		f."CLLX",	
		f."SYR",	
		f."LX",	
		f."YXRQS",	
		f."YXRQZ",	
		f."J<PERSON>",	
		f."GXSJ",	
		f."DJRQ",	
		f."SPR",	
		f."SPBJ",	
		f."SPRQ",	
		f."GFSD",	
		f."YXSJ",	
		f."YXRQ",	
		f."LDJZ",	
		f."BZ",	
		f."DYBJ",	
		f."ZT",	
		f."YLZD1",	
		f."YLZD2",	
		f."YLZD3",	
		f."YLZD4",	
		f."ZX",	
		f."JUJUE",	
		f."JUETEXT",	
		f."SPONE",	
		f."SPONEPEOPLE",	
		f."SPTWO",	
		f."SPTWOPEOPLE",	
		f."SPTHREE",	
		f."SPTHREEPEOPLE",	
		f."SPBJ2",	
		f."LSSP",	
		f."LSSPBJ",	
		f."SBSD",	
		f."LY",	
		f."YSSBSD",	
		f."LSTXZ",	
		f."TXSBBH",	
		f."DLJB",	
		f."TXJX",	
		f."YGZ",	
		f."GZSJ",	
		f."YLZD5",	
		f."YLZD6"
	</sql>
	
	<!-- 字段用于新增 -->
	<sql id="Field2">
		"XH",	
		"TXZLX",	
		"TXZHM",	
		"HPZL",	
		"HPHM",	
		"CLLX",	
		"SYR",	
		"LX",	
		"YXRQS",	
		"YXRQZ",	
		"JBR",	
		"GXSJ",	
		"DJRQ",	
		"SPR",	
		"SPBJ",	
		"SPRQ",	
		"GFSD",	
		"YXSJ",	
		"YXRQ",	
		"LDJZ",	
		"BZ",	
		"DYBJ",	
		"ZT",	
		"YLZD1",	
		"YLZD2",	
		"YLZD3",	
		"YLZD4",	
		"ZX",	
		"JUJUE",	
		"JUETEXT",	
		"SPONE",	
		"SPONEPEOPLE",	
		"SPTWO",	
		"SPTWOPEOPLE",	
		"SPTHREE",	
		"SPTHREEPEOPLE",	
		"SPBJ2",	
		"LSSP",	
		"LSSPBJ",	
		"SBSD",	
		"LY",	
		"YSSBSD",	
		"LSTXZ",	
		"TXSBBH",	
		"DLJB",	
		"TXJX",	
		"YGZ",	
		"GZSJ",	
		"YLZD5",	
		"YLZD6"
	</sql>
	
	<!-- 字段值 -->
	<sql id="FieldValue">
		#{XH},	
		#{TXZLX},	
		#{TXZHM},	
		#{HPZL},	
		#{HPHM},	
		#{CLLX},	
		#{SYR},	
		#{LX},	
		#{YXRQS},	
		#{YXRQZ},	
		#{JBR},	
		#{GXSJ},	
		#{DJRQ},	
		#{SPR},	
		#{SPBJ},	
		#{SPRQ},	
		#{GFSD},	
		#{YXSJ},	
		#{YXRQ},	
		#{LDJZ},	
		#{BZ},	
		#{DYBJ},	
		#{ZT},	
		#{YLZD1},	
		#{YLZD2},	
		#{YLZD3},	
		#{YLZD4},	
		#{ZX},	
		#{JUJUE},	
		#{JUETEXT},	
		#{SPONE},	
		#{SPONEPEOPLE},	
		#{SPTWO},	
		#{SPTWOPEOPLE},	
		#{SPTHREE},	
		#{SPTHREEPEOPLE},	
		#{SPBJ2},	
		#{LSSP},	
		#{LSSPBJ},	
		#{SBSD},	
		#{LY},	
		#{YSSBSD},	
		#{LSTXZ},	
		#{TXSBBH},	
		#{DLJB},	
		#{TXJX},	
		#{YGZ},	
		#{GZSJ},	
		#{YLZD5},	
		#{YLZD6}
	</sql>
	
	<!-- 新增-->
	<insert id="save" parameterType="pd">
		insert into
		<include refid="tableName"></include>
		(
		<include refid="Field2"></include>
		) values (
		<include refid="FieldValue"></include>
		)
	</insert>
	
	<!-- 删除-->
	<delete id="delete" parameterType="pd">
		delete from
		<include refid="tableName"></include>
		where 
			"XH" = #{XH}
	</delete>
	
	<!-- 修改 -->
	<update id="edit" parameterType="pd">
		update
		<include refid="tableName"></include>
			set 
				"XH" = #{XH},	
				"TXZLX" = #{TXZLX},	
				"TXZHM" = #{TXZHM},	
				"HPZL" = #{HPZL},	
				"HPHM" = #{HPHM},	
				"CLLX" = #{CLLX},	
				"SYR" = #{SYR},	
				"LX" = #{LX},	
				"YXRQS" = #{YXRQS},	
				"YXRQZ" = #{YXRQZ},	
				"JBR" = #{JBR},	
				"GXSJ" = #{GXSJ},	
				"DJRQ" = #{DJRQ},	
				"SPR" = #{SPR},	
				"SPBJ" = #{SPBJ},	
				"SPRQ" = #{SPRQ},	
				"GFSD" = #{GFSD},	
				"YXSJ" = #{YXSJ},	
				"YXRQ" = #{YXRQ},	
				"LDJZ" = #{LDJZ},	
				"BZ" = #{BZ},	
				"DYBJ" = #{DYBJ},	
				"ZT" = #{ZT},	
				"YLZD1" = #{YLZD1},	
				"YLZD2" = #{YLZD2},	
				"YLZD3" = #{YLZD3},	
				"YLZD4" = #{YLZD4},	
				"ZX" = #{ZX},	
				"JUJUE" = #{JUJUE},	
				"JUETEXT" = #{JUETEXT},	
				"SPONE" = #{SPONE},	
				"SPONEPEOPLE" = #{SPONEPEOPLE},	
				"SPTWO" = #{SPTWO},	
				"SPTWOPEOPLE" = #{SPTWOPEOPLE},	
				"SPTHREE" = #{SPTHREE},	
				"SPTHREEPEOPLE" = #{SPTHREEPEOPLE},	
				"SPBJ2" = #{SPBJ2},	
				"LSSP" = #{LSSP},	
				"LSSPBJ" = #{LSSPBJ},	
				"SBSD" = #{SBSD},	
				"LY" = #{LY},	
				"YSSBSD" = #{YSSBSD},	
				"LSTXZ" = #{LSTXZ},	
				"TXSBBH" = #{TXSBBH},	
				"DLJB" = #{DLJB},	
				"TXJX" = #{TXJX},	
				"YGZ" = #{YGZ},	
				"GZSJ" = #{GZSJ},	
				"YLZD5" = #{YLZD5},	
				"YLZD6" = #{YLZD6}
			where 
				"XH" = #{XH}
	</update>
	
	<!-- 通过ID获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include> f
		where 
			f."XH" = #{XH}
	</select>
	
	<!-- 列表 -->
	<select id="datalistPage" parameterType="page" resultType="pd">
		select
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include> f
		where 1=1
		<if test="pd.KEYWORDS != null and pd.KEYWORDS != ''"><!-- 关键词检索 -->
			and
				(
				<!--	根据需求自己加检索条件
					字段1 LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
					 or 
					字段2 LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%') 
				-->
				)
		</if>
	</select>
	
	<!-- 列表(全部) -->
	<select id="listAll" parameterType="pd" resultType="pd">
		select
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include> f
	</select>
	
	<!-- 批量删除 -->
	<delete id="deleteAll" parameterType="String">
		delete from
		<include refid="tableName"></include>
		where 
			"XH" in
		<foreach item="item" index="index" collection="array" open="(" separator="," close=")">
                 #{item}
		</foreach>
	</delete>
	
	<!-- fh313596790qq(青苔) -->
</mapper>