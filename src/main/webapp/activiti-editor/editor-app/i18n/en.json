{"HEADER.BRAND": "Activiti 设计器", "HEADER.BRAND_TAGLINE": "powered by Alfresco", "PAGE.HEADER": "业务流程的细节", "ACTION.OK": "确认", "ACTION.SAVE": "保存", "ACTION.SAVE-AND-CLOSE": "保存并关闭", "ACTION.SEND": "发送", "ACTION.CANCEL": "取消", "ACTION.SELECT": "选择", "ACTION.ADD": "添加", "ACTION.REMOVE": "移除", "ACTION.MOVE.UP": "上移", "ACTION.MOVE.DOWN": "下移", "ACTION.FHTS": " 数据保存通过键盘输入事件，若选择待办人时,再多打个字符后删除，方可保存 ", "ACTION.FUSER": "用户", "ACTION.FROLE": "角色", "MAIN_NAVIGATION_ORCHESTRATIONS": "业务流程", "MAIN_NAVIGATION_DISPATCH_RULES": "调度规则", "MAIN_NAVIGATION_ASSET_GROUPS": "审批组", "MAIN_NAVIGATION_SOLUTIONS": "解决", "TOOLBAR.ACTION.CLOSE": "关闭编辑器并返回到概览页面", "TOOLBAR.ACTION.SAVE": "保存模型", "TOOLBAR.ACTION.VALIDATE": "校验模型", "TOOLBAR.ACTION.CUT": "剪切 (在业务流程中选择一个或多个元素)", "TOOLBAR.ACTION.COPY": "复制 (在业务流程中选择一个或多个元素)", "TOOLBAR.ACTION.PASTE": "粘贴", "TOOLBAR.ACTION.DELETE": "删除选定的元素", "TOOLBAR.ACTION.UNDO": "撤消", "TOOLBAR.ACTION.REDO": "重做", "TOOLBAR.ACTION.ZOOMIN": "放大", "TOOLBAR.ACTION.ZOOMOUT": "缩小", "TOOLBAR.ACTION.ZOOMACTUAL": "缩放到实际大小", "TOOLBAR.ACTION.ZOOMFIT": "适应屏幕", "TOOLBAR.ACTION.MOVE": "移动", "TOOLBAR.ACTION.IMPORT": "导入", "TOOLBAR.ACTION.EXPORT": "导出", "TOOLBAR.ACTION.BENDPOINT.ADD": "为选定的流程连线添加弯曲点", "TOOLBAR.ACTION.BENDPOINT.REMOVE": "为选定的流程连线删除弯曲点", "TOOLBAR.ACTION.ALIGNHORIZONTAL": "水平对齐", "TOOLBAR.ACTION.ALIGNVERTICAL": "垂直对齐", "TOOLBAR.ACTION.SAMESIZE": "同样大小", "TOOLBAR.ACTION.HELP": "预览", "TOOLBAR.ACTION.FEEDBACK": "反馈", "KICKSTART.PROCESS_TOOLBAR.ACTION.SAVE": "保存模型", "KICKSTART.PROCESS_TOOLBAR.ACTION.VALIDATE": "校验模型", "KICKSTART.PROCESS_TOOLBAR.ACTION.HELP": "预览", "KICKSTART.PROCESS_TOOLBAR.ACTION.FEEDBACK": "反馈", "FORM_TOOLBAR.ACTION.SAVE": "保存模型", "FORM_TOOLBAR.ACTION.VALIDATE": "校验模型", "FORM_TOOLBAR.ACTION.HELP": "预览", "FORM_TOOLBAR.ACTION.FEEDBACK": "反馈", "APP_DEFINITION_TOOLBAR.ACTION.SAVE": "保存应用程序定义", "APP_DEFINITION_TOOLBAR.ACTION.VALIDATE": "校验应用程序定义", "APP_DEFINITION_TOOLBAR.ACTION.HELP": "预览", "APP_DEFINITION_TOOLBAR.ACTION.FEEDBACK": "反馈", "BUTTON.ACTION.DELETE.TOOLTIP": "从模型中删除元素", "BUTTON.ACTION.MORPH.TOOLTIP": "更改元素类型", "ELEMENT.AUTHOR": "作者", "ELEMENT.DATE_CREATED": "创建日期", "ELEMENT.SELECTED_EMPTY_TITLE": "(输入名称)", "PROPERTY.REMOVED": "清除", "PROPERTY.EMPTY": "", "PROPERTY.PROPERTY.EDIT.TITLE": "修改 \"{{title}}\"", "PROPERTY.FEEDBACK.TITLE": "请填写您的反馈", "PROPERTY.ASSIGNMENT.TITLE": "指派", "PROPERTY.ASSIGNMENT.TYPE": "类型", "PROPERTY.ASSIGNMENT.TYPE.IDENTITYSTORE": "身份储存器", "PROPERTY.ASSIGNMENT.TYPE.STATIC": "静态值", "PROPERTY.ASSIGNMENT.ASSIGNEE": "代理人", "PROPERTY.ASSIGNMENT.MATCHING": "使用上下方向键选择并按回车键确认或使用鼠标", "PROPERTY.ASSIGNMENT.ASSIGNEE_PLACEHOLDER": "请输入代理人", "PROPERTY.ASSIGNMENT.EMPTY": "无代理人", "PROPERTY.ASSIGNMENT.ASSIGNEE_DISPLAY": "代理人 {{assignee}}", "PROPERTY.ASSIGNMENT.CANDIDATE_USERS_DISPLAY": "{{length}} 候选人", "PROPERTY.ASSIGNMENT.CANDIDATE_USERS": "候选人", "PROPERTY.ASSIGNMENT.CANDIDATE_GROUPS_DISPLAY": "{{length}} 候选组", "PROPERTY.ASSIGNMENT.CANDIDATE_GROUPS": "候选组", "PROPERTY.ASSIGNMENT.USER_IDM_DISPLAY": "用户 {{firstName}} {{lastName}}", "PROPERTY.ASSIGNMENT.USER_IDM_EMAIL_DISPLAY": "用户 {{email}}", "PROPERTY.ASSIGNMENT.IDM_EMPTY": "发起人", "PROPERTY.ASSIGNMENT.IDM.TYPE": "任务", "PROPERTY.ASSIGNMENT.IDM.NO_CANDIDATE_USERS": "没有选择候选人...", "PROPERTY.ASSIGNMENT.IDM.NO_CANDIDATE_GROUPS": "没有选择候选组...", "PROPERTY.ASSIGNMENT.IDM.DROPDOWN.INITIATOR": "分派给发起人", "PROPERTY.ASSIGNMENT.IDM.DROPDOWN.USER": "分派给一个用户", "PROPERTY.ASSIGNMENT.IDM.DROPDOWN.USERS": "候选人", "PROPERTY.ASSIGNMENT.IDM.DROPDOWN.GROUPS": "候选组", "PROPERTY.ASSIGNMENT.EMAIL.HELP": "键入一个电子邮件地址，然后按回车键继续", "PROPERTY.EXECUTIONLISTENERS.DISPLAY": "{{length}} 执行监听", "PROPERTY.EXECUTIONLISTENERS.EMPTY": "没有执行侦听器配置", "PROPERTY.EXECUTIONLISTENERS.EVENT": "事件", "PROPERTY.EXECUTIONLISTENERS.CLASS": "类", "PROPERTY.EXECUTIONLISTENERS.CLASS.PLACEHOLDER": "输入类名", "PROPERTY.EXECUTIONLISTENERS.EXPRESSION": "表达式", "PROPERTY.EXECUTIONLISTENERS.EXPRESSION.PLACEHOLDER": "输入表达式", "PROPERTY.EXECUTIONLISTENERS.DELEGATEEXPRESSION": "委托表达式", "PROPERTY.EXECUTIONLISTENERS.DELEGATEEXPRESSION.PLACEHOLDER": "输入委托表达式", "PROPERTY.EXECUTIONLISTENERS.UNSELECTED": "没有配置执行监听", "PROPERTY.EXECUTIONLISTENERS.FIELDS.NAME": "名称", "PROPERTY.EXECUTIONLISTENERS.FIELDS.NAME.PLACEHOLDER": "输入名称", "PROPERTY.EXECUTIONLISTENERS.FIELDS.EXPRESSION": "表达式", "PROPERTY.EXECUTIONLISTENERS.FIELDS.EXPRESSION.PLACEHOLDER": "输入表达式", "PROPERTY.EXECUTIONLISTENERS.FIELDS.STRINGVALUE": "字符串", "PROPERTY.EXECUTIONLISTENERS.FIELDS.STRINGVALUE.PLACEHOLDER": "输入字符串", "PROPERTY.EXECUTIONLISTENERS.FIELDS.STRING": "字符串", "PROPERTY.EXECUTIONLISTENERS.FIELDS.STRING.PLACEHOLDER": "输入字符串", "PROPERTY.EXECUTIONLISTENERS.FIELDS.IMPLEMENTATION": "实现类", "PROPERTY.EXECUTIONLISTENERS.FIELDS.EMPTY": "没有选择字段", "PROPERTY.FIELDS": "{{length}} 字段", "PROPERTY.FIELDS.EMPTY": "没有选择字段", "PROPERTY.FIELDS.NAME": "名称", "PROPERTY.FIELDS.NAME.PLACEHOLDER": "输入名称", "PROPERTY.FIELDS.EXPRESSION": "表达式", "PROPERTY.FIELDS.EXPRESSION.PLACEHOLDER": "输入表达式", "PROPERTY.FIELDS.STRINGVALUE": "字符串", "PROPERTY.FIELDS.STRINGVALUE.PLACEHOLDER": "输入字符串", "PROPERTY.FIELDS.STRING": "字符串", "PROPERTY.FIELDS.STRING.PLACEHOLDER": "输入字符串", "PROPERTY.FIELDS.IMPLEMENTATION": "实现类", "PROPERTY.FIELDS.UNSELECTED": "没有选择字段", "PROPERTY.FORMPROPERTIES.VALUE": "{{length}} 表单属性", "PROPERTY.FORMPROPERTIES.EMPTY": "没有配置表单", "PROPERTY.FORMPROPERTIES.ID": "活动编号", "PROPERTY.FORMPROPERTIES.ID.PLACEHOLDER": "输入活动编号", "PROPERTY.FORMPROPERTIES.NAME": "名称", "PROPERTY.FORMPROPERTIES.NAME.PLACEHOLDER": "输入名称", "PROPERTY.FORMPROPERTIES.TYPE": "类型", "PROPERTY.FORMPROPERTIES.DATEPATTERN": "时间选择框", "PROPERTY.FORMPROPERTIES.DATEPATTERN.PLACEHOLDER": "输入日期", "PROPERTY.FORMPROPERTIES.VALUES": "值", "PROPERTY.FORMPROPERTIES.ENUMVALUES.EMPTY": "没有选择的枚举值", "PROPERTY.FORMPROPERTIES.VALUES.ID": "编号", "PROPERTY.FORMPROPERTIES.VALUES.NAME": "名称", "PROPERTY.FORMPROPERTIES.VALUES.ID.PLACEHOLDER": "输入值的编号", "PROPERTY.FORMPROPERTIES.VALUES.NAME.PLACEHOLDER": "输入值的名称", "PROPERTY.FORMPROPERTIES.EXPRESSION": "表达式", "PROPERTY.FORMPROPERTIES.EXPRESSION.PLACEHOLDER": "输入表达式", "PROPERTY.FORMPROPERTIES.VARIABLE": "变量", "PROPERTY.FORMPROPERTIES.VARIABLE.PLACEHOLDER": "输入变量", "PROPERTY.FORMPROPERTIES.REQUIRED": "必输", "PROPERTY.FORMPROPERTIES.READABLE": "可读", "PROPERTY.FORMPROPERTIES.WRITABLE": "可写", "PROPERTY.INPARAMETERS.VALUE": "{{length}} 输入参数", "PROPERTY.INPARAMETERS.EMPTY": "没有配置输入参数", "PROPERTY.OUTPARAMETERS.VALUE": "{{length}} 返回参数", "PROPERTY.OUTPARAMETERS.EMPTY": "没有配置返回参数", "PROPERTY.PARAMETER.SOURCE": "源", "PROPERTY.PARAMETER.SOURCE.PLACEHOLDER": "输入源", "PROPERTY.PARAMETER.SOURCEEXPRESSION": "源表达式", "PROPERTY.PARAMETER.SOURCEEXPRESSION.PLACEHOLDER": "输入源表达式", "PROPERTY.PARAMETER.TARGET": "目标", "PROPERTY.PARAMETER.TARGET.PLACEHOLDER": "输入目标", "PROPERTY.PARAMETER.EMPTY": "没有选择参数", "PROPERTY.SUBPROCESSREFERENCE.EMPTY": "没有引用子流程", "PROPERTY.SUBPROCESSREFERENCE.TITLE": "引用错误的子流程", "PROPERTY.SUBPROCESSREFERENCE.ERROR.SUBPROCESS": "子流程加载错误.请稍后再试", "PROPERTY.SUBPROCESSREFERENCE.FOLDER.ROOT": "文件夹", "PROPERTY.SUBPROCESSREFERENCE.FOLDER.LOADING": "文件夹加载中...", "PROPERTY.SUBPROCESSREFERENCE.FOLDER.EMPTY": "文件夹未包含子文件夹", "PROPERTY.SUBPROCESSREFERENCE.SUBPROCESS.LOADING": "子流程加载中...", "PROPERTY.SUBPROCESSREFERENCE.SUBPROCESS.EMPTY": "文件夹包含子文件夹", "PROPERTY.FORMREFERENCE.EMPTY": "没有引用表单", "PROPERTY.FORMREFERENCE.TITLE": "表单引用", "PROPERTY.FORMREFERENCE.ERROR.FORM": "表单加载错误.请稍后再试!", "PROPERTY.FORMREFERENCE.FOLDER.ROOT": "文件夹", "PROPERTY.FORMREFERENCE.FOLDER.LOADING": "文件夹加载中...", "PROPERTY.FORMREFERENCE.FOLDER.EMPTY": "文件夹未包含子文件夹", "PROPERTY.FORMREFERENCE.FORM.LOADING": "表单加载中...", "PROPERTY.FORMREFERENCE.FORM.EMPTY": "文件夹包含子文件夹", "PROPERTY.TASKLISTENERS.VALUE": "{{length}} 任务监听", "PROPERTY.TASKLISTENERS.EMPTY": "未配置任务监听", "PROPERTY.TASKLISTENERS.EVENT": "事件", "PROPERTY.TASKLISTENERS.CLASS": "类", "PROPERTY.TASKLISTENERS.CLASS.PLACEHOLDER": "输入类名", "PROPERTY.TASKLISTENERS.EXPRESSION": "表达式", "PROPERTY.TASKLISTENERS.EXPRESSION.PLACEHOLDER": "请输入表达式", "PROPERTY.TASKLISTENERS.DELEGATEEXPRESSION": "委托表达式", "PROPERTY.TASKLISTENERS.DELEGATEEXPRESSION.PLACEHOLDER": "请输入委托表达式", "PROPERTY.TASKLISTENERS.UNSELECTED": "没有选择任务监听", "PROPERTY.TASKLISTENERS.FIELDS.NAME": "名称", "PROPERTY.TASKLISTENERS.FIELDS.NAME.PLACEHOLDER": "请输入名称", "PROPERTY.TASKLISTENERS.FIELDS.EXPRESSION": "表达式", "PROPERTY.TASKLISTENERS.FIELDS.EXPRESSION.PLACEHOLDER": "请输入表达式", "PROPERTY.TASKLISTENERS.FIELDS.STRINGVALUE": "字符串", "PROPERTY.TASKLISTENERS.FIELDS.STRINGVALUE.PLACEHOLDER": "请输入字符串", "PROPERTY.TASKLISTENERS.FIELDS.STRING": "字符串", "PROPERTY.TASKLISTENERS.FIELDS.STRING.PLACEHOLDER": "请输入字符串", "PROPERTY.TASKLISTENERS.FIELDS.IMPLEMENTATION": "执行", "PROPERTY.TASKLISTENERS.FIELDS.EMPTY": "未选择字段", "PROPERTY.EVENTLISTENERS.DISPLAY": "{{length}} 事件监听", "PROPERTY.EVENTLISTENERS.EMPTY": "未配置事件侦听器", "PROPERTY.EVENTLISTENERS.EVENTS": "事件", "PROPERTY.EVENTLISTENERS.RETHROW": "抛出事件?", "PROPERTY.EVENTLISTENERS.CLASS": "类", "PROPERTY.EVENTLISTENERS.CLASS.PLACEHOLDER": "输入类名", "PROPERTY.EVENTLISTENERS.DELEGATEEXPRESSION": "委托表达式", "PROPERTY.EVENTLISTENERS.DELEGATEEXPRESSION.PLACEHOLDER": "请输入委托表达式", "PROPERTY.EVENTLISTENERS.ENTITYTYPE": "类型", "PROPERTY.EVENTLISTENERS.ENTITYTYPE.PLACEHOLDER": "请输入类型", "PROPERTY.EVENTLISTENERS.RETHROWTYPE": "抛出事件类型", "PROPERTY.EVENTLISTENERS.ERRORCODE": "错误代码", "PROPERTY.EVENTLISTENERS.ERRORCODE.PLACEHOLDER": "请输入错误代码", "PROPERTY.EVENTLISTENERS.MESSAGENAME": "消息名称", "PROPERTY.EVENTLISTENERS.MESSAGENAME.PLACEHOLDER": "请输入消息名称", "PROPERTY.EVENTLISTENERS.SIGNALNAME": "信号名称", "PROPERTY.EVENTLISTENERS.SIGNALNAME.PLACEHOLDER": "请输入信号名称", "PROPERTY.EVENTLISTENERS.UNSELECTED": "没有选择事件监听器", "PROPERTY.SIGNALDEFINITIONS.DISPLAY": "{{length}} 信号定义", "PROPERTY.SIGNALDEFINITIONS.EMPTY": "没有配置信号定义", "PROPERTY.SIGNALDEFINITIONS.SCOPE-GLOBAL": "全局", "PROPERTY.SIGNALDEFINITIONS.SCOPE-PROCESSINSTANCE": "流程初始化", "PROPERTY.SIGNALDEFINITIONS.ID": "编号", "PROPERTY.SIGNALDEFINITIONS.NAME": "名称", "PROPERTY.SIGNALDEFINITIONS.SCOPE": "范围", "PROPERTY.MESSAGEDEFINITIONS.DISPLAY": "{{length}} 消息定义", "PROPERTY.MESSAGEDEFINITIONS.EMPTY": "没有配置消息定义", "PROPERTY.MESSAGEDEFINITIONS.ID": "编号", "PROPERTY.MESSAGEDEFINITIONS.NAME": "名称", "PROPERTY.SEQUENCEFLOW.ORDER.EMPTY": "没有确定顺序流排序", "PROPERTY.SEQUENCEFLOW.ORDER.NOT.EMPTY": "顺序流排序", "PROPERTY.SEQUENCEFLOW.ORDER.NO.OUTGOING.SEQUENCEFLOW.FOUND": "没有输出顺序流.", "PROPERTY.SEQUENCEFLOW.ORDER.DESCRIPTION": "不能设置已经被使用的编号:", "PROPERTY.SEQUENCEFLOW.ORDER.SEQUENCEFLOW.VALUE": "顺序流 {{targetType}} {{targetTitle}}", "PROPERTY.SEQUENCEFLOW.CONDITION.TITLE": "条件", "PROPERTY.SEQUENCEFLOW.CONDITION.TYPE.TITLE": "条件类型", "PROPERTY.SEQUENCEFLOW.CONDITION.TYPE.VARIABLE": "选择的变量", "PROPERTY.SEQUENCEFLOW.CONDITION.TYPE.STATIC": "静态值", "PROPERTY.SEQUENCEFLOW.CONDITION.STATIC": "条件表达式", "PROPERTY.SEQUENCEFLOW.CONDITION.STATIC_PLACEHOLDER": "输入表达式值", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.TYPE": "变量类型", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.NO-CONDITION": "没有条件", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.FORM-FIELD": "表单字段", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.FORM-OUTCOME": "表单输出", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.SELECT-FIELD": "选择的字段", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.NO-FIELDS-AVAILABLE": "没有字段变量", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.SELECT-FORM": "选择表单", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.NO-FORMS-AVAILABLE": "没有表单变量", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.SELECT-OPERATOR": "选择操作", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.EQUALS": "等于", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.NOTEQUALS": "不等于", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.LESSTHAN": "小于", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.GREATERTHAN": "大于", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.SELECT-OUTCOME": "选择输出", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.NO-OUTCOMES-AVAILABLE": "没有输出变量", "PROPERTY.SEQUENCEFLOW.CONDITION.NO-CONDITION-DISPLAY": "没有条件", "MODEL.SAVE.TITLE": "保存模型", "MODEL.NAME": "名称", "MODEL.DESCRIPTION": "描述", "MODEL.SAVE.NEWVERSION": "存为新版本? 这样你可以随时回到以前的版本", "MODEL.SAVE.COMMENT": "注释", "MODEL.SAVE.SAVING": "保存模型", "MODEL.LASTMODIFIEDDATE": "上次保存时间", "MODEL.SAVE.ERROR": "未知错误:保存失败!", "EVENT_TYPE.ACTIVITY.COMPENSATE.TOOLTIP": "一个活动被另外一个活动替代执行", "EVENT_TYPE.ACTIVITY.COMPLETED.TOOLTIP": "一个活动被成功的执行", "EVENT_TYPE.ACTIVITY.ERROR.RECEIVED.TOOLTIP": "在收到活动错误之前,活动已收到错误事件", "EVENT_TYPE.MEMBERSHIP.CREATED.TOOLTIP": "一个唯一的成员被创建", "EVENT_TYPE.MEMBERSHIP.DELETED.TOOLTIP": "一个唯一的成员被删除", "EVENT_TYPE.MEMBERSHIPS.DELETED.TOOLTIP": "所有成员都被删除.可能是由于没有事件被分配", "EVENT_TYPE.TASK.ASSIGNED.TOOLTIP": "在ENTITY_UPDATED事件抛出时，任务已经被分配", "EVENT_TYPE.TASK.COMPLETED.TOOLTIP": "在任务实体删除前任务已经被完成", "EVENT_TYPE.UNCAUGHT.BPMNERROR.TOOLTIP": "一个BPMN被抛出,但没有捕获", "EVENT_TYPE.VARIABLE.CREATED.TOOLTIP": "一个变量被创建", "EVENT_TYPE.VARIABLE.DELETED.TOOLTIP": "一个变量被删除", "EVENT_TYPE.VARIABLE.UPDATED.TOOLTIP": "一个变量被更新"}