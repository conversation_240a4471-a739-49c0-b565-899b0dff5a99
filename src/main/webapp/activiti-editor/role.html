<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>FH Admin</title>
    <!-- HTML5 Shim and Respond.js IE10 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 10]>
		<script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
		<script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
		<![endif]-->
    <!-- Meta -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui">
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="author" content="FH Admin QQ313596790" />

    <link rel="icon" href="../assets/images/favicon.ico" type="image/x-icon">
    <link rel="stylesheet" href="../assets/fonts/fontawesome/css/fontawesome-all.min.css">
    <link rel="stylesheet" href="../assets/plugins/animation/css/animate.min.css">
    <link rel="stylesheet" href="../assets/css/style.css">
    
    <!-- vue -->
	<script src="../assets/js/vue.js"></script>
    
</head>

<body style="background-color: white">
    
    <!-- [加载状态 ] start -->
    <div class="loader-bg">
        <div class="loader-track">
            <div class="loader-fill"></div>
        </div>
    </div>
    <!-- [ 加载状态  ] End -->

    <!-- [ 主内容区 ] start -->
        <div class="pcoded-wrapper" id="app">
            <div class="pcoded-content">
                <div class="pcoded-inner-content">
                    <div class="main-body">
                        <div class="page-wrapper">
                            <!-- [ Main Content ] start -->
                            <div class="row">
                            	<!-- [ Hover-table ] start -->
                                <div class="col-xl-12">
                                    <div class="card">
										<input type="hidden" v-model="RNUMBER" id="RNUMBER" />
										<!-- 检索条件  -->
										<div style="padding-left: 15px;padding-top: 15px;">
										<table>
											<tr>
												<td>
							                        <div class="input-group input-group-sm mb-3">
                                                     	<input class="form-control" type="text" v-model="KEYWORDS" placeholder="这里输入关键词" />
                                                 	</div>
												</td>
												<td style="vertical-align:top;padding-left:5px;">
													<a class="btn btn-light btn-sm" v-on:click="getList" style="width: 23px;height:30px;margin-top:1px;" title="检索">
														<i style="margin-top:-3px;margin-left: -6px;"  class="feather icon-search"></i>
													</a>
												</td>
												<td v-show="loading">
													<!-- [加载状态 ] start -->
											    	<div class="d-flex justify-content-center" style="margin-top:-10px;">
			                                            <div class="spinner-grow spinner-grow-sm" role="status">
			                                                <span class="sr-only">Loading...</span>
			                                            </div>
			                                        </div>
											    	<!-- [ 加载状态  ] End -->
												</td>
											</tr>
										</table>
										</div>
										<!-- 检索  -->
										<div class="card-block table-border-style" style="margin-top: -15px">
		                                	<div class="table-responsive">
												<table class="table table-hover">
													<thead>
														<tr>
															<th style="width:50px;" id="fhadminth">NO</th>
															<th>角色</th>
															<th>编号</th>
														</tr>
													</thead>
													<tbody>
														<!-- 开始循环 -->	
														<template v-for="(data,index) in varList">
															<tr v-on:dblclick="setRole(data.RNUMBER)" style="cursor:pointer;">
																<td scope="row">{{page.showCount*(page.currentPage-1)+index+1}}</td>
																<td>{{data.ROLE_NAME}}</td>
																<td>{{data.RNUMBER}}</td>
															</tr>
														</template>
														<tr v-show="varList.length==0">
															<td colspan="10">没有相关数据</td>
														</tr>
													</tbody>
												</table>
												<table style="width:100%;margin-top:15px;">
													<tr>
														<td style="vertical-align:top;"></td>
														<td style="vertical-align:top;"><div style="float: right;padding-top: 0px;margin-top: 0px;" v-html="page.pageStr"></div></td>
													</tr>
												</table>
												
											</div>
										</div>
									</div>
								</div>
                            </div>
                            <!-- [ Main Content ] end -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <!-- [ 主内容区 ] end -->
    
<script type="text/javascript" src="../assets/js/jquery-1.7.2.js"></script>
<script type="text/javascript" src="../assets/js/pre-loader.js"></script>
<script src="../assets/plugins/sweetalert/js/sweetalert.min.js"></script>
<!-- 表单验证提示 -->
<script src="../assets/js/jquery.tips.js"></script>

<script type="text/javascript">
var locat = (window.location+'').split('/'); 
var vm = new Vue({
	el: '#app',
	
	data:{
		varList: [],	//list
		page: [],		//分页类
		RNUMBER: '',	//编码
		KEYWORDS:'',	//检索关键词
		showCount: -1,	//每页显示条数(这个是系统设置里面配置的，初始为-1即可，固定此写法)
		currentPage: 1,	//当前页码
		loading:false	//加载状态
    },
    
	methods: {
		
        //初始执行
        init() {
        	if('activiti-editor'== locat[3]){locat =  locat[0]+'//'+locat[2];}else{locat =  locat[0]+'//'+locat[2]+'/'+locat[3];};
    		this.getList();
    		this.choiceTips();
        },
        
        //获取列表
        getList: function(){
        	this.loading = true;
        	$.ajax({
        		xhrFields: {
                    withCredentials: true
                },
        		type: "POST",
        		url: locat+'/role/roleListWindow?showCount='+this.showCount+'&currentPage='+this.currentPage,
        		data: {KEYWORDS:this.KEYWORDS,ROLE_ID:1,tm:new Date().getTime()},
        		dataType:"json",
        		success: function(data){
        		 if("success" == data.result){
        			 vm.varList = data.roleList;
        			 vm.page = data.page;
        			 vm.loading = false;
        		 }else if ("exception" == data.result){
                 	showException("选择角色",data.exception);//显示异常
                 }
        		}
        	}).done().fail(function(){
                swal("登录失效!", "请求服务器无响应，稍后再试", "warning");
            });
        },
        
    	//选定角色
    	setRole: function (RNUMBER){
    		this.RNUMBER = RNUMBER;
    		this.roleBinding();
    	},
    	
    	//选择角色
    	roleBinding: function (){
    		if('' == this.RNUMBER){
    			$("#fhadminth").tips({
    				side:3,
    	            msg:'没有选择任何角色',
    	            bg:'#AE81FF',
    	            time:2
    	        });
    		}else{
    			$("#RNUMBER").val(this.RNUMBER);
    			parent.Dialog.close();
    		}
    	},
    	
    	//提示双击选择
    	choiceTips: function (){
    		$("#fhadminth").tips({
    			side:1,
    	        msg:'双击选择角色',
    	        bg:'#AE81FF',
    	        time:6
    	    });
    	},
        
        //-----分页必用----start
        nextPage: function (page){
        	this.currentPage = page;
        	this.getList();
        },
        changeCount: function (value){
        	this.showCount = value;
        	this.getList();
        },
        toTZ: function (){
        	var toPaggeVlue = document.getElementById("toGoPage").value;
        	if(toPaggeVlue == ''){document.getElementById("toGoPage").value=1;return;}
        	if(isNaN(Number(toPaggeVlue))){document.getElementById("toGoPage").value=1;return;}
        	this.nextPage(toPaggeVlue);
        }
       //-----分页必用----end
		
	},
	
	mounted(){
        this.init();
    }
})
		
</script>
</html>