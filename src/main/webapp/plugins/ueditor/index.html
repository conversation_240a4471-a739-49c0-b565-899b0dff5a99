<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
        "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8"/>
    <script type="text/javascript" charset="utf-8">window.UEDITOR_HOME_URL = "";</script>
    <script type="text/javascript" charset="utf-8" src="ueditor.config.js"></script>
    <script type="text/javascript" charset="utf-8" src="ueditor.all.min.js"> </script>
    <script type="text/javascript" charset="utf-8" src="lang/zh-cn/zh-cn.js"></script>
    <script type="text/javascript" charset="utf-8" src="../../assets/js/jquery-1.7.2.js"></script>

    <style type="text/css">
        div{
            width:100%;
        }
    </style>
</head>
<body>
<div>
    <script id="editor" type="text/plain"></script>
</div>

<script type="text/javascript">

	var serverurl = '';
	var TYPE = '';
	
    //实例化编辑器
    var ue = UE.getEditor('editor');
    ue.ready(function() {
    	var fheight = getUrlKey('fheight');
    	ue.setHeight(fheight+0);
    	TYPE = getUrlKey('TYPE');
    	var lt = location+'';
    	serverurl = lt.replace('plugins/ueditor/index.html?TYPE='+TYPE+'&fheight='+fheight,'');
    	getHcContent(TYPE);
    })
    
    //编辑器失去焦点事件
    ue.addListener('blur',function(editor){
    	var CONTENT = getContent();
    	var CONTENT2 = getContentTxt();
    	//if('' == CONTENT2 || null ==CONTENT2)return;
    	$.ajax({
    		type: "POST",
    		url: serverurl+'ueditor/edit',
    		data: {CONTENT:CONTENT,CONTENT2:CONTENT2,TYPE:TYPE,tm:new Date().getTime()},
    		dataType:"json",
    		cache: false,
    		success: function(data){
    		 if("success" == data.result){
    		 }
    		}
    	})
    	
    });
    
  	//获取富文本内容
    function getHcContent(TYPE){
    	$.ajax({
    		type: "POST",
    		url: serverurl+'ueditor/getContent',
        	data: {TYPE:TYPE,tm:new Date().getTime()},
    		dataType:'json',
    		cache: false,
    		success: function(data){
    			if(data.result == 'success'){
                	ue.setContent(data.pd.CONTENT);	//带标签的
    			}else{
    				ue.setContent('');
    			}
    		}
    	});
    }
    
    //ueditor纯文本
	function getContentTxt() {
	    var arr = [];
	    arr.push(UE.getEditor('editor').getContentTxt());
	    return arr.join("");
	}
	//ueditor有标签文本
	function getContent() {
	    var arr = [];
	    arr.push(UE.getEditor('editor').getContent());
	    return arr.join("");
	}
	
	//根据url参数名称获取参数值
    function getUrlKey(name) {
        return decodeURIComponent(
            (new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(location.href) || [, ""])[1].replace(/\+/g, '%20')) || null;
    }

</script>
</body>
</html>