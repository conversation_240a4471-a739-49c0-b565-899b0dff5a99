<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>FH Admin</title>
    <!-- HTML5 Shim and Respond.js IE10 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 10]>
		<script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
		<script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
		<![endif]-->
    <!-- Meta -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui">
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="author" content="FH Admin QQ313596790" />

    <link rel="icon" href="../../../assets/images/favicon.ico" type="image/x-icon">
    <link rel="stylesheet" href="../../../assets/fonts/fontawesome/css/fontawesome-all.min.css">
    <link rel="stylesheet" href="../../../assets/plugins/animation/css/animate.min.css">
    <link rel="stylesheet" href="../../../assets/css/style.css">
    
    <!-- 日期插件 -->
    <link rel="stylesheet" href="../../../assets/plugins/material-datetimepicker/css/bootstrap-material-datetimepicker.css">
    
    <!-- select插件 -->
    <link rel="stylesheet" href="../../../assets/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="../../../assets/plugins/multi-select/css/multi-select.css">
    
    <!-- vue -->
	<script src="../../../assets/js-vue/vue.js"></script>
    <!--全局配置-->
    <script src="../../../assets/js-v/config.js"></script>

</head>

<body style="background-color: white">
    
    <!-- [加载状态 ] start -->
    <div class="loader-bg">
        <div class="loader-track">
            <div class="loader-fill"></div>
        </div>
    </div>
    <!-- [ 加载状态  ] End -->

    <!-- [ 主内容区 ] start -->
        <div class="pcoded-wrapper" id="app">
            <div class="pcoded-content">
                <div class="pcoded-inner-content">
                    <div class="main-body">
                        <div class="page-wrapper">
                            <!-- [ Main Content ] start -->
                            <div class="row">
					
								<div style="width: 100%;">
									<div id="showform">
														            <div class="input-group input-group-sm mb-3" style="margin-top: -10px;">
		                                    <div class="input-group-prepend">
		                                        <span class="input-group-text" style="width: 79px;"><span style="width: 100%;">序号</span></span>
		                                    </div>
		                                    <input type="text" class="form-control" ref="XH" id="XH" v-model="pd.XH" maxlength="16" placeholder="这里输入序号" title="序号">
		                                </div>
							            <div class="input-group input-group-sm mb-3" style="margin-top: -10px;">
		                                    <div class="input-group-prepend">
		                                        <span class="input-group-text" style="width: 79px;"><span style="width: 100%;">号牌种类</span></span>
		                                    </div>
		                                    <input type="text" class="form-control" ref="HPZL" id="HPZL" v-model="pd.HPZL" maxlength="2" placeholder="这里输入号牌种类" title="号牌种类">
		                                </div>
							            <div class="input-group input-group-sm mb-3" style="margin-top: -10px;">
		                                    <div class="input-group-prepend">
		                                        <span class="input-group-text" style="width: 79px;"><span style="width: 100%;">号牌号码</span></span>
		                                    </div>
		                                    <input type="text" class="form-control" ref="HPHM" id="HPHM" v-model="pd.HPHM" maxlength="15" placeholder="这里输入号牌号码" title="号牌号码">
		                                </div>
							            <div class="input-group input-group-sm mb-3" style="margin-top: -10px;">
		                                    <div class="input-group-prepend">
		                                        <span class="input-group-text" style="width: 79px;"><span style="width: 100%;">车辆类型</span></span>
		                                    </div>
		                                    <input type="text" class="form-control" ref="CLLX" id="CLLX" v-model="pd.CLLX" maxlength="3" placeholder="这里输入车辆类型" title="车辆类型">
		                                </div>
							            <div class="input-group input-group-sm mb-3" style="margin-top: -10px;">
		                                    <div class="input-group-prepend">
		                                        <span class="input-group-text" style="width: 79px;"><span style="width: 100%;">所有人</span></span>
		                                    </div>
		                                    <input type="text" class="form-control" ref="SYR" id="SYR" v-model="pd.SYR" maxlength="128" placeholder="这里输入所有人" title="所有人">
		                                </div>
							            <div class="input-group input-group-sm mb-3" style="margin-top: -10px;">
		                                    <div class="input-group-prepend">
		                                        <span class="input-group-text" style="width: 79px;"><span style="width: 100%;">状态</span></span>
		                                    </div>
		                                    <input type="text" class="form-control" ref="ZT" id="ZT" v-model="pd.ZT" maxlength="6" placeholder="这里输入状态" title="状态">
		                                </div>
							            <div class="input-group input-group-sm mb-3" style="margin-top: -10px;">
		                                    <div class="input-group-prepend">
		                                        <span class="input-group-text" style="width: 79px;"><span style="width: 100%;">备注</span></span>
		                                    </div>
		                                    <input type="text" class="form-control" ref="BZ" id="BZ" v-model="pd.BZ" maxlength="128" placeholder="这里输入备注" title="备注">
		                                </div>
							            <div class="input-group input-group-sm mb-3" style="margin-top: -10px;">
		                                    <div class="input-group-prepend">
		                                        <span class="input-group-text" style="width: 79px;"><span style="width: 100%;">更新日期</span></span>
		                                    </div>
		                                    <input type="text" class="form-control date" ref="GXRQ" id="GXRQ" maxlength="32" placeholder="这里输入更新日期" title="更新日期">
		                                </div>
							            <div class="input-group" style="margin-top:10px;background-color: white" >
							            	<span style="width: 100%;text-align: center;">
							            		<a class="btn btn-light btn-sm" v-on:click="save">保存</a>
							            		<a class="btn btn-light btn-sm" onclick="top.Dialog.close()">取消</a>
							            	</span>
							            </div>
							       </div>
						           <!-- [加载状态 ] start -->
								    <div id="jiazai" style="display:none;margin-top:50px;">
								    	<div class="d-flex justify-content-center">
	                                        <div class="spinner-border" style="width: 3rem; height: 3rem;" role="status">
                                                <span class="sr-only">Loading...</span>
                                            </div>
                                        </div>
                                    </div>
								    <!-- [ 加载状态  ] End -->
								</div>
	    
                            </div>
                            <!-- [ Main Content ] end -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <!-- [ 主内容区 ] end -->
    
<script type="text/javascript" src="../../../assets/js/jquery-1.7.2.js"></script>
<script type="text/javascript" src="../../../assets/js/pre-loader.js"></script>
<script src="../../../assets/plugins/sweetalert/js/sweetalert.min.js"></script>

<!-- 日期插件 -->
<script src="../../../assets/js/pages/moment-with-locales.min.js"></script>
<script src="../../../assets/plugins/material-datetimepicker/js/bootstrap-material-datetimepicker.js"></script>
<script src="../../../assets/js/pages/form-picker-custom.js"></script>

<!-- select插件 -->
<script src="../../../assets/plugins/select2/js/select2.full.min.js"></script>
<script src="../../../assets/plugins/multi-select/js/jquery.quicksearch.js"></script>
<script src="../../../assets/plugins/multi-select/js/jquery.multi-select.js"></script>
<script src="../../../assets/js/pages/form-select-custom.js"></script>

<!-- 表单验证提示 -->
<script src="../../../assets/js/jquery.tips.js"></script>

<script type="text/javascript">

var vm = new Vue({
	el: '#app',
	
	data:{
		BLACKLIST_ID: '',	//主键ID
		pd: [],						//存放字段参数
		msg:'add'
    },
	
	methods: {
		
        //初始执行
        init() {
        	var FID = this.getUrlKey('FID');	//当接收过来的FID不为null时,表示此页面是修改进来的
        	if(null != FID){
        		this.msg = 'edit';
        		this.BLACKLIST_ID = FID;
        		this.getData();
        	}
        	setTimeout(function(){
        		vm.getDict();
            },200);
        },
        
        //去保存
    	save: function (){
    		
			if(this.pd.XH == '' || this.pd.XH == undefined){
				$("#XH").tips({
					side:3,
		            msg:'请输入序号',
		            bg:'#AE81FF',
		            time:2
		        });
				this.pd.XH = '';
				this.$refs.XH.focus();
			return false;
			}
			if(this.pd.HPZL == '' || this.pd.HPZL == undefined){
				$("#HPZL").tips({
					side:3,
		            msg:'请输入号牌种类',
		            bg:'#AE81FF',
		            time:2
		        });
				this.pd.HPZL = '';
				this.$refs.HPZL.focus();
			return false;
			}
			if(this.pd.HPHM == '' || this.pd.HPHM == undefined){
				$("#HPHM").tips({
					side:3,
		            msg:'请输入号牌号码',
		            bg:'#AE81FF',
		            time:2
		        });
				this.pd.HPHM = '';
				this.$refs.HPHM.focus();
			return false;
			}
			if(this.pd.CLLX == '' || this.pd.CLLX == undefined){
				$("#CLLX").tips({
					side:3,
		            msg:'请输入车辆类型',
		            bg:'#AE81FF',
		            time:2
		        });
				this.pd.CLLX = '';
				this.$refs.CLLX.focus();
			return false;
			}
			if(this.pd.SYR == '' || this.pd.SYR == undefined){
				$("#SYR").tips({
					side:3,
		            msg:'请输入所有人',
		            bg:'#AE81FF',
		            time:2
		        });
				this.pd.SYR = '';
				this.$refs.SYR.focus();
			return false;
			}
			if(this.pd.ZT == '' || this.pd.ZT == undefined){
				$("#ZT").tips({
					side:3,
		            msg:'请输入状态',
		            bg:'#AE81FF',
		            time:2
		        });
				this.pd.ZT = '';
				this.$refs.ZT.focus();
			return false;
			}
			if(this.pd.BZ == '' || this.pd.BZ == undefined){
				$("#BZ").tips({
					side:3,
		            msg:'请输入备注',
		            bg:'#AE81FF',
		            time:2
		        });
				this.pd.BZ = '';
				this.$refs.BZ.focus();
			return false;
			}
			this.pd.GXRQ = $("#GXRQ").val();
			if(this.pd.GXRQ == '' || this.pd.GXRQ == undefined){
				$("#GXRQ").tips({
					side:3,
		            msg:'请输入更新日期',
		            bg:'#AE81FF',
		            time:2
		        });
				this.pd.GXRQ = '';
				this.$refs.GXRQ.focus();
			return false;
			}
    		
    		$("#showform").hide();
    		$("#jiazai").show();
    		
            //发送 post 请求提交保存
            $.ajax({
	            	xhrFields: {
	                    withCredentials: true
	                },
					type: "POST",
					url: httpurl+'blacklist/'+this.msg,
			    	data: {BLACKLIST_ID:this.BLACKLIST_ID,
				    XH:this.pd.XH,
				    HPZL:this.pd.HPZL,
				    HPHM:this.pd.HPHM,
				    CLLX:this.pd.CLLX,
				    SYR:this.pd.SYR,
				    ZT:this.pd.ZT,
				    BZ:this.pd.BZ,
				    GXRQ:this.pd.GXRQ,
			    	tm:new Date().getTime()},
					dataType:"json",
					success: function(data){
                        if("success" == data.result){
                        	swal("", "保存成功", "success");
                        	setTimeout(function(){
                        		top.Dialog.close();//关闭弹窗
                            },1000);
                        }else if ("exception" == data.result){
                        	showException("黑名单管理",data.exception);//显示异常
                        	$("#showform").show();
                    		$("#jiazai").hide();
                        }
                    }
				}).done().fail(function(){
                   swal("登录失效!", "请求服务器无响应，稍后再试", "warning");
                   $("#showform").show();
           		   $("#jiazai").hide();
                });
    	},
    	
    	//根据主键ID获取数据
    	getData: function(){
    		//发送 post 请求
            $.ajax({
            	xhrFields: {
                    withCredentials: true
                },
				type: "POST",
				url: httpurl+'blacklist/goEdit',
		    	data: {BLACKLIST_ID:this.BLACKLIST_ID,tm:new Date().getTime()},
				dataType:"json",
				success: function(data){
                     if("success" == data.result){
                     	vm.pd = data.pd;							//参数map
						$("#GXRQ").val(data.pd.GXRQ);
                     }else if ("exception" == data.result){
                     	showException("黑名单管理",data.exception);	//显示异常
                     	$("#showform").show();
                 		$("#jiazai").hide();
                     }
                  }
			}).done().fail(function(){
                  swal("登录失效!", "请求服务器无响应，稍后再试", "warning");
                  $("#showform").show();
          		  $("#jiazai").hide();
               });
    	},
    	
    	//获取数据字典数据
		getDict: function (){
		},
    	
    	//根据url参数名称获取参数值
        getUrlKey: function (name) {
            return decodeURIComponent(
                (new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(location.href) || [, ""])[1].replace(/\+/g, '%20')) || null;
        }
        
	},
	
	mounted(){
        this.init();
    }
})
				
</script>

</body>
</html>