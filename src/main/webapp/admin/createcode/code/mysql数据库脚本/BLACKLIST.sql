
SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- Table structure for `BLACKLIST`
-- ----------------------------
DROP TABLE IF EXISTS `BLACKLIST`;
CREATE TABLE `BLACKLIST` (
 		`BLACKLIST_ID` varchar(100) NOT NULL,
		`XH` varchar(16) DEFAULT NULL COMMENT '序号',
		`HPZL` varchar(2) DEFAULT NULL COMMENT '号牌种类',
		`HPHM` varchar(15) DEFAULT NULL COMMENT '号牌号码',
		`CLLX` varchar(3) DEFAULT NULL COMMENT '车辆类型',
		`SYR` varchar(128) DEFAULT NULL COMMENT '所有人',
		`ZT` varchar(6) DEFAULT NULL COMMENT '状态',
		`BZ` varchar(128) DEFAULT NULL COMMENT '备注',
		`GXRQ` varchar(7) DEFAULT NULL COMMENT '更新日期',
  		PRIMARY KEY (`BLACKLIST_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
