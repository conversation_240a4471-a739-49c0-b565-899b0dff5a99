-- ----------------------------
-- Table structure for "C##FHBOOT"."BLACKLIST"
-- ----------------------------
-- DROP TABLE "C##FHBOOT"."BLACKLIST";
CREATE TABLE "C##FHBOOT"."BLACKLIST" (
	"XH" VARCHAR2(16 BYTE) NULL ,
	"HPZL" VARCHAR2(2 BYTE) NULL ,
	"HPHM" VARCHAR2(15 BYTE) NULL ,
	"CLLX" VARCHAR2(3 BYTE) NULL ,
	"SYR" VARCHAR2(128 BYTE) NULL ,
	"ZT" VARCHAR2(6 BYTE) NULL ,
	"BZ" VARCHAR2(128 BYTE) NULL ,
	"GXRQ" VARCHAR2(7 BYTE) NULL ,
	"BLACKLIST_ID" VARCHAR2(100 BYTE) NOT NULL 
)
LOGGING
NOCOMPRESS
NOCACHE
;

COMMENT ON COLUMN "C##FHBOOT"."BLACKLIST"."XH" IS '序号';
COMMENT ON COLUMN "C##FHBOOT"."BLACKLIST"."HPZL" IS '号牌种类';
COMMENT ON COLUMN "C##FHBOOT"."BLACKLIST"."HPHM" IS '号牌号码';
COMMENT ON COLUMN "C##FHBOOT"."BLACKLIST"."CLLX" IS '车辆类型';
COMMENT ON COLUMN "C##FHBOOT"."BLACKLIST"."SYR" IS '所有人';
COMMENT ON COLUMN "C##FHBOOT"."BLACKLIST"."ZT" IS '状态';
COMMENT ON COLUMN "C##FHBOOT"."BLACKLIST"."BZ" IS '备注';
COMMENT ON COLUMN "C##FHBOOT"."BLACKLIST"."GXRQ" IS '更新日期';
COMMENT ON COLUMN "C##FHBOOT"."BLACKLIST"."BLACKLIST_ID" IS 'ID';

-- ----------------------------
-- Indexes structure for table BLACKLIST
-- ----------------------------

-- ----------------------------
-- Checks structure for table "C##FHBOOT"."BLACKLIST"

-- ----------------------------

ALTER TABLE "C##FHBOOT"."BLACKLIST" ADD CHECK ("BLACKLIST_ID" IS NOT NULL);

-- ----------------------------
-- Primary Key structure for table "C##FHBOOT"."BLACKLIST"
-- ----------------------------
ALTER TABLE "C##FHBOOT"."BLACKLIST" ADD PRIMARY KEY ("BLACKLIST_ID");
