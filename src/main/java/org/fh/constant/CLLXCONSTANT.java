package org.fh.constant;

import java.util.HashMap;
import java.util.Map;

public class CLLXCONSTANT {
    //车辆类型数组
    public final static Map<String, String> CLLX = new HashMap() {{

        put("B11", "重型普通半挂车");
        put("B12", "重型厢式半挂车");
        put("B13", "重型罐式半挂车");
        put("B14", "重型平板半挂车");
        put("B15", "重型集装箱半挂车");
        put("B16", "重型自卸半挂车");
        put("B17", "重型特殊结构半挂车");
        put("B18", "重型仓栅式半挂车");
        put("B19", "重型旅居半挂车");
        put("B1A", "重型专项作业半挂车");
        put("B1B", "重型低平板半挂车");
        put("B1C", "重型车辆运输半挂车");
        put("B1D", "重型罐式自卸半挂车");
        put("B1E", "重型平板自卸半挂车");
        put("B1F", "重型集装箱自卸半挂车");
        put("B1G", "重型特殊结构自卸半挂车");
        put("B1H", "重型仓栅式自卸半挂车");
        put("B1J", "重型专项作业自卸半挂车");
        put("B1K", "重型低平板自卸半挂车");
        put("B1L", "重型专门用途半挂车");
        put("B1U", "重型中置轴旅居挂车");
        put("B1V", "重型中置轴车辆运输车");
        put("B1W", "重型中置轴普通挂车");
        put("B21", "中型普通半挂车");
        put("B22", "中型厢式半挂车");
        put("B23", "中型罐式半挂车");
        put("B24", "中型平板半挂车");
        put("B25", "中型集装箱半挂车");
        put("B26", "中型自卸半挂车");
        put("B27", "中型特殊结构半挂车");
        put("B28", "中型仓栅式半挂车");
        put("B29", "中型旅居半挂车");
        put("B2A", "中型专项作业半挂车");
        put("B2B", "中型低平板半挂车");
        put("B2C", "中型车辆运输半挂车");
        put("B2D", "中型罐式自卸半挂车");
        put("B2E", "中型平板自卸半挂车");
        put("B2F", "中型集装箱自卸半挂车");
        put("B2G", "中型特殊结构自卸半挂车");
        put("B2H", "中型仓栅式自卸半挂车");
        put("B2J", "中型专项作业自卸半挂车");
        put("B2K", "中型低平板自卸半挂车");
        put("B2L", "中型专门用途半挂车");
        put("B2U", "中型中置轴旅居挂车");
        put("B2V", "中型中置轴车辆运输车");
        put("B2W", "中型中置轴普通挂车");
        put("B31", "轻型普通半挂车");
        put("B32", "轻型厢式半挂车");
        put("B33", "轻型罐式半挂车");
        put("B34", "轻型平板半挂车");
        put("B35", "轻型自卸半挂车");
        put("B36", "轻型仓栅式半挂车");
        put("B37", "轻型旅居半挂车");
        put("B38", "轻型专项作业半挂车");
        put("B39", "轻型低平板半挂车");
        put("B3C", "轻型车辆运输半挂车");
        put("B3D", "轻型罐式自卸半挂车");
        put("B3E", "轻型平板自卸半挂车");
        put("B3F", "轻型集装箱自卸半挂车");
        put("B3G", "轻型特殊结构自卸半挂车");
        put("B3H", "轻型仓栅式自卸半挂车");
        put("B3J", "轻型专项作业自卸半挂车");
        put("B3K", "轻型低平板自卸半挂车");
        put("B3L", "轻型专门用途半挂车");
        put("B3U", "轻型中置轴旅居挂车");
        put("B3V", "轻型中置轴车辆运输车");
        put("B3W", "轻型中置轴普通挂车");
        put("B41", "微型栏板半挂车");
        put("B42", "微型厢式半挂车");
        put("B43", "微型罐式半挂车");
        put("B44", "微型平板半挂车");
        put("B45", "微型自卸半挂车");
        put("B46", "微型仓栅式半挂车");
        put("B47", "微型旅居半挂车");
        put("B48", "微型专项作业半挂车");
        put("B49", "微型低平板半挂车");
        put("B4C", "微型车辆运输半挂车");
        put("B4D", "微型罐式自卸半挂车");
        put("B4E", "微型平板自卸半挂车");
        put("B4F", "微型集装箱自卸半挂车");
        put("B4G", "微型特殊结构自卸半挂车");
        put("B4H", "微型仓栅式自卸半挂车");
        put("B4J", "微型专项作业自卸半挂车");
        put("B4K", "微型低平板自卸半挂车");
        put("B4L", "微型专门用途半挂车");
        put("B4U", "微型中置轴旅居挂车");
        put("B4V", "微型中置轴车辆运输挂车");
        put("B4W", "微型中置轴普通挂车");
        put("D11", "无轨电车");
        put("D12", "有轨电车");
        put("G11", "重型普通全挂车");
        put("G12", "重型厢式全挂车");
        put("G13", "重型罐式全挂车");
        put("G14", "重型平板全挂车");
        put("G15", "重型集装箱全挂车");
        put("G16", "重型自卸全挂车");
        put("G17", "重型仓栅式全挂车");
        put("G18", "重型旅居全挂车");
        put("G19", "重型专项作业全挂车");
        put("G1A", "重型厢式自卸全挂车");
        put("G1B", "重型罐式自卸全挂车");
        put("G1C", "重型平板自卸全挂车");
        put("G1D", "重型集装箱自卸全挂车");
        put("G1E", "重型仓栅式自卸全挂车");
        put("G1F", "重型专项作业自卸全挂车");
        put("G21", "中型普通全挂车");
        put("G22", "中型厢式全挂车");
        put("G23", "中型罐式全挂车");
        put("G24", "中型平板全挂车");
        put("G25", "中型集装箱全挂车");
        put("G26", "中型自卸全挂车");
        put("G27", "中型仓栅式全挂车");
        put("G28", "中型旅居全挂车");
        put("G29", "中型专项作业全挂车");
        put("G2A", "中型厢式自卸全挂车");
        put("G2B", "中型罐式自卸全挂车");
        put("G2C", "中型平板自卸全挂车");
        put("G2D", "中型集装箱自卸全挂车");
        put("G2E", "中型仓栅式自卸全挂车");
        put("G2F", "中型专项作业自卸全挂车");
        put("G31", "轻型普通全挂车");
        put("G32", "轻型厢式全挂车");
        put("G33", "轻型罐式全挂车");
        put("G34", "轻型平板全挂车");
        put("G35", "轻型自卸全挂车");
        put("G36", "轻型仓栅式全挂车");
        put("G37", "轻型旅居全挂车");
        put("G38", "轻型专项作业全挂车");
        put("G3A", "轻型厢式自卸全挂车");
        put("G3B", "轻型罐式自卸全挂车");
        put("G3C", "轻型平板自卸全挂车");
        put("G3D", "轻型集装箱自卸全挂车");
        put("G3E", "轻型仓栅式自卸全挂车");
        put("G3F", "轻型专项作业自卸全挂车");
        put("G41", "微型栏板全挂车");
        put("G42", "微型厢式全挂车");
        put("G43", "微型罐式全挂车");
        put("G44", "微型平板全挂车");
        put("G45", "微型自卸全挂车");
        put("G46", "微型仓栅式全挂车");
        put("G48", "微型特殊用途全挂车");
        put("G4A", "微型厢式自卸全挂车");
        put("G4B", "微型罐式自卸全挂车");
        put("G4C", "微型平板自卸全挂车");
        put("G4D", "微型集装箱自卸全挂车");
        put("G4E", "微型仓栅式自卸全挂车");
        put("G4F", "微型特殊用途自卸全挂车");
        put("H11", "重型普通货车");
        put("H12", "重型厢式货车");
        put("H13", "重型封闭货车");
        put("H14", "重型罐式货车");
        put("H15", "重型平板货车");
        put("H16", "重型集装厢车");
        put("H17", "重型自卸货车");
        put("H18", "重型特殊结构货车");
        put("H19", "重型仓栅式货车");
        put("H1A", "重型车辆运输车");
        put("H1B", "重型厢式自卸货车");
        put("H1C", "重型罐式自卸货车");
        put("H1D", "重型平板自卸货车");
        put("H1E", "重型集装厢自卸货车");
        put("H1F", "重型特殊结构自卸货车");
        put("H1G", "重型仓栅式自卸货车");
        put("H1J", "重型专门用途货车");
        put("H21", "中型普通货车");
        put("H22", "中型厢式货车");
        put("H23", "中型封闭货车");
        put("H24", "中型罐式货车");
        put("H25", "中型平板货车");
        put("H26", "中型集装厢车");
        put("H27", "中型自卸货车");
        put("H28", "中型特殊结构货车");
        put("H29", "中型仓栅式货车");
        put("H2A", "中型车辆运输车");
        put("H2B", "中型厢式自卸货车");
        put("H2C", "中型罐式自卸货车");
        put("H2D", "中型平板自卸货车");
        put("H2E", "中型集装厢自卸货车");
        put("H2F", "中型特殊结构自卸货车");
        put("H2G", "中型仓栅式自卸货车");
        put("H2J", "中型专门用途货车");
        put("H31", "轻型普通货车");
        put("H32", "轻型厢式货车");
        put("H33", "轻型封闭货车");
        put("H34", "轻型罐式货车");
        put("H35", "轻型平板货车");
        put("H37", "轻型自卸货车");
        put("H38", "轻型特殊结构货车");
        put("H39", "轻型仓栅式货车");
        put("H3A", "轻型车辆运输车");
        put("H3B", "轻型厢式自卸货车");
        put("H3C", "轻型罐式自卸货车");
        put("H3D", "轻型平板自卸货车");
        put("H3F", "轻型特殊结构自卸货车");
        put("H3G", "轻型仓栅式自卸货车");
        put("H3H", "轻型多用途货车");
        put("H3J", "轻型专门用途货车");
        put("H41", "微型普通货车");
        put("H42", "微型厢式货车");
        put("H43", "微型封闭货车");
        put("H44", "微型罐式货车");
        put("H45", "微型自卸货车");
        put("H46", "微型特殊结构货车");
        put("H47", "微型仓栅式货车");
        put("H4A", "微型车辆运输车");
        put("H4B", "微型厢式自卸货车");
        put("H4C", "微型罐式自卸货车");
        put("H4F", "微型特殊结构自卸货车");
        put("H4G", "微型仓栅式自卸货车");
        put("H4H", "微型多用途货车");
        put("H4J", "微型专门用途货车");
        put("H51", "普通低速货车");
        put("H52", "厢式低速货车");
        put("H53", "罐式低速货车");
        put("H54", "自卸低速货车");
        put("H55", "仓栅式低速货车");
        put("H5B", "厢式自卸低速货车");
        put("H5C", "罐式自卸低速货车");
        put("J11", "轮式装载机械");
        put("J12", "轮式挖掘机械");
        put("J13", "轮式平地机械");
        put("K11", "大型普通客车");
        put("K12", "大型双层客车");
        put("K13", "大型卧铺客车");
        put("K14", "大型铰接客车");
        put("K15", "大型越野客车");
        put("K16", "大型轿车");
        put("K17", "大型专用客车");
        put("K18", "大型专用校车");
        put("K1A", "大型旅居车");
        put("K21", "中型普通客车");
        put("K22", "中型双层客车");
        put("K23", "中型卧铺客车");
        put("K24", "中型铰接客车");
        put("K25", "中型越野客车");
        put("K26", "中型轿车");
        put("K27", "中型专用客车");
        put("K28", "中型专用校车");
        put("K2A", "中型旅居车");
        put("K31", "小型普通客车");
        put("K32", "小型越野客车");
        put("K33", "小型轿车");
        put("K34", "小型专用客车");
        put("K38", "小型专用校车");
        put("K39", "小型面包车");
        put("K3A", "小型旅居车");
        put("K41", "微型普通客车");
        put("K42", "微型越野客车");
        put("K43", "微型轿车");
        put("K49", "微型面包车");
        put("K4A", "微型旅居车");
        put("M11", "普通正三轮摩托车");
        put("M12", "轻便正三轮摩托车");
        put("M13", "正三轮载客摩托车");
        put("M14", "正三轮载货摩托车");
        put("M15", "侧三轮摩托车");
        put("M21", "普通二轮摩托车");
        put("M22", "轻便二轮摩托车");
        put("N11", "三轮汽车");
        put("Q11", "重型半挂牵引车");
        put("Q12", "重型全挂牵引车");
        put("Q21", "中型半挂牵引车");
        put("Q22", "中型全挂牵引车");
        put("Q31", "轻型半挂牵引车");
        put("Q32", "轻型全挂牵引车");
        put("T11", "大型轮式拖拉机");
        put("T21", "小型轮式拖拉机");
        put("T22", "手扶拖拉机");
        put("T23", "手扶变形运输机");
        put("X99", "其它");
        put("Z11", "大型非载货专项作业车");
        put("Z12", "大型载货专项作业车");
        put("Z21", "中型非载货专项作业车");
        put("Z22", "中型载货专项作业车");
        put("Z31", "小型非载货专项作业车");
        put("Z32", "小型载货专项作业车");
        put("Z41", "微型非载货专项作业车");
        put("Z42", "微型载货专项作业车");
        put("Z51", "重型非载货专项作业车");
        put("Z52", "重型载货专项作业车");
        put("Z71", "轻型非载货专项作业车");
        put("Z72", "轻型载货专项作业车");

    }};
}
