package org.fh.config;

import javax.sql.DataSource;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import com.alibaba.druid.pool.DruidDataSource;

/**
 * 说明：第一数据源配置
 * 作者：FH Admin Q313596790
 * 官网：www.fhadmin.org
 */
@Configuration
@MapperScan(basePackages = MasterDataSourceConfig.PACKAGE, sqlSessionFactoryRef = "masterSqlSessionFactory") //扫描 Mapper 接口并容器管理
public class MasterDataSourceConfig {

    static final String PACKAGE = "org.fh.mapper.dsno1";								//master 目录
    static final String MAPPER_LOCATION = "classpath:mybatis/dsno1/*/*.xml";			//扫描的 xml 目录
    static final String CONFIG_LOCATION = "classpath:mybatis/dsno1/mybatis-config.xml"; //自定义的mybatis config 文件位置
    static final String TYPE_ALIASES_PACKAGE = "org.fh.entity"; 						//扫描的 实体类 目录
 
    @Value("${datasource.no1.url}")
    private String url;
 
    @Value("${datasource.no1.username}")
    private String user;
 
    @Value("${datasource.no1.password}")
    private String password;
 
    @Value("${datasource.no1.driver-class-name}")
    private String driverClass;
 
    @Bean(name = "masterDataSource")
    @Primary
    public DataSource masterDataSource() {
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setDriverClassName(driverClass);
        dataSource.setUrl(url);
        dataSource.setUsername(user);
        dataSource.setPassword(password);

        // 连接池基本配置
        dataSource.setInitialSize(5);                    // 初始化连接数
        dataSource.setMinIdle(5);                        // 最小空闲连接数
        dataSource.setMaxActive(20);                     // 最大活跃连接数
        dataSource.setMaxWait(60000);                    // 获取连接等待超时时间(毫秒)

        // 连接有效性检测配置
        dataSource.setValidationQuery("SELECT 1");       // 验证连接的SQL
        dataSource.setTestWhileIdle(true);               // 空闲时检测连接是否有效
        dataSource.setTestOnBorrow(false);               // 获取连接时检测连接是否有效
        dataSource.setTestOnReturn(false);               // 归还连接时检测连接是否有效

        // 连接回收配置
        dataSource.setTimeBetweenEvictionRunsMillis(60000);     // 配置间隔多久进行一次检测，检测需要关闭的空闲连接(毫秒)
        dataSource.setMinEvictableIdleTimeMillis(300000);       // 配置连接在池中最小生存时间(毫秒)
        dataSource.setMaxEvictableIdleTimeMillis(900000);       // 配置连接在池中最大生存时间(毫秒)

        // 预编译语句缓存配置
        dataSource.setPoolPreparedStatements(true);             // 开启PSCache
        dataSource.setMaxPoolPreparedStatementPerConnectionSize(20);  // 每个连接上PSCache的大小

        // 监控统计配置
        try {
            dataSource.setFilters("stat,wall,slf4j");            // 配置监控统计拦截的filters
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 连接泄漏检测
        dataSource.setRemoveAbandoned(true);                     // 开启连接泄漏检测
        dataSource.setRemoveAbandonedTimeout(1800);              // 连接泄漏超时时间(秒)
        dataSource.setLogAbandoned(true);                        // 记录连接泄漏日志

        return dataSource;
    }
 
    @Bean(name = "masterTransactionManager")
    @Primary
    public DataSourceTransactionManager masterTransactionManager() {
        return new DataSourceTransactionManager(masterDataSource());
    }
 
    @Bean(name = "masterSqlSessionFactory")
    @Primary
    public SqlSessionFactory masterSqlSessionFactory(@Qualifier("masterDataSource") DataSource masterDataSource)throws Exception {
        final SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(masterDataSource);
        sessionFactory.setMapperLocations(new PathMatchingResourcePatternResolver().getResources(MasterDataSourceConfig.MAPPER_LOCATION));
        sessionFactory.setConfigLocation(new DefaultResourceLoader().getResource(MasterDataSourceConfig.CONFIG_LOCATION));
        sessionFactory.setTypeAliasesPackage(MasterDataSourceConfig.TYPE_ALIASES_PACKAGE);
        return sessionFactory.getObject();
    }
}
