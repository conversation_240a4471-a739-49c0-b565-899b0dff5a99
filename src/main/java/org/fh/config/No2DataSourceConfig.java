package org.fh.config;

import javax.sql.DataSource;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import com.alibaba.druid.pool.DruidDataSource;

/**
 * 说明：第二数据源配置
 * 作者：FH Admin Q313596790
 * 官网：www.fhadmin.org
 */
@Configuration
@MapperScan(basePackages = No2DataSourceConfig.PACKAGE, sqlSessionFactoryRef = "no2SqlSessionFactory")	//扫描 Mapper 接口并容器管理
public class No2DataSourceConfig {

    static final String PACKAGE = "org.fh.mapper.dsno2";								//master 目录
    static final String MAPPER_LOCATION = "classpath:mybatis/dsno2/*/*.xml";			//扫描的 xml 目录
    static final String CONFIG_LOCATION = "classpath:mybatis/dsno2/mybatis-config.xml"; //自定义的mybatis config 文件位置
    static final String TYPE_ALIASES_PACKAGE = "org.fh.entity"; 						//扫描的 实体类 目录
 
    @Value("${datasource.no2.url}")
    private String url;
 
    @Value("${datasource.no2.username}")
    private String user;
 
    @Value("${datasource.no2.password}")
    private String password;
 
    @Value("${datasource.no2.driver-class-name}")
    private String driverClass;
 
    @Bean(name = "no2DataSource")
    public DataSource no2DataSource() {
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setDriverClassName(driverClass);
        dataSource.setUrl(url);
        dataSource.setUsername(user);
        dataSource.setPassword(password);

        // 连接池基本配置 (Oracle数据库配置相对保守)
        dataSource.setInitialSize(3);                    // 初始化连接数
        dataSource.setMinIdle(3);                        // 最小空闲连接数
        dataSource.setMaxActive(15);                     // 最大活跃连接数
        dataSource.setMaxWait(60000);                    // 获取连接等待超时时间(毫秒)

        // 连接有效性检测配置 (Oracle专用)
        dataSource.setValidationQuery("SELECT 1 FROM DUAL");  // Oracle验证连接的SQL
        dataSource.setTestWhileIdle(true);               // 空闲时检测连接是否有效
        dataSource.setTestOnBorrow(false);               // 获取连接时检测连接是否有效
        dataSource.setTestOnReturn(false);               // 归还连接时检测连接是否有效

        // 连接回收配置
        dataSource.setTimeBetweenEvictionRunsMillis(60000);     // 配置间隔多久进行一次检测，检测需要关闭的空闲连接(毫秒)
        dataSource.setMinEvictableIdleTimeMillis(300000);       // 配置连接在池中最小生存时间(毫秒)
        dataSource.setMaxEvictableIdleTimeMillis(900000);       // 配置连接在池中最大生存时间(毫秒)

        // 预编译语句缓存配置
        dataSource.setPoolPreparedStatements(true);             // 开启PSCache
        dataSource.setMaxPoolPreparedStatementPerConnectionSize(20);  // 每个连接上PSCache的大小

        // 监控统计配置
        try {
            dataSource.setFilters("stat,wall,slf4j");            // 配置监控统计拦截的filters
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 连接泄漏检测
        dataSource.setRemoveAbandoned(true);                     // 开启连接泄漏检测
        dataSource.setRemoveAbandonedTimeout(1800);              // 连接泄漏超时时间(秒)
        dataSource.setLogAbandoned(true);                        // 记录连接泄漏日志

        return dataSource;
    }
 
    @Bean(name = "no2TransactionManager")
    public DataSourceTransactionManager no2TransactionManager() {
        return new DataSourceTransactionManager(no2DataSource());
    }
 
    @Bean(name = "no2SqlSessionFactory")
    public SqlSessionFactory no2SqlSessionFactory(@Qualifier("no2DataSource") DataSource no2DataSource)throws Exception {
        final SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(no2DataSource);
        sessionFactory.setMapperLocations(new PathMatchingResourcePatternResolver().getResources(No2DataSourceConfig.MAPPER_LOCATION));
        sessionFactory.setConfigLocation(new DefaultResourceLoader().getResource(No2DataSourceConfig.CONFIG_LOCATION));
        sessionFactory.setTypeAliasesPackage(No2DataSourceConfig.TYPE_ALIASES_PACKAGE);
        return sessionFactory.getObject();
    }
}
