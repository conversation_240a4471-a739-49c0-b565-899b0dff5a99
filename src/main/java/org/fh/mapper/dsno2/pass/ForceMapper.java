package org.fh.mapper.dsno2.pass;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.fh.entity.pass.ForceVo;
import org.fh.entity.pass.VehicleVo;
import org.fh.entity.pass.ViolationVo;

import java.util.List;
/**
 * <AUTHOR>
 * @date 2020/7/28
 */
@Mapper
public interface ForceMapper {

    @Select("select WFDZ,WFXW1,HCBJ,WFSJ from VIO_FORCE where HPZL = #{HPZL} and HPHM = #{HPHM} and HCBJ = #{HCBJ}")
    List<ForceVo> GetForce(String HPZL, String HPHM, String HCBJ);


}
