package org.fh.mapper.dsno2.pass;

import org.apache.ibatis.annotations.Param;
import org.fh.entity.Page;
import org.fh.entity.PageData;

import java.util.List;
import java.util.Map;

/**
 * 说明： 黑名单Mapper
 * 作者：FH Admin QQ313596790
 * 时间：2020-06-12
 * 官网：www.fhadmin.org
 * @version
 */
public interface BlacklistMapper{

	/**新增
	 * @param pd
	 * @throws Exception
	 */
	void save(PageData pd);

	/**删除
	 * @param pd
	 * @throws Exception
	 */
	void delete(PageData pd);

	/**修改
	 * @param pd
	 * @throws Exception
	 */
	void edit(PageData pd);

	/**列表
	 * @param page
	 * @throws Exception
	 */
	List<PageData> datalistPage(Page page);

	/**列表(全部)
	 * @param pd
	 * @throws Exception
	 */
	List<PageData> listAll(PageData pd);

	/**通过id获取数据
	 * @param pd
	 * @throws Exception
	 */
	PageData findById(PageData pd);

	/**批量删除
	 * @param ArrayDATA_IDS
	 * @throws Exception
	 */
	void deleteAll(String[] ArrayDATA_IDS);

    String fandByHPId(@Param("hphm") String hphm, @Param("hpzl") String hpzl);


	String fandBySYR(String SYR);

	void savesyr(PageData pd);


	List<Map<String, Object>> highstate(@Param("hphm") String hphm, @Param("hpzl") String hpzl,@Param("lstxz") String lstxz);

	List<Map<String, Object>> overdue(@Param("hphm") String hphm, @Param("hpzl") String hpzl);

}
