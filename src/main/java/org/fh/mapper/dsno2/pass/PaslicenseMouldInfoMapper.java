package org.fh.mapper.dsno2.pass;

import org.fh.entity.Page;
import org.fh.entity.PageData;

import java.util.List;

/** 
 * 说明： 模板和路段信息关联模块--模板明细Mapper
 * 作者：FH Admin QQ313596790
 * 时间：2020-06-13
 * 官网：www.fhadmin.org
 * @version
 */
public interface PaslicenseMouldInfoMapper{

	/**新增
	 * @param pd
	 * @throws Exception
	 */
	void save(PageData pd);

	/**
	 * 批量新增
	 * @param list
	 */
	void addBatch(List<PageData> list);
	
	/**删除
	 * @param pd
	 * @throws Exception
	 */
	void delete(PageData pd);
	
	/**修改
	 * @param pd
	 * @throws Exception
	 */
	void edit(PageData pd);
	
	/**列表
	 * @param page
	 * @throws Exception
	 */
	List<PageData> datalistPage(Page page);
	
	/**列表(全部)
	 * @param pd
	 * @throws Exception
	 */
	List<PageData> listAll(PageData pd);

	/**通过id获取数据
	 * @param pd
	 * @throws Exception
	 */
	List<String> findByMBID(String MBID);
	

	
	/**批量删除
	 * @param ArrayDATA_IDS
	 * @throws Exception
	 */
	void deleteAll(String[] ArrayDATA_IDS);


	/**
	 * 通过道路ID获取数据
	 */
	List<String> getById(String ROADPARTID,String MBID);
}

