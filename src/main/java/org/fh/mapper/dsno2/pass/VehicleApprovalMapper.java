package org.fh.mapper.dsno2.pass;

import org.fh.entity.Page;
import org.fh.entity.PageData;
import org.fh.entity.pass.PaslicenseEnterprisVeh;
import org.fh.entity.pass.VehicleImageBean;

import java.util.List;

/**
 * 说明： 区域通行证--面签车辆审批Mapper
 * 作者：FH Admin QQ313596790
 * 时间：2020-05-27
 * 官网：www.fhadmin.org
 * @version
 */
public interface VehicleApprovalMapper{

	/**新增
	 * @param pd
	 * @throws Exception
	 */
	void save(PageData pd);

	void saveInfo(PaslicenseEnterprisVeh paslicenseEnterprisVeh);

	/**删除
	 * @param pd
	 * @throws Exception
	 */
	void delete(PageData pd);

	/**修改
	 * @param pd
	 * @throws Exception
	 */
	void edit(PaslicenseEnterprisVeh paslicenseEnterprisVeh);

	/**列表
	 * @param page
	 * @throws Exception
	 */
	List<PageData> datalistPage(Page page);

	/**列表(全部)
	 * @param pd
	 * @throws Exception
	 */
	List<PageData> listAll(PageData pd);

	/**获取总记录数
	 * @param pd
	 * @return
	 */
	int getCount(PageData pd);

	/**通过id获取数据
	 * @param pd
	 * @throws Exception
	 */
	PageData findById(PageData pd);
	PaslicenseEnterprisVeh findByID(String ID);
	List<PageData> findByAHE(Page page);
	List<PageData> findByAHElistPage(Page page);
	PaslicenseEnterprisVeh findByInfo(PaslicenseEnterprisVeh  paslicenseEnterprisVeh);
	/**通过id获取图片
	 * @param pd
	 * @throws Exception
	 */
	VehicleImageBean getImageById(PageData pd);

	/**批量删除
	 * @param ArrayDATA_IDS
	 * @throws Exception
	 */
	void deleteAll(String[] ArrayDATA_IDS);
	PaslicenseEnterprisVeh GetInfoById(String ID);

	List<PageData> findByEN(Page page);

	String selectIdCard(String VEHNUM,String VEHTYPE, String MEMBERIDCARD);

   String selectCard(String VEHNUM, String VEHTYPE, String ENTERPRISEID);

   String selectByIdCard(String VEHNUM, String VEHTYPE);

   String GetByIdCard(String VEHNUM, String VEHTYPE);

   String GetIdCard(String VEHNUM, String VEHTYPE);
}
