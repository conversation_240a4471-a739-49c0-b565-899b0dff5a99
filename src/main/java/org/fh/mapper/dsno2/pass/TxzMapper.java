package org.fh.mapper.dsno2.pass;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.fh.entity.Page;
import org.fh.entity.PageData;

import java.util.List;
import java.util.Map;

/**
 * 说明： 通行证Mapper
 * 作者：FH Admin QQ313596790
 * 时间：2020-05-26
 * 官网：www.fhadmin.org
 */
public interface TxzMapper {

    /**
     * 新增
     *
     * @param pd
     * @throws Exception
     */
    void save(PageData pd);

    /**
     * 删除
     *
     * @param pd
     * @throws Exception
     */
    void delete(PageData pd);

    /**
     * 修改
     *
     * @param pd
     * @throws Exception
     */
    void edit(PageData pd);

    void update(PageData pd);

    /**
     * 列表
     *
     * @param page
     * @throws Exception
     */
    List<PageData> datalistPage(Page page);

    List<PageData> listPageselectList(Page page);

    List<PageData> listPageselectByList(Page page);

    /**
     * 列表(全部)
     *
     * @param pd
     * @throws Exception
     */
    List<PageData> listAll(PageData pd);

    /**
     * 通过id获取数据
     *
     * @param pd
     * @throws Exception
     */
    PageData findById(PageData pd);

    /**
     * 批量删除
     *
     * @param ArrayDATA_IDS
     * @throws Exception
     */
    void deleteAll(String[] ArrayDATA_IDS);

    /**
     * 批量审批
     *
     * @param
     * @throws Exception
     */
    void updateAll(PageData pd);

    void updateById(PageData pd);

    List<Map<String, Object>> fandByHPZL();


    List<Map<String, Object>> fandBySPZT();

    //@Select("select YLZD6 from PASLICENSE")
    List<String> GetRoad(@Param("YSQS") String ysqs, @Param("YXQZ") String yxqz,@Param("JBR")String jbr);

    @Select("select count(1) from PASLICENSE")
    Integer getAllCount();


    @Select(
            "<script> select  * from (select A.*,rownum rn from (" +
            "select YLZD6 from PASLICENSE where AUDITING_STATUS=1" +
            "<if test='YSQS != null and YSQS.length()>0'>"+
            "and YXRQS &gt;=to_date (#{YSQS},'yyyy-mm-dd')"+
            "</if>"+
            "<if test='YXQZ != null and YXQZ.length()>0'>"+
            "and YXRQZ &lt;=to_date (#{YXQZ},'yyyy-mm-dd')"+
            "</if>"+
            "<if test='JBR != null and JBR.length()>0'>"+
            "and JBR=#{JBR}"+
            "</if>"+
            ")A" +
            "  where rownum &lt;= #{num}*#{bindex}" +
            ")" +
            " where rn &gt; #{num}*(#{bindex}-1)" +
                    "</script>"
            )
    List<String> getAllRoad(@Param("bindex") Integer bindex,@Param("num") Integer num,@Param("YSQS") String YSQS,@Param("YXQZ") String YXQZ,@Param("JBR") String JBR);

    List<Map<String, Object>> GetSPZT(@Param("YSQS") String ysqs, @Param("YXQZ") String yxqz,@Param("JBR")String jbr);

    List<Map<String, Object>> GetTXZ(@Param("YSQS") String ysqs, @Param("YXQZ") String yxqz,@Param("JBR")String jbr);

    List<Map<String, Object>> GetCLLX(@Param("YSQS") String ysqs, @Param("YXQZ") String yxqz,@Param("JBR")String jbr);

    List datalist(Page page);

}
