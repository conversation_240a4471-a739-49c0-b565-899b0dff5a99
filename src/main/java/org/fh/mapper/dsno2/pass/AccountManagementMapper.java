package org.fh.mapper.dsno2.pass;
import org.fh.entity.Page;
import org.fh.entity.PageData;
import org.fh.entity.pass.AccountImageBean;
import org.fh.entity.pass.PaslicenseEnterpris;

import java.util.List;
import java.util.Map;

/** 
 * 说明： 区域通行证--面签账户管理Mapper
 * 作者：FH Admin QQ313596790
 * 时间：2020-05-27
 * 官网：www.fhadmin.org
 * @version
 */
public interface AccountManagementMapper{

	/**新增
	 * @param pd
	 * @throws Exception
	 */
	void save(PageData pd);
	
	/**删除
	 * @param pd
	 * @throws Exception
	 */
	void delete(PageData pd);
	
	/**修改
	 * @param pd
	 * @throws Exception
	 */
	void edit(PageData pd);
	void editPwd(PageData pd);
	
	/**列表
	 * @param page
	 * @throws Exception
	 */
	List<PageData> datalistPage(Page page);
	List<PageData> selectAll(Page page);

	String selectId(String ID);
	String selectById(String ID);
	List<PageData> listPageselectCar(Page page);
	
	/**列表(全部)
	 * @param pd
	 * @throws Exception
	 */
	List<PageData> listAll(PageData pd);

	/**
	 * 获取分页查询总记录数
	 * @param pd
	 * @return
	 */
	int getCount(PageData pd);
	int getRoadCount(PageData pd);
	int carCount(PageData pd);

	/**通过id获取数据
	 * @param pd
	 * @throws Exception
	 */
	PageData findById(PageData pd);
	PageData findPass(PageData pd);
	/**通过id获取数据
	 * @param pd
	 * @throws Exception
	 */
	AccountImageBean getImageById(PageData pd);
	
	/**批量删除
	 * @param ArrayDATA_IDS
	 * @throws Exception
	 */
	void deleteAll(String[] ArrayDATA_IDS);

	PaslicenseEnterpris getInfoByAccount(String account);

	public int updatePaslicenseEnterpris(PaslicenseEnterpris paslicenseEnterpris);

	PaslicenseEnterpris findByStringId(String ID);

    PaslicenseEnterpris getInfoByAccountID(String ID, String ACCOUNT);

    List<Map<String, Object>> fandByMQ();


}

