package org.fh.mapper.dsno2.pass;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.fh.entity.pass.ViolationVo;

import java.util.List;
/**
 * <AUTHOR>
 * @date 2020/7/28
 */
@Mapper
public interface ViolationMapper {

    //违法地址，违法行为，违法记分数，交款标记，违法时间
    @Select("select WFDZ,WFXW,WFJFS,JKBJ,WFSJ from VIO_VIOLATION where HPZL = #{HPZL} and HPHM = #{HPHM} and JKBJ = #{JSKJ}")
    List<ViolationVo> GetViolation(String HPZL, String HPHM, String JSKJ);
}
