package org.fh.mapper.dsno2.pass;
import org.fh.entity.pass.VehicleVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.fh.entity.pass.ViolationVo;

import java.util.List;
/**
 * <AUTHOR>
 * @date 2020/7/28
 */
@Mapper
public interface VehicleMapper {

    @Select("select ZT,SFZMHM,SFZMMC from VEHICLE where HPZL = #{HPZL} and HPHM = #{HPHM}")
    VehicleVo getZT(String HPZL, String HPHM);

    @Select("select QZBFQZ,CLLX,SYXZ,SYR,SJHM,YXQZ from VEHICLE where HPZL = #{hpzl} and HPHM = #{hphm}")
    VehicleVo getVehicleInfo(String hpzl , String hphm);

}
