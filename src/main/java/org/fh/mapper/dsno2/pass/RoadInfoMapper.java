package org.fh.mapper.dsno2.pass;

import org.apache.ibatis.annotations.Param;
import org.fh.entity.Page;
import org.fh.entity.PageData;
import org.fh.entity.pass.Road;

import java.util.List;

/** 
 * 说明： 区域通行证--道路信息列表Mapper
 * 作者：FH Admin QQ313596790
 * 时间：2020-05-27
 * 官网：www.fhadmin.org
 * @version
 */
public interface RoadInfoMapper{

	/**新增
	 * @param pd
	 * @throws Exception
	 */
	void save(PageData pd);
	
	/**删除
	 * @param pd
	 * @throws Exception
	 */
	void delete(PageData pd);

	/**删除
	 * @param pd
	 * @throws Exception
	 */
	void Delete(PageData pd);
	/**批量删除
	 * @param ArrayDATA_IDS
	 * @throws Exception
	 */
	void DeleteAll(String[] ArrayDATA_IDS);
	
	/**修改
	 * @param pd
	 * @throws Exception
	 */
	void edit(PageData pd);
	
	/**列表
	 * @param page
	 * @throws Exception
	 */
	List<PageData> datalistPage(Page page);

	List<PageData> selectPage(Page page);

	/**根据模板id列表获取路段信息
	 * @param page
	 * @throws Exception
	 */
	List<PageData> datalistPageByRoadIDList(Page page);

	List<PageData> rowlistPage(Page page);
	
	/**列表(全部)
	 * @param pd
	 * @throws Exception
	 */
	List<PageData> listAll(PageData pd);

	/**
	 * 获取总记录数
	 * @param pd
	 * @return
	 */
	int getCount(PageData pd);
	
	/**通过id获取数据
	 * @param pd
	 * @throws Exception
	 */
	PageData findById(PageData pd);
	
	/**批量删除
	 * @param ArrayDATA_IDS
	 * @throws Exception
	 */
	void deleteAll(String[] ArrayDATA_IDS);

	List<Road> fndByRoad(@Param("name") String name);

	int getRoadCount(PageData pd);


	//List<Map<String,Object>> fndByRoad(String name);


}

