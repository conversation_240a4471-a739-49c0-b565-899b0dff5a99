package org.fh.mapper.dsno2.pass;

import org.fh.entity.Page;
import org.fh.entity.PageData;
import org.fh.entity.pass.RoadPart;

import java.util.List;

/**
 * 说明： 区域通行证--模板管理Mapper
 * 作者：FH Admin QQ313596790
 * 时间：2020-05-27
 * 官网：www.fhadmin.org
 * @version
 */
public interface TemplateManagementMapper{

	/**新增
	 * @param pd
	 * @throws Exception
	 */
	void save(PageData pd);

	/**删除
	 * @param pd
	 * @throws Exception
	 */
	void delete(PageData pd);

	/**修改
	 * @param pd
	 * @throws Exception
	 */
	void edit(PageData pd);

	/**列表
	 * @param page
	 * @throws Exception
	 */
	List<PageData> datalistPage(Page page);


	/**列表(全部)
	 * @param pd
	 * @throws Exception
	 */
	List<PageData> listAll(PageData pd);

	/**
	 * 查詢模板道路
	 * @param pd
	 * @return
	 */
	List<RoadPart> listById(PageData pd);

	/**获取总记录数
	 * @param pd
	 * @return
	 */
	int getCount(PageData pd);

	/**通过id获取数据
	 * @param pd
	 * @throws Exception
	 */
	PageData findById(PageData pd);

	/**批量删除
	 * @param ArrayDATA_IDS
	 * @throws Exception
	 */
	void deleteAll(String[] ArrayDATA_IDS);

    int getRoadCount(PageData pd);

    List<PageData> listPagelistRoad(Page page);
}

