package org.fh.mapper.dsno2.pass;

import org.fh.entity.Page;
import org.fh.entity.PageData;

import java.util.List;

/** 
 * 说明： 区域通行证--历史数据检索Mapper
 * 作者：FH Admin QQ313596790
 * 时间：2020-05-27
 * 官网：www.fhadmin.org
 * @version
 */
public interface HistoricalDataMapper{

	/**新增
	 * @param pd
	 * @throws Exception
	 */
	void save(PageData pd);
	
	/**注销
	 * @param pd
	 * @throws Exception
	 */
	void zx(PageData pd);
	
	/**修改
	 * @param pd
	 * @throws Exception
	 */
	void edit(PageData pd);
	
	/**列表
	 * @param pd
	 * @throws Exception
	 */
	List<PageData> datalistPage(Page pd);
	
	/**列表(全部)
	 * @param pd
	 * @throws Exception
	 */
	List<PageData> listAll(PageData pd);

	/**
	 * 获取总记录数(条件查询)
	 * @param pd
	 * @return
	 */
	int getCount(PageData pd);
	
	/**通过id获取数据
	 * @param pd
	 * @throws Exception
	 */
	PageData findById(PageData pd);
	
	/**批量删除
	 * @param ArrayDATA_IDS
	 * @throws Exception
	 */
	void deleteAll(String[] ArrayDATA_IDS);

	/**
	 * 批量注销
	 * @param pd
	 */
	void zx_All(PageData pd);
	
}

