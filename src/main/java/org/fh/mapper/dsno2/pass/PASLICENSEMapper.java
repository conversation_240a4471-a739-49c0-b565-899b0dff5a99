package org.fh.mapper.dsno2.pass;


import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.fh.entity.pass.PASLICENSE;
import org.fh.entity.pass.PASLICENSEWithBLOBs;

import java.util.List;
import java.util.Map;


@Mapper
public interface PASLICENSEMapper {

    int insert(PASLICENSEWithBLOBs record);

    PASLICENSEWithBLOBs select(String xh);

    PASLICENSEWithBLOBs selectById(String hpzl, String hphm, String MEMBERIDCARD, String ENTERPRISEID);

    String selectXh(String hpzl,String hphm);

    PASLICENSEWithBLOBs selectListById(String XH);

    int insertList(@Param("list") List<PASLICENSEWithBLOBs> list);

    int insertRoad(@Param("list") List<String> list);

    void update(PASLICENSEWithBLOBs record);

    void updateMap(PASLICENSEWithBLOBs record);

    void updateZx(PASLICENSEWithBLOBs record);

    Map<String, Object> trafficStatus(@Param("hphm") String hphm, @Param("hpzl") String hpzl, @Param("txzlx") String txzlx);

    PASLICENSE getId(String xh);

    void update_txz(PASLICENSEWithBLOBs paslicenseWithBLOBs);
    Integer trafficStatus1(@Param("hphm") String hphm, @Param("hpzl") String hpzl, @Param("txzlx") String txzlx);

    List<PASLICENSEWithBLOBs> selectByIdHphm(PASLICENSEWithBLOBs paslicenseWithBLOBs);

    List selectListIdCar(PASLICENSEWithBLOBs paslicenseWithBLOBs);

    List selectTest(PASLICENSEWithBLOBs paslicenseWithBLOBs);
}

