package org.fh.mapper.dsno2.pass;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.fh.entity.pass.SurveilVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/28
 */
@Mapper
public interface SurveilMapper {
    @Select("select WFDZ,WFXW,JKBJ,WFSJ from VIO_SURVEIL where HPZL = #{HPZL} and HPHM = #{HPHM} and JKBJ = #{JSKJ}")
    List<SurveilVo>  GetSurveil(String HPZL, String HPHM, String JSKJ);

    @Select("select WFMS from VIO_NCODEWFDM where WFXW = #{wfxw}")
    String GetWFMS(String wfxw);
}
