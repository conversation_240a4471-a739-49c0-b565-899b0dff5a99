package org.fh.controller.pass;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.fh.controller.base.BaseController;
import org.fh.entity.Page;
import org.fh.entity.PageData;
import org.fh.service.pass.RoadInfoService;
import org.fh.service.system.FHlogService;
import org.fh.util.Jurisdiction;
import org.fh.util.ObjectExcelView;
import org.fh.util.Tools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 说明：区域通行证--道路信息列表
 * 作者：FH Admin QQ313596790
 * 时间：2020-05-27
 * 官网：www.fhadmin.org
 */
@Controller
@RequestMapping("/roadInfo")
public class RoadInfoController extends BaseController {

    @Autowired
    private RoadInfoService roadInfoService;

    @Autowired
    private PaslicenseMouldInfoController paslicenseMouldInfoController;
    @Autowired
    private FHlogService FHLOG;

    /**删除
     * @param out
     * @throws Exception
     */
    @RequestMapping(value="/Delete")
    @ResponseBody
    public Object Delete() throws Exception{
        Map<String,String> map = new HashMap<String,String>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        roadInfoService.Delete(pd);
        map.put("result", errInfo);				//返回结果
        FHLOG.save(Jurisdiction.getUsername(), "道路信息列表-->删除成功.ID:"+pd.getString("ID"));				//记录日志

        return map;
    }

    /**批量删除
     * @param
     * @throws Exception
     */
    @RequestMapping(value="/DeleteAll")
    @ResponseBody
    public Object DeleteAll() throws Exception{
        Map<String,Object> map = new HashMap<String,Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        String DATA_IDS = pd.getString("DATA_IDS");
        if(Tools.notEmpty(DATA_IDS)){
            String ArrayDATA_IDS[] = DATA_IDS.split(",");
            roadInfoService.DeleteAll(ArrayDATA_IDS);
            FHLOG.save(Jurisdiction.getUsername(), "道路信息列表-->批量删除成功.ID:"+pd.getString("ID"));				//记录日志

            errInfo = "success";
        }else{
            errInfo = "error";
        }
        map.put("result", errInfo);				//返回结果
        return map;
    }

    /**
     * 保存
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/add")
    @RequiresPermissions("roadInfo:add")
    @ResponseBody
    public Object add() throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        pd.put("ID", this.get32UUID());    //主键
        pd.put("GXSJ", LocalDate.now());//更新日期
        roadInfoService.save(pd);
        FHLOG.save(Jurisdiction.getUsername(), "道路信息列表-->保存成功.ID:"+pd.getString("ID"));				//记录日志

        map.put("result", errInfo);
        return map;
    }

    /**
     * 删除
     *
     * @param out
     * @throws Exception
     */
    @RequestMapping(value = "/delete")
    @RequiresPermissions("roadInfo:del")
    @ResponseBody
    public Object delete() throws Exception {
        Map<String, String> map = new HashMap<String, String>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        roadInfoService.delete(pd);
        map.put("result", errInfo);                //返回结果
        FHLOG.save(Jurisdiction.getUsername(), "道路信息列表-->删除成功.ID:"+pd.getString("ID"));				//记录日志

        return map;
    }

    /**
     * 修改
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/edit")
    @RequiresPermissions("roadInfo:edit")
    @ResponseBody
    public Object edit() throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        pd.put("GXSJ", LocalDate.now());
        roadInfoService.edit(pd);
        FHLOG.save(Jurisdiction.getUsername(), "道路信息列表-->修改成功.ID:"+pd.getString("ID"));				//记录日志
        map.put("result", errInfo);
        return map;
    }

//
//    /**
//     * 根据路段ID列表获取路段信息列表
//     *主要提供给模板管理模块中添加模板明细选择道路用，以及道路信息列表
//     * @param page
//     * @throws Exception
//     */
//    @RequestMapping(value = "/list")
////    @RequiresPermissions("roadInfo:list")
//    @ResponseBody
//    public Object datalistPageByRoadIDList(Page page) throws Exception {
//        Map<String, Object> map = new HashMap<String, Object>();
//        String errInfo = "success";
//        PageData pd = new PageData();
//        pd = this.getPageData();
//
//        String LDMCKEYWORDS = pd.getString("LDMCKEYWORDS");//关键词检索条件--道路名称
//        if (Tools.notEmpty(LDMCKEYWORDS)) {
//            pd.put("LDMCKEYWORDS", LDMCKEYWORDS.trim());
//        }
//        String DLJBKEYWORDS = pd.getString("DLJBKEYWORDS");//关键词检索条件--道路名称
//        if (Tools.notEmpty(DLJBKEYWORDS)) {
//            pd.put("DLJBKEYWORDS", DLJBKEYWORDS.trim());
//        }
//
//        //获取路段信息列表
//        List<String> roadList = new ArrayList<>();
//        String MBID = pd.getString("MBID");
//        if (Tools.notEmpty(MBID)) {
//            roadList = paslicenseMouldInfoController.getRoadIDListByMBID(MBID.trim());
//        }
//
//        String msg = pd.getString("msg");
//        if (Tools.notEmpty(msg)){
//            if (null!=roadList && roadList.size()>0){
//               pd.put("ids",roadList);
//            }else {
//                List<PageData> varList = new ArrayList<>();
//                page.setTotalResult(0);
//                map.put("page", page);
//                map.put("varList", varList);
//                map.put("result", errInfo);
//                return map;
//            }
//        }else {
//            pd.put("unboundIds",roadList);
//        }
//
//
//        int count = roadInfoService.getCount(pd);  //总记录数
//        page.setTotalResult(count); //补充page中的总记录数
//        pd.put("currentPage", page.getCurrentPage());    //当前页 方便xml中分页语句直接获取
//        pd.put("showCount", page.getShowCount());    //每页显示记录数
//        page.setPd(pd);
//
//        List<PageData> varList = roadInfoService.list(page);    //列出roadInfo列表
//        page.setTotalResult(count); //纠正page中的总记录数
//
//        map.put("varList", varList);
//        map.put("page", page);
//        map.put("result", errInfo);
//        return map;
//    }


    /**
     * 根据路段ID列表获取路段信息列表
     *
     * @param page
     * @throws Exception
     */
    @RequestMapping(value = "/list")
//    @RequiresPermissions("roadInfo:list")
    @ResponseBody
    public Object datalistPageByRoadIDList(Page page) throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();

        String KEYWORDS = pd.getString("KEYWORDS");
        if(Tools.notEmpty(KEYWORDS)){
            pd.put("KEYWORDS", KEYWORDS.trim().toLowerCase());
        }
        String DLJBKEYWORDS = pd.getString("DLJBKEYWORDS");
        if(Tools.notEmpty(DLJBKEYWORDS)) {
            pd.put("DLJBKEYWORDS", DLJBKEYWORDS.trim());
        }
        page.setPd(pd);

        List<PageData> varList = roadInfoService.list(page);    //列出roadInfo列表

        map.put("varList", varList);
        map.put("page", page);
        map.put("result", errInfo);
        return map;
    }


    /**
     * 去修改页面获取数据
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/goEdit")
    @RequiresPermissions("roadInfo:edit")
    @ResponseBody
    public Object goEdit() throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        pd = roadInfoService.findById(pd);    //根据ID读取
        map.put("pd", pd);
        map.put("result", errInfo);
        return map;
    }

    /**
     * 批量删除
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/deleteAll")
    @RequiresPermissions("roadInfo:del")
    @ResponseBody
    public Object deleteAll() throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        String DATA_IDS = pd.getString("DATA_IDS");
        if (Tools.notEmpty(DATA_IDS)) {
            String ArrayDATA_IDS[] = DATA_IDS.split(",");
            roadInfoService.deleteAll(ArrayDATA_IDS);
            FHLOG.save(Jurisdiction.getUsername(), "道路信息列表-->批量删除成功.ID:"+ArrayDATA_IDS);				//记录日志

            errInfo = "success";
        } else {
            errInfo = "error";
        }
        map.put("result", errInfo);                //返回结果
        return map;
    }

    /**
     * 导出到excel
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/excel")
    @RequiresPermissions("toExcel")
    public ModelAndView exportExcel() throws Exception {
        ModelAndView mv = new ModelAndView();
        PageData pd = new PageData();
        pd = this.getPageData();
        Map<String, Object> dataMap = new HashMap<String, Object>();
        List<String> titles = new ArrayList<String>();
        titles.add("id");    //1
        titles.add("路段名称");    //2
        titles.add("排序");    //3
        titles.add("六合一(0否1是)");    //4
        titles.add("拼音");    //5
        titles.add("等级");    //6
        titles.add("显示 1显示 0不显示");    //7
        titles.add("上级id");    //8
        titles.add("状态 0正常 1删除");    //9
        titles.add("交换 0未交换 1已交换");    //10
        titles.add("更新时间");    //11
        dataMap.put("titles", titles);
        List<PageData> varOList = roadInfoService.listAll(pd);
        List<PageData> varList = new ArrayList<PageData>();
        for (int i = 0; i < varOList.size(); i++) {
            PageData vpd = new PageData();
            vpd.put("var1", varOList.get(i).getString("ID"));        //1
            vpd.put("var2", varOList.get(i).getString("LDMC"));        //2
            vpd.put("var3", varOList.get(i).getString("PX"));        //3
            vpd.put("var4", varOList.get(i).getString("LHY"));        //4
            vpd.put("var5", varOList.get(i).getString("PY"));        //5
            vpd.put("var6", varOList.get(i).getString("DJ"));        //6
            vpd.put("var7", varOList.get(i).getString("XS"));        //7
            vpd.put("var8", varOList.get(i).getString("SJID"));        //8
            vpd.put("var9", varOList.get(i).getString("ZT"));        //9
            vpd.put("var10", varOList.get(i).getString("JH"));        //10
            vpd.put("var11", varOList.get(i).getString("GXSJ"));        //11
            varList.add(vpd);
        }
        dataMap.put("varList", varList);
        ObjectExcelView erv = new ObjectExcelView();
        mv = new ModelAndView(erv, dataMap);
        FHLOG.save(Jurisdiction.getUsername(), "用户:"+Jurisdiction.getUsername()+"-->道路信息列表-->导出表格");				//记录日志

        return mv;
    }

}
