package org.fh.controller.pass;
import org.apache.commons.lang.StringUtils;
import org.fh.entity.pass.ForceVo;
import org.fh.entity.pass.SurveilVo;
import org.fh.entity.pass.VehicleVo;
import org.fh.entity.pass.ViolationVo;
import org.fh.util.Const;
import org.fh.service.pass.VehicleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/7/28
 */
//TODO 默认 0-未处理，1-已处理
@Controller
@RequestMapping("/zt")
public class IllegalInformationController {

    @Autowired
    private VehicleService vehicleService;

    /**
     * 根据号牌种类和号牌号码查询机VehicleInfo 表强制报废期止，车辆类型，使用性质，机动车所有人，手机号码，检验有效期止
     *
     * @param vehicleVo HPZL,HPHM
     * @return jsonStr
     */
    @PostMapping(value = "/getVehicleInfo",  consumes = "application/json")
    @ResponseBody
    public Map<String, Object> getVehicleInfo(@RequestBody VehicleVo vehicleVo) {
        Map<String,Object> map = new HashMap<String,Object>();
        String Info = "";
        try {
            VehicleVo ZtVo = vehicleService.getVehicleInfo(vehicleVo);
            map.put("result", ZtVo);
        }
        catch (Exception e) {
            String errInfo = "error";
            System.out.println(e.getMessage());
            map.put("result", errInfo);
            map.put("msg", e.getMessage());
        }
        return map;
    }
    /**
     * 根据号牌种类和号牌号码查询机动车状态并根据代码值查询代码属性1
     *
     * @param vehicleVo HPZL,HPHM
     * @return jsonStr
     */
    @PostMapping(value = "/getZt",  consumes = "application/json")
    @ResponseBody
    public Map<String, Object> getZT(@RequestBody VehicleVo vehicleVo) {
        Map<String,Object> map = new HashMap<String,Object>();
        String Info = "";
        try {
            VehicleVo ZtVo = vehicleService.getZT(vehicleVo);
            //查询出的状态码可能含有多个字符，遍历字符串在通用MAP中取值
            String ZT = ZtVo.getZT();
            if (StringUtils.isNotBlank(ZT))
            {
                if (ZT.length() != 1){
                    StringBuffer buf = new StringBuffer();
                    for(int i = 0; i < ZT.length(); i++) {
                        if (Const.ZT_MAP.get(String.valueOf(ZT.charAt(i))) != null){
                            buf.append(Const.ZT_MAP.get(String.valueOf(ZT.charAt(i))));
                            buf.append(' ');
                        }
                    }
                    Info = buf.toString();
                }else {
                    Info = (String) Const.ZT_MAP.get(ZT);
                }
            }
            map.put("result", Info);
//            return HttpResponse.success();
        } catch (Exception e) {
            String errInfo = "error";
            System.out.println(e.getMessage());
            map.put("result", errInfo);
            map.put("msg", e.getMessage());
//            return HttpResponse.failure(e);
        }
        return map;
    }

    /**
     * 根据号牌种类和号牌号码查询VIO_VIOLATION表返回违法信息
     *
     * @param violationVo HPZL,HPHM
     * @return jsonStr
     */
    @PostMapping(value = "/getViolation",  consumes = "application/json")
    @ResponseBody
    public Map<String, Object> getWF(@RequestBody ViolationVo violationVo){
        Map<String,Object> map = new HashMap<String,Object>();
        try {
            List<ViolationVo> ZtVo = vehicleService.getWF(violationVo);
            map.put("result", ZtVo);
        }
        catch (Exception e) {
            String errInfo = "error";
            System.out.println(e.getMessage());
            map.put("result", errInfo);
            map.put("msg", e.getMessage());
        }
        return map;
    }
    /**
     * 根据号牌种类和号牌号码查询VIO_SURVEIL表返回违法信息
     *
     * @param surveilVo HPZL,HPHM
     * @return jsonStr
     */
    @PostMapping(value = "/getSurveil",  consumes = "application/json")
    @ResponseBody
    public Map<String, Object> GetSurveilWf(@RequestBody SurveilVo surveilVo){
        Map<String,Object> map = new HashMap<String,Object>();
        try {
            List<SurveilVo> ZtVo = vehicleService.GetSurveilWf(surveilVo);
            map.put("result", ZtVo);
        }
        catch (Exception e) {
            String errInfo = "error";
            System.out.println(e.getMessage());
            map.put("result", errInfo);
            map.put("msg", e.getMessage());
        }
        return map;
    }
    /**
     * 根据号牌种类和号牌号码查询VIO_FORCE表返回违法信息
     *
     * @param forceVo HPZL,HPHM
     * @return jsonStr
     */
    @PostMapping(value = "/getForce",  consumes = "application/json")
    @ResponseBody
    public Map<String, Object> GetForce(@RequestBody ForceVo forceVo){
        Map<String,Object> map = new HashMap<String,Object>();
        try {
            List<ForceVo> ZtVo = vehicleService.GetForceWf(forceVo);
            map.put("result", ZtVo);
        }
        catch (Exception e) {
            String errInfo = "error";
            System.out.println(e.getMessage());
            map.put("result", errInfo);
            map.put("msg", e.getMessage());
        }
        return map;
    }
}
