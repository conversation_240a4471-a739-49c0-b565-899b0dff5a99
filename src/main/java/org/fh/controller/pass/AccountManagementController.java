package org.fh.controller.pass;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.fh.controller.base.BaseController;
import org.fh.entity.Page;
import org.fh.entity.PageData;
import org.fh.entity.pass.AccountImageBean;
import org.fh.entity.pass.PaslicenseEnterpris;
import org.fh.service.pass.AccountManagementService;
import org.fh.service.system.FHlogService;
import org.fh.service.system.UsersService;
import org.fh.util.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.time.LocalDateTime;
import java.util.*;

import static org.fh.util.DbFH.getPprVue;

//import org.fh.util.UserSessionUtil;

/**
 * 说明：区域通行证--面签账户管理
 * 作者：FH Admin QQ313596790
 * 时间：2020-05-27
 * 官网：www.fhadmin.org
 */
//@Controller
@RestController
@RequestMapping("/accountmanagement")
public class AccountManagementController extends BaseController {

    @Autowired
    private AccountManagementService accountmanagementService;

    @Autowired
    private UsersService usersService;

    @Autowired
    private FHlogService FHLOG;

    private static Properties pros = getPprVue();
    /**
     * 添加/修改企业信息
     *
     * @param
     * @throws Exception
     */

    @CrossOrigin
    @RequestMapping(value = "/SaveCompanyInfo")
    @ResponseBody
    public Object SaveCompanyInfo() throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        String name = Jurisdiction.getUsername();  //用户表的username是PASLICENSE_ENTERPRIS的account
        PaslicenseEnterpris paslicenseEnterpris = accountmanagementService.getInfoByAccount(name);
        paslicenseEnterpris.setOWNERPHONE(pd.getString("OWNERPHONE"));
        paslicenseEnterpris.setOWNERNAME(pd.getString("OWNERNAME"));
        paslicenseEnterpris.setENTERPRISENAME(pd.getString("ENTERPRISENAME"));
        paslicenseEnterpris.setORGANIZATIONCODE(pd.getString("ORGANIZATIONCODE"));
        paslicenseEnterpris.setMANAGERNAME(pd.getString("MANAGERNAME"));
        paslicenseEnterpris.setMANAGERPHONE(pd.getString("MANAGERPHONE"));
        paslicenseEnterpris.setORGANIZATIONPIC(pd.getString("ORGANIZATIONPIC"));
        paslicenseEnterpris.setAPPLICATIONPIC(pd.getString("APPLICATIONPIC"));
        paslicenseEnterpris.setENTERPRISEADDRESS(pd.getString("ENTERPRISEADDRESS"));
        paslicenseEnterpris.setSHZT("0");
        Date now = new Date();
        paslicenseEnterpris.setXGSJ(now);
        paslicenseEnterpris.setUNITORCONTRACTPIC(pd.getString("UNITORCONTRACTPIC"));
        // TODO 将机构信息存入PASLICENSE_ENTERPRISE表
        accountmanagementService.updatePaslicenseEnterpris(paslicenseEnterpris);
        FHLOG.save(Jurisdiction.getUsername(), "面签账户管理-->修改成功.面签账号:"+paslicenseEnterpris.getACCOUNT());				//记录日志
        map.put("result", errInfo);
        return map;
    }

    /**
     * 添加
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/add")
    @RequiresPermissions("accountmanagement:add")
    @ResponseBody
    public Object add() throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        pd.put("ID", this.get32UUID());    //主键
        if (Tools.notEmpty(pd.getString("PASSWORD"))) {
            pd.put("PASSWORD", MD5.md5(pd.getString("PASSWORD")));
        }
        pd.put("TJSJ", LocalDateTime.now());
        String username = Jurisdiction.getName();
        String Rnumbers = Jurisdiction.getRnumbers();
        pd.put("TJR", username);
        //判断用户分组
        //用户是否在系统组或者备用组的科研所
        pd.put("TJBM", UserSessionUtil.getUserGroup(Rnumbers));
        pd.put("SHZT", "1");

        System.out.println(pd);
        accountmanagementService.save(pd);
        map.put("result", errInfo);
        FHLOG.save(Jurisdiction.getUsername(), "面签账户管理-->添加成功.面签账号:"+pd.getString("ACCOUNT"));				//记录日志
        return map;
    }

    /**
     * 删除
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/delete")
    @RequiresPermissions("accountmanagement:del")
    @ResponseBody
    public Object delete() throws Exception {
        Map<String, String> map = new HashMap<String, String>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        accountmanagementService.delete(pd);
        map.put("result", errInfo);                //返回结果
        FHLOG.save(Jurisdiction.getUsername(), "面签账户管理-->删除成功.面签账号:"+pd.getString("ID"));				//记录日志

        return map;
    }

    /**
     * 管理员修改
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/edit")
    @RequiresPermissions("accountmanagement:edit")
    @ResponseBody
    public Object edit() throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
//        密码默认
//        String password = pros.getProperty("accountManagePwd");
        if (Tools.notEmpty(pd.getString("PASSWORD"))) {
            pd.put("PASSWORD", MD5.md5(pd.getString("PASSWORD")));
        }
        if (pd.getString("check").equals("save")){
            pd.remove("PASSWORD");
        }
        pd.put("SHSJ", LocalDateTime.now());
        pd.put("SHR", Jurisdiction.getName());
        accountmanagementService.edit(pd);
        FHLOG.save(Jurisdiction.getUsername(), "面签账户管理-->修改成功.面签账号:"+pd.getString("ACCOUNT"));				//记录日志

        map.put("result", errInfo);
        return map;
    }
    /**
     * 列表
     *
     * @param page
     * @throws Exception
     */
    @RequestMapping(value = "/list")
    @RequiresPermissions("accountmanagement:list")
    @ResponseBody
    public Object list(Page page) throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();

        String ACCOUNTKEYWORDS = pd.getString("ACCOUNTKEYWORDS");//关键词检索条件--账号
        if (Tools.notEmpty(ACCOUNTKEYWORDS)) {
            pd.put("ACCOUNTKEYWORDS", ACCOUNTKEYWORDS.trim());
        }
        String ENTERPRISENAMEKEYWORDS = pd.getString("ENTERPRISENAMEKEYWORDS");//关键词检索条件--企业名称  用户名
        if (Tools.notEmpty(ENTERPRISENAMEKEYWORDS)) {
            pd.put("ENTERPRISENAMEKEYWORDS", ENTERPRISENAMEKEYWORDS.trim());
        }
        String ZHLXKEYWORDS = pd.getString("ZHLXKEYWORDS");//关键词检索条件--账号类型
        if (Tools.notEmpty(ZHLXKEYWORDS)) {
            pd.put("ZHLXKEYWORDS", ZHLXKEYWORDS.trim());
        }
        String TJRKEYWORDS = pd.getString("TJRKEYWORDS");//关键词检索条件--添加人
        if (Tools.notEmpty(TJRKEYWORDS)) {
            pd.put("TJRKEYWORDS", TJRKEYWORDS.trim());
        }
        String TJBMKEYWORDS = pd.getString("TJBMKEYWORDS");//关键词检索条件--添加部门 默认查看本部门添加
        if (Tools.notEmpty(TJBMKEYWORDS)) {
            pd.put("TJBMKEYWORDS", TJBMKEYWORDS.trim());
        } else {
            pd.put("TJBMKEYWORDS", UserSessionUtil.getUserGroup(Jurisdiction.getRnumbers()));
        }
        String SHZTEYWORDS = pd.getString("SHZTEYWORDS");//关键词检索条件--审核状态
        if (Tools.notEmpty(SHZTEYWORDS)) {
            pd.put("SHZTEYWORDS", SHZTEYWORDS.trim());
        }
        page.setPd(pd);

        List<PageData> varList = accountmanagementService.list(page);    //列出AccountManagement列表

        map.put("varList", varList);
        map.put("page", page);
        map.put("result", errInfo);
        return map;
    }

    /**
     * 获取账号信息
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/getInfo")
    @ResponseBody
    public Object getInfo() throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        String name = Jurisdiction.getUsername();  //用户表的username是PASLICENSE_ENTERPRIS的account

        PaslicenseEnterpris paslicenseEnterpris = accountmanagementService.getInfoByAccount(name);
        map.put("pd", paslicenseEnterpris);
        map.put("result", errInfo);
        return map;
    }

    /**
     * 获取随机账号
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/getAccount")
    @RequiresPermissions("accountmanagement:edit")
    @ResponseBody
    public Object getAccount() throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        pd.put("ACCOUNT", RandomAccount.getAccount());
        map.put("pd", pd);
        map.put("result", errInfo);
        return map;
    }

    /**
     * 去页面修改数据
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/goEdit")
    @RequiresPermissions("accountmanagement:edit")
    @ResponseBody
    public Object goEdit() throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        pd.put("XGSJ", LocalDateTime.now());
        pd = accountmanagementService.findById(pd);
        map.put("pd", pd);
        map.put("result", errInfo);

        return map;
    }

    /**
     * 根据id获取数据
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/findById")
//    @RequiresPermissions("accountmanagement:edit")
    @ResponseBody
    public Object findById() throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        PaslicenseEnterpris data = accountmanagementService.getInfoByAccount(pd.getString("ID"));
        map.put("pd", data);
        map.put("result", errInfo);
        return map;
    }


    /**
     * 去页面修改数据
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/getImageById")
//    @RequiresPermissions("accountmanagement:edit")
    @ResponseBody
    public Object getImageById() throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        AccountImageBean imageBean = accountmanagementService.getImageById(pd);
        if (null != imageBean && Tools.notEmpty(imageBean.getORGANIZATIONPIC())) {
            pd.put("ORGANIZATIONPIC", imageBean.getORGANIZATIONPIC());
        }

        if (null != imageBean && Tools.notEmpty(imageBean.getAPPLICATIONPIC())) {
            pd.put("APPLICATIONPIC", imageBean.getAPPLICATIONPIC());
        }

        if (null != imageBean && Tools.notEmpty(imageBean.getUNITORCONTRACTPIC())) {
            pd.put("UNITORCONTRACTPIC", imageBean.getUNITORCONTRACTPIC());
        }
        map.put("pd", pd);
        map.put("result", errInfo);
        return map;
    }

    /**
     * 批量删除
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/deleteAll")
    @RequiresPermissions("accountmanagement:del")
    @ResponseBody
    public Object deleteAll() throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        String DATA_IDS = pd.getString("DATA_IDS");
        if (Tools.notEmpty(DATA_IDS)) {
            String ArrayDATA_IDS[] = DATA_IDS.split(",");
            accountmanagementService.deleteAll(ArrayDATA_IDS);
            FHLOG.save(Jurisdiction.getUsername(), "面签账户管理-->批量删除成功.ID:"+DATA_IDS);				//记录日志

            errInfo = "success";
        } else {
            errInfo = "error";
        }
        map.put("result", errInfo);                //返回结果
        return map;
    }

    /**
     * 导出到excel
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/excel")
    @RequiresPermissions("toExcel")
    public ModelAndView exportExcel() throws Exception {
        ModelAndView mv = new ModelAndView();
        PageData pd = new PageData();
        pd = this.getPageData();
        Map<String, Object> dataMap = new HashMap<String, Object>();
        List<String> titles = new ArrayList<String>();
        titles.add("id");    //1
        titles.add("账号");    //2
        titles.add("密码");    //3
        titles.add("企业名称");    //4
        titles.add("添加时间");    //5
        titles.add("组织机构代码");    //6
        titles.add("法人姓名");    //7
        titles.add("法人联系电话");    //8
        titles.add("管理人姓名");    //9
        titles.add("管理人电话");    //10
        titles.add("企业地址");    //11
        titles.add("组织机构代码证图片");    //12
        titles.add("申请书图片");    //13
        titles.add("单位证明图片");    //14
        titles.add("审核状态（0未审核 1审核通过 2审核不通过）");    //15
        titles.add("审核时间");    //16
        titles.add("审核人");    //17
        titles.add("审核意见");    //18
        titles.add("修改时间");    //19
        titles.add("账号类型(企业 个人)");    //20
        titles.add("交换标记(0未交换 1已交换)");    //21
        titles.add("交换时间");    //22
        titles.add("添加部门");    //23
        titles.add("添加人");    //24
        dataMap.put("titles", titles);
        List<PageData> varOList = accountmanagementService.listAll(pd);
        List<PageData> varList = new ArrayList<PageData>();
        for (int i = 0; i < varOList.size(); i++) {
            PageData vpd = new PageData();
            vpd.put("var1", varOList.get(i).getString("ID"));        //1
            vpd.put("var2", varOList.get(i).getString("ACCOUNT"));        //2
            vpd.put("var3", varOList.get(i).getString("PASSWORD"));        //3
            vpd.put("var4", varOList.get(i).getString("ENTERPRISENAME"));        //4
            vpd.put("var5", varOList.get(i).getString("TJSJ"));        //5
            vpd.put("var6", varOList.get(i).getString("ORGANIZATIONCODE"));        //6
            vpd.put("var7", varOList.get(i).getString("OWNERNAME"));        //7
            vpd.put("var8", varOList.get(i).getString("OWNERPHONE"));        //8
            vpd.put("var9", varOList.get(i).getString("MANAGERNAME"));        //9
            vpd.put("var10", varOList.get(i).getString("MANAGERPHONE"));        //10
            vpd.put("var11", varOList.get(i).getString("ENTERPRISEADDRESS"));        //11
            vpd.put("var12", varOList.get(i).getString("ORGANIZATIONPIC"));        //12
            vpd.put("var13", varOList.get(i).getString("APPLICATIONPIC"));        //13
            vpd.put("var14", varOList.get(i).getString("UNITORCONTRACTPIC"));        //14
            vpd.put("var15", varOList.get(i).getString("SHZT"));        //15
            vpd.put("var16", varOList.get(i).getString("SHSJ"));        //16
            vpd.put("var17", varOList.get(i).getString("SHR"));        //17
            vpd.put("var18", varOList.get(i).getString("SHYJ"));        //18
            vpd.put("var19", varOList.get(i).getString("XGSJ"));        //19
            vpd.put("var20", varOList.get(i).getString("ZHLX"));        //20
            vpd.put("var21", varOList.get(i).getString("SWITCH"));        //21
            vpd.put("var22", varOList.get(i).getString("SWITCHTIME"));        //22
            vpd.put("var23", varOList.get(i).getString("TJBM"));        //23
            vpd.put("var24", varOList.get(i).getString("TJR"));        //24
            varList.add(vpd);
        }
        dataMap.put("varList", varList);
        ObjectExcelView erv = new ObjectExcelView();
        mv = new ModelAndView(erv, dataMap);
        FHLOG.save(Jurisdiction.getUsername(), "当前用户:"+Jurisdiction.getUsername()+"面签账户管理-->导出表格");				//记录日志

        return mv;
    }

    @GetMapping("/findByMQ")
    public Object findByMQ() throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        List<String> list1 = new ArrayList<>();
        List<Map<String, Object>> list = accountmanagementService.fandByMQ();

        for (Map<String, Object> stringObjectMap : list) {
            String name = (String) stringObjectMap.get("NAME");
            list1.add(name);
        }
        map.put("list", list);
        map.put("list1", list1);
        return map;

    }


}
