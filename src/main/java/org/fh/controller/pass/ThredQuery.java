package org.fh.controller.pass;

import org.fh.entity.Page;
import org.fh.mapper.dsno2.pass.TxzMapper;
import org.fh.util.SpringUtil;
import org.fh.util.Tools;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.concurrent.Callable;
import java.util.function.Function;
import java.util.stream.Collectors;

public class ThredQuery implements Callable<Map> {

    private String search;//查询条件 根据条件来定义该类的属性

    private int bindex;//当前页数

    private int num;//每页查询多少条

    private String YXQS;
    private String YXQZ;
    private String JBR;

    private Page page;//每次分页查出来的数据
    private Object List;

    @Autowired
    private TxzMapper txzMapper;

    public  ThredQuery(int bindex,int num,String ysqs,String yxqz,String jbr) {
        this.bindex=bindex;
        this.num=num;
        this.YXQS=ysqs;
        this.YXQZ=yxqz;
        this.JBR=jbr;
    }

    @Override
    public Map<String, Long> call() throws Exception {
        //返回数据给Future
        try {
            TxzMapper txzMapper = SpringUtil.getBean(TxzMapper.class);
            List<String> RoadAll = new ArrayList<>();
            for (String strList : txzMapper.getAllRoad(bindex,num,YXQS,YXQZ,JBR)) {
                if (Tools.notEmpty(strList)) {
                    RoadAll.addAll(Arrays.asList(strList.split(",")));
                }
            }
            // 分组计数
            Map<String, Long> groupRoadMap = RoadAll.stream().collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
            //最终返回的链表，{道路名称:所选条数,}
            Map<String, Long> finalMap = new LinkedHashMap<>();
            //分组, 计数和排序
            groupRoadMap.entrySet().stream()
                    .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                    .forEachOrdered(e -> finalMap.put(e.getKey(), e.getValue()));
            return finalMap;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

    }

}
