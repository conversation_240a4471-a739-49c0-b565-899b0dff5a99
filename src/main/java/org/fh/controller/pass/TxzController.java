package org.fh.controller.pass;

import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.fh.constant.AccountConstant;
import org.fh.controller.base.BaseController;
import org.fh.entity.Page;
import org.fh.entity.PageData;
import org.fh.entity.pass.ForceVo;
import org.fh.entity.pass.PASLICENSEWithBLOBs;
import org.fh.entity.pass.PaslicenseRoadpart;
import org.fh.mapper.dsno2.pass.PASLICENSEMapper;
import org.fh.mapper.dsno2.pass.PaslicenseRoadpartMapper;
import org.fh.mapper.dsno2.pass.RoaddeviceMapper;
import org.fh.mapper.dsno2.pass.SurveilMapper;
import org.fh.service.pass.*;
import org.fh.service.system.FHlogService;
import org.fh.service.system.UsersService;
import org.fh.util.*;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 说明：通行证
 * 作者：FH Admin QQ313596790
 * 时间：2020-05-26
 * 官网：www.fhadmin.org
 */
//@Controller
@Slf4j
@RestController
@RequestMapping("/txz")
public class TxzController extends BaseController {

    @Autowired
    private TxzService txzService;
    @Resource
    private SixInOne sixInOne;
    @Autowired
    private PASLICENSEService paslicenseService;
    @Autowired
    private SurveilMapper surveilMapper;
    private static Properties pros = DbFH.getPprVue();
    @Autowired
    private BlacklistService blacklistService;
    @Autowired
    private RoaddeviceMapper roaddeviceMapper;
    @Autowired
    private FHlogService FHLOG;
    @Autowired
    private PaslicenseRoadpartMapper paslicenseRoadpartMapper;
    @Autowired
    PASLICENSEMapper paslicenseMapper;
    @Autowired
    private RoadInfoService roadInfoService;
    @Autowired
    private AccountManagementService accountManagementService;
    @Autowired
    AppletsController appletsController;
    @Autowired
    private UsersService usersService;


    //发布库token
    private String Token = "";

    public String DesDncrypt(String param) {
        String info = "";
        try {
            info = DesEncryptor.desDncrypt(param, pros.getProperty("outintertfaceKey"));
        } catch (Exception e) {
            info = e.getMessage();
        } finally {
            return info;
        }
    }

   public static void main(String[] args) throws JSONException {
      String Content = "";
      String end = "lxFTOCFuzVc8WXFJKXgAZWkw6PKIn31yAzKkV/rZm0rE0POsSW88UN81GTP38nG/626HVIeF4upR\\r\\nt8UOlSmzgOjUirm9Wls9Wgbi/ekpe0XQo+OitpxH9BtFdWwf/SRLk3hm4R2n8Y39bWcVA5yikYzY\\r\\n/Nqi7fNTc9IIdabOEhUid/n84hzB0frteYeldRbCo6lvoQkptoTII+k97MLOMeGactNIIt4kwixm\\r\\nHeA+h4XXbI9XHONFR0/4EGp7JFp8wPlM+BMdX2HC+bkzyECKqqY/eo/gPPT+swA8yJUmvtAGhSZI\\r\\n+LWfxxFhaJW0IVqjwdQ1+cd9dRCDt7QGqwiwYf5qkg3URQb7daMgHb2ySpOV9g6K2MidLev1sugp\\r\\nG+cQzqXs4N1xOiEhmTs+pRNSDOWgAKjB+sEfYbSYQW11UshXjRx+vVFFsfbvipCSDde0K9fxSEJQ\\r\\nxKL5jSniIYxAIzFQQdXqTDbRHkr/5K2AWKLBuMv9hRiWI9yLmm8DKC0h65HJqztucmaL/i/02e0n\\r\\nWQrxzo44Gnbse4TeBN6Lp0oirYVb5esWZqseFKm2z0GBWkiXUo8nFGVQWEF+ujyKxnnX+Q4qY23Q\\r\\nhi5OAWTj4OiMj5FHf1lTu4e5rvEsnwMwbuUcbgQhdTfabfB4EZTbQB1upIUJizpjxmCJlFOjcN12\\r\\nIgpIu3L7T2VcTIZ0c6SsJ1sDDqZlnjfMSjOrU9PTqY6dY1hGNTrfCMD3DAK/jEaquHZ4W93+RgMm\\r\\nBJByAstqgoKbg32edM1dCADf/ofJoPrk4q/Ze3np3574n3q+KT8KQ+3t3hFF7nXxiEb6NBPcEAWK\\r\\nSKoBnOBQbZb7nA9GFVv9HmlKxevBdJaMyRhtQQnKWidHXBvO4g==";
      JSONObject a = new JSONObject();
      a.put("HPHM", "桂ADS761");
      a.put("HPZL", "02");

      try {
         Content = DesEncryptor.encrypt(a.toString(), pros.getProperty("outintertfaceKey"));
         System.out.println(DesEncryptor.desDncrypt(end.replaceAll("\\\\r\\\\n", ""), pros.getProperty("outintertfaceKey")));
         System.out.println(Content);
      } catch (Exception e) {
         e.printStackTrace();
      }

   }

    public String GetDev(String strParam) throws Exception {
        String ids = strParam;

        AtomicReference<String> message = new AtomicReference<>("");
        List<HashMap<Object,Object>> data =  roaddeviceMapper.getDepNumMap();
        for (String id : ids.split(",")) {
            data.forEach(item ->{
            if (item.get("ROADPARTID").equals(id)){
                message.updateAndGet(v -> v + item.get("SBBH") + ",");
            }
        });
        }

        if (message.get().length() > 0){
            return message.get().substring(0,message.get().length() - 1);
        }else {
            return message.get();
        }

    }

    public String GetDlsh(String strParam) throws Exception {
        String ids = strParam;

        AtomicReference<String> message = new AtomicReference<>("");
        List<HashMap<Object,Object>> data =  paslicenseRoadpartMapper.selectMap();
        for (String id : ids.split(",")) {
            data.forEach(item ->{
                if (item.get("ID").equals(id)){
                    message.updateAndGet(v -> v + item.get("DLHS") + ",");
                }
            });
        }

        if (message.get().length() > 0){
            return message.get().substring(0,message.get().length() - 1);
        }else {
            return message.get();
        }

    }

    /**
     * 01C21获取机动车基本信息
     *
     * @param
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/GetMPSData")
    @ResponseBody
    public Map<String, Object> GetMPSData(@RequestBody String strParam) throws Exception {
        com.alibaba.fastjson.JSONObject jsonParam = com.alibaba.fastjson.JSONObject.parseObject(DesDncrypt(strParam));
        Map<String, Object> map = new HashMap<String, Object>();
        String State = "1";
        String Message = "查询成功";
        String Value = "";
        String Content = "";

        String hpzl = jsonParam.getString("hpzl");
        String hphm = jsonParam.getString("hphm");
        String dabh = jsonParam.getString("dabh");
        String check = jsonParam.getString("check");
        //封装请求对象JSON
        JSONObject mapInfo = new JSONObject();
        mapInfo.put("xtlb", "01");
        mapInfo.put("hpzl", hpzl);
        mapInfo.put("hphm", hphm);
        mapInfo.put("dabh", "");
        List BackInfoList = new ArrayList();
        //添加需要的参数列表
        BackInfoList.add("clsbdh");
        BackInfoList.add("fdjh");
        BackInfoList.add("qzbfqz");
        BackInfoList.add("cllx");
        BackInfoList.add("syxz");
        BackInfoList.add("syr");
        BackInfoList.add("yxqz");
        BackInfoList.add("zt");
        try {
            Content = SixInOne.getInfo(mapInfo, BackInfoList).toString();
            this.FHLOG.save(IpUtils.getIpAddr(IpUtils.getRequestAttributes().getRequest()), hphm + "六合一请求结果:" + Content);
        } catch (Exception e) {
            State = "0";
            Message = e.getMessage();
        }
        map.put("State", State);
        map.put("Message", Message);
        map.put("Value", Value);
        if (check == null) {
            Content = DesEncryptor.encrypt(Content, pros.getProperty("outintertfaceKey"));
        }
        map.put("Content", Content);

        return map;
    }

    /**
     * 04c03 电子监控记录查询
     *
     * @param
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/MonitorRecord")
    @ResponseBody
    public Map<String, Object> MonitorRecord(@RequestBody String strParam) throws Exception {

        com.alibaba.fastjson.JSONObject jsonParam = com.alibaba.fastjson.JSONObject.parseObject(DesDncrypt(strParam));
        Map<String, Object> map = new HashMap<String, Object>();
        String State = "1";
        String Message = "查询成功";
        String Value = "";
        String Content = "";

        String hpzl = jsonParam.getString("hpzl");
        String hphm = jsonParam.getString("hphm");
        String xh = jsonParam.getString("xh");
        String check = jsonParam.getString("check");
        //封装请求对象JSON
        JSONObject mapInfo = new JSONObject();
        mapInfo.put("xtlb", "04");
        mapInfo.put("hpzl", hpzl);
        mapInfo.put("hphm", hphm);
        mapInfo.put("xh", xh);
        mapInfo.put("clbj", "0");
        List BackInfoList = new ArrayList();
        //添加需要的参数列表
        BackInfoList.add("clbj");
        BackInfoList.add("wfsj");
        BackInfoList.add("wfdd");
        BackInfoList.add("wfxw");

        try {
            JSONObject a = sixInOne.getInfo(mapInfo, BackInfoList);
            if (a.length() != 0){
                String wfxw = a.getString("wfxw");
                a.put("wfxw", surveilMapper.GetWFMS(wfxw));
                Content = a.toString();
                System.out.println(Content);
                FHLOG.save(IpUtils.getIpAddr(IpUtils.getRequestAttributes().getRequest()), hphm + "六合一请求结果:" + Content);
            }
        } catch (Exception e) {
            State = "0";
            Message = e.getMessage();
        }
        if (check == null) {
            Content = DesEncryptor.encrypt(Content, pros.getProperty("outintertfaceKey"));
        }
        map.put("State", State);
        map.put("Message", Message);
        map.put("Value", Value);
        map.put("Content", Content);

        return map;
    }

    /**
     * 通过序号获取数据
     *
     * @param
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(value = "/getById")
    public PASLICENSEWithBLOBs getById(@RequestParam(value = "XH") String xh) throws Exception {
        PASLICENSEWithBLOBs record = paslicenseService.getById(xh);
        String Rnumber = Jurisdiction.getRnumbers();
        if (Rnumber.contains(AccountConstant.BackKysnumber) || Rnumber.contains(AccountConstant.BackTqddnumber) || Rnumber.contains(AccountConstant.BackZxknumber)) {
            //判断是否有权限审核
            record.setPermissions(true);
        } else {
            record.setPermissions(false);
        }
        return record;
    }

    /**
     * 办理通行证
     *
     * @param
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(value = "/add")
    public Object add(@RequestBody com.alibaba.fastjson.JSONObject jsonParam) throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        List<PASLICENSEWithBLOBs> txzList = new ArrayList<>();
        List<Object> JsonList = new ArrayList<>();
        String JSONP = jsonParam.getString("txzList");
        txzList = JSONArray.toList(JSONArray.fromObject(JSONP), PASLICENSEWithBLOBs.class);//这里的t是Class<T>
        List<String> result = paslicenseService.selectByDj();
        for (int i = 0; i < txzList.size(); i++) {

            String HPHM = txzList.get(i).getHphm();
            String HPZL = txzList.get(i).getHpzl();

            com.alibaba.fastjson.JSONObject JSON = new com.alibaba.fastjson.JSONObject();
            JSON.put("HPHM", HPHM);
            JSON.put("HPZL", HPZL);
            try {
                PASLICENSEWithBLOBs record = txzList.get(i);
                //处理有效期
                String Syxrqs = JSONArray.fromObject(jsonParam.getString("txzList")).getJSONObject(i).getString("yxrqs");
                String Syxrqz = JSONArray.fromObject(jsonParam.getString("txzList")).getJSONObject(i).getString("yxrqz");
                SimpleDateFormat ssf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                record.setYxrqs(ssf.parse(Syxrqs + " 00:00:00"));
                record.setYxrqz(ssf.parse(Syxrqz + " 23:59:59"));
                //生成随机数
                Random r = new Random();
                DateFormat bf = new SimpleDateFormat("yyyyMMddHHmmssss");
                Calendar c = new GregorianCalendar();
                Date date = new Date();
                String format = bf.format(date);
                String xh = format + r.nextInt(1000);
                record.setXh(xh);
                c.setTime(date);
                c.add(Calendar.MINUTE, 2);
                date = c.getTime();
                String strSecond = bf.format(date);
                record.setTxzhm(strSecond + r.nextInt(1000));
                record.setJbr(Jurisdiction.getName());
                record.setTxsbbh(GetDev(record.getYlzd5()));
                //TODO 判断是否选择一二类路或者有违法记录，选择就判断组权限，前台组需要提交审核到后台组
                List<String> result2 = Arrays.asList(record.getYlzd5().split(","));
                List<String> intersection = result.stream().filter(item -> result2.contains(item)).collect(Collectors.toList());
                //TODO 判断车辆是否存在未过期长期通行证
                List<String> resmap = new ArrayList<>();
                List<Map<String, Object>> objectMap = blacklistService.overdue(HPHM.trim(), HPZL.trim());

                if (objectMap != null && objectMap.size() > 0) {
                    for (Map<String, Object> stringObjectMap : objectMap) {
                        String yxrqz = (String) stringObjectMap.get("YXRQZ").toString();
                        String time = DateUtil.getTime();

                        //计算相隔时间
                        SimpleDateFormat format2 = new SimpleDateFormat("yyyy-MM-dd");
                        Date yxrqz1 = format2.parse(yxrqz);
                        Date endDate = format2.parse(time);
                        long day = (yxrqz1.getTime() - endDate.getTime()) / (24 * 60 * 60 * 1000);
                        if (day > 0 && day >= 20) {
                            resmap.add("1");
                        }
                    }
                }
                String Message = txzList.get(i).getMessage();
                //判断强制报废期已过期和检验有效期已过期是否过期
                if (txzList.get(i).getMessage().contains("未能查询到该车牌信息")) {
                    JSON.put("Message", "办理失败,未能查询到该车牌信息");
                    JsonList.add(JSON);
                    continue;
                }
                if (txzList.get(i).getMessage().contains("手动输入")) {
                    record.setBz(record.getBz() + ";该车辆为手动输入办理车辆");
                }
                if (txzList.get(i).getMessage().contains("车辆状态异常")) {
                    JSON.put("Message", "办理失败,该车辆状态异常");
                    JsonList.add(JSON);
                    continue;
                }
                if (txzList.get(i).getMessage().contains("查询成功")) {
                    Message = "";
                }
                //判断提交的路段id是否与一二类路段id列表有交集
                if (intersection.size() > 0 || Tools.notEmpty(Message)) {
                    //判断Rnumbers是否是通行证前台组的三类角色
                    String Rnumber = Jurisdiction.getRnumbers();
                    if (Rnumber.contains(AccountConstant.BackKysnumber) || Rnumber.contains(AccountConstant.BackTqddnumber) || Rnumber.contains(AccountConstant.BackZxknumber)) {
                        //**********选择一二类路并且有异常情况同时是低权限
                        if (resmap.size() > 0) {
                            if (record.getZfz().equals("1")) {
                                //注销原有的通行证
                                paslicenseService.updateZx(record);
                                //覆盖主证
                                record.setZx("0");
                                record.setAuditing_status("0");
                                paslicenseService.update_txz(record);
                                JSON.put("Message", "办理成功,已覆盖原有通行证但您的权限不足已提交后台审批");
                                FHLOG.save(Jurisdiction.getUsername(), HPHM + "通行证--->办理成功,覆盖原有通行证但权限不足.xh:" + xh);                //记录日志
                                JsonList.add(JSON);
                            } else {
                                record.setAuditing_status("0");
                                paslicenseService.insert(record);
                                JSON.put("Message", "办理成功,已添加新的一条通行证但您的权限不足已提交后台审批");
                                FHLOG.save(Jurisdiction.getUsername(), HPHM + "通行证--->办理成功,添加新的通行证但权限不足.xh:" + xh);                //记录日志
                                JsonList.add(JSON);
                            }
                        } else {
                            record.setAuditing_status("0");
                            paslicenseService.insert(record);
                            JSON.put("Message", "您的权限不足已提交后台审批");
                            FHLOG.save(Jurisdiction.getUsername(), HPHM + "通行证--->权限不足已提交后台审批.xh:" + xh);                //记录日志
                            JsonList.add(JSON);
                        }
                        //****************有高级权限
                    } else {
                        //审批通过
                        if (resmap.size() > 0) {
                            if (record.getZfz().equals("1")) {
                                //注销原有的通行证
                                this.paslicenseService.updateZx(record);
                                //覆盖主证
                                record.setZx("0");
                                record.setAuditing_status("1");
                                paslicenseService.insert(record);
                                JSON.put("Message", "办理成功,已覆盖原有通行证");
                                JsonList.add(JSON);
                                FHLOG.save(Jurisdiction.getUsername(), HPHM + "通行证--->办理成功,已覆盖原有通行证.xh:" + xh);                //记录日志
                            } else {
                                record.setAuditing_status("1");
                                paslicenseService.insert(record);
                                JSON.put("Message", "办理成功,已添加新的一条通行证");
                                JsonList.add(JSON);
                                FHLOG.save(Jurisdiction.getUsername(), HPHM + "通行证--->办理成功,已添加新的一条通行证.xh:" + xh);                //记录日志
                            }
                        } else {
                            record.setAuditing_status("1");
                            paslicenseService.insert(record);
                            JSON.put("Message", "办理成功");
                            JsonList.add(JSON);
                            FHLOG.save(Jurisdiction.getUsername(),  HPHM + "通行证--->办理成功.xh:" + xh);                //记录日志
                        }
                    }
                }
                //****************没有异常
                else {
                    //审批通过
                    if (resmap.size() > 0) {
                        if (record.getZfz().equals("1")) {
                            //注销原有的通行证
                            this.paslicenseService.updateZx(record);
                            //覆盖主证
                            record.setZx("0");
                            record.setAuditing_status("1");
                            paslicenseService.insert(record);
                            JSON.put("Message", "办理成功,已覆盖原有通行证");
                            JsonList.add(JSON);
                            FHLOG.save(Jurisdiction.getUsername(), HPHM + "通行证--->办理成功,已覆盖原有通行证.xh:" + xh);                //记录日志
                        } else {
                            record.setAuditing_status("1");
                            paslicenseService.insert(record);
                            JSON.put("Message", "办理成功,已添加新的一条通行证");
                            JsonList.add(JSON);
                            FHLOG.save(Jurisdiction.getUsername(), HPHM + "通行证--->办理成功,已添加新的一条通行证.xh:" + xh);                //记录日志
                        }
                    } else {
                        record.setAuditing_status("1");
                        paslicenseService.insert(record);
                        JSON.put("Message", "办理成功");
                        JsonList.add(JSON);
                        FHLOG.save(Jurisdiction.getUsername(),  HPHM + "通行证--->办理成功.xh:" + xh);                //记录日志
                    }
                }
            }catch (Exception e){
                JSON.put("Message", "办理失败,网络状态异常");
                JsonList.add(JSON);
                // e.printStackTrace();
                log.error("通行证办理失败: param:{}, \nresult: {}", jsonParam, result, e);
            }
        }
        map.put("JsonList", JsonList);
        map.put("result", errInfo);
        return map;
    }


    /**
     * 一键续办
     *
     * @param
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(value = "/update")
    public Object update(@RequestParam(value = "XH") String xh) throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        PASLICENSEWithBLOBs record = paslicenseService.getById(xh);
        Date dNow = new Date();
        Date dBefore = new Date();
        Calendar calendar = Calendar.getInstance(); //得到日历
        calendar.setTime(dNow);                        //把当前时间赋给日历
        calendar.add(Calendar.MONTH, +3);     //设置为后3月
        dBefore = calendar.getTime();            //得到后3月的时间
        SimpleDateFormat ssf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd"); //设置时间格式
        String defaultStartDate = sdf.format(dBefore);    //格式化前3月的时间
        String defaultEndDate = sdf.format(dNow);    //格式化当前时间

        Random r = new Random();
        DateFormat bf = new SimpleDateFormat("yyyyMMddHHmmssss");
        Date date = new Date();
        String format = bf.format(date);
        Calendar c = new GregorianCalendar();
        String Newxh = format + r.nextInt(1000);
        c.setTime(date);
        c.add(Calendar.MINUTE, 2);
        date = c.getTime();
        String strSecond = bf.format(date);
        record.setTxzhm(strSecond + r.nextInt(1000));

        // TODO 判断车辆是否存在黑名单中
        //添加黑名单车辆先判断该号牌是否存在
        String zt = blacklistService.fandByHPId(record.getHphm().trim(), record.getHpzl().trim());
        if (zt != null) {
            errInfo = "该车辆为黑名单车辆";
            map.put("result", errInfo);
            return map;
        }
        //TODO 判断是否选择一二类路或者有违法记录，选择就判断组权限，前台组需要提交审核到后台组
        List<String> result = paslicenseService.selectByDj();
        List<String> result2 = Arrays.asList(record.getYlzd5().split(","));
        List<String> intersection = result.stream().filter(item -> result2.contains(item)).collect(Collectors.toList());
        //判断提交的路段id是否与一二类路段id列表有交集
        if (intersection.size() > 0 || record.getWfxw() != null || record.getWfxw() != "") {
            //判断Rnumbers是否是通行证前台组的三类角色
            String Rnumber = Jurisdiction.getRnumbers();
            if (Rnumber.contains(AccountConstant.BackKysnumber) || Rnumber.contains(AccountConstant.BackTqddnumber) || Rnumber.contains(AccountConstant.BackZxknumber)) {
                errInfo = "您有未处理违法行为或权限不足";
            } else {
                record.setYxrqs(ssf.parse(defaultEndDate + " 00:00:00"));
                record.setYxrqz(ssf.parse(defaultStartDate + " 23:59:59"));
                record.setYxrq(defaultEndDate + "至" + defaultStartDate);
                record.setXh(Newxh);
                record.setZx("0");
                record.setAuditing_status("1");
                record.setJbr(Jurisdiction.getName());
                paslicenseService.insert(record);
                FHLOG.save(Jurisdiction.getUsername(), record.getHphm() + "通行证--->续办成功.xh:" + Newxh);				//记录日志
            }
        } else {
            record.setYxrqs(ssf.parse(defaultEndDate + " 00:00:00"));
            record.setYxrqz(ssf.parse(defaultStartDate + " 23:59:59"));
            record.setYxrq(defaultEndDate + "至" + defaultStartDate);
            record.setXh(Newxh);
            record.setZx("0");
            record.setAuditing_status("1");
            record.setJbr(Jurisdiction.getName());
            paslicenseService.insert(record);
            FHLOG.save(Jurisdiction.getUsername(), record.getHphm() + "通行证--->续办成功.xh:" + Newxh);				//记录日志
        }
        map.put("result", errInfo);
        return map;
    }

    /**
     * 根据等级查询道路
     *
     * @param dj
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(value = "/get")
    public Object get(@RequestParam(value = "DJ") String dj) throws Exception {
        List<PaslicenseRoadpart> result = paslicenseService.select(dj);
        return result;
    }

    /**
     * 查询全部道路
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(value = "/getAllRoad")
    public Object getAllRoad() throws Exception {
        List<PaslicenseRoadpart> result = paslicenseService.selectAll();
        Map<String, List<PaslicenseRoadpart>> collect = result.stream().collect(Collectors.groupingBy(PaslicenseRoadpart::getDj));
        return collect;
    }

    /**
     * 查询道路列表
     */
    @RequestMapping(value = "/selectPage")
    @ResponseBody
    public Object selectPage(Page page) throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();

        String KEYWORDS = pd.getString("KEYWORDS");
        if (Tools.notEmpty(KEYWORDS)) {
            pd.put("KEYWORDS", KEYWORDS.trim().toLowerCase());
        }
        String DLJBKEYWORDS = pd.getString("DLJBKEYWORDS");
        if (Tools.notEmpty(DLJBKEYWORDS)) {
            pd.put("DLJBKEYWORDS", DLJBKEYWORDS.trim());
        }
        page.setPd(pd);
        List<PageData> varList = txzService.rowlistPage(page);    //列出roadInfo列表
        map.put("varList", varList);
        map.put("page", page);
        map.put("result", errInfo);

        return map;
    }

    /**
     * 修改
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/edit")
    @ResponseBody
    public Object edit(PASLICENSEWithBLOBs paslicense) throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        SimpleDateFormat sdf = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.US);

        String format2 = sdf.format(paslicense.getYxrqs());
        String format3 = sdf.format(paslicense.getYxrqz());
        Date parse = sdf.parse(format2);
        Date parse1 = sdf.parse(format3);

        paslicense.setYlzd5(paslicense.getYlzd5());
        paslicense.setYlzd6(paslicense.getYlzd6());

        if (paslicense.getAuditing_status()!=null){
            paslicense.setSpr(Jurisdiction.getName());
            paslicense.setSprq(new Date());
        }

        paslicense.setYxrqs(parse);  //有效日期始
        paslicense.setYxrqz(parse1);   //有效日期至
        paslicense.setGxsj(new Date()); //更新时间
        paslicenseService.update_txz(paslicense);
        map.put("result", errInfo);
        FHLOG.save(Jurisdiction.getUsername(), "审批管理--->修改成功.xh:"+paslicense.getXh());				//记录日志
        return map;
    }


    /**
     * 去修改页面获取数据
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/goEdit")
    @ResponseBody
    public Object goEdit() throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        pd = txzService.findById(pd);
        map.put("pd", pd);
        map.put("result", errInfo);
        return map;
    }


    /**
     * 删除
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/delete")
    @RequiresPermissions("txz:del")
    @ResponseBody
    public Object delete() throws Exception {
        Map<String, String> map = new HashMap<String, String>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        txzService.delete(pd);
        map.put("result", errInfo);                //返回结果
        FHLOG.save(Jurisdiction.getUsername(), "通行证--->删除成功.xh:"+pd.getString("XH"));				//记录日志



        return map;
    }


    /**
     * 列表
     *
     * @param page
     * @throws Exception
     */
    @RequestMapping(value = "/list")
    @ResponseBody
    public Object list(Page page) throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        String HPZLKEYWORDS = pd.getString("HPZLKEYWORDS");//关键词检索条件--号牌种类
        if (Tools.notEmpty(HPZLKEYWORDS)) {
            pd.put("HPZLKEYWORDS", Tools.getHpzl(HPZLKEYWORDS.trim()));
        }
        String HPHMKEYWORDS = pd.getString("HPHMKEYWORDS");//关键词检索条件--号牌号码
        if (Tools.notEmpty(HPHMKEYWORDS)) {
            pd.put("HPHMKEYWORDS", HPHMKEYWORDS.trim());
        }
        String JBRKEYWORDS = pd.getString("JBRKEYWORDS");//关键词检索条件--经办人
        if (Tools.notEmpty(JBRKEYWORDS)) {
            pd.put("JBRKEYWORDS", JBRKEYWORDS.trim());
        }
        String JDCSYRKEYWORDS = pd.getString("JDCSYRKEYWORDS");//关键词检索条件--机动车所有人
        if (Tools.notEmpty(JDCSYRKEYWORDS)) {
            pd.put("JDCSYRKEYWORDS", JDCSYRKEYWORDS.trim());
        }
        String CLLXKEYWORDS = pd.getString("CLLXKEYWORDS");//关键词检索条件--车辆类型
        if (Tools.notEmpty(CLLXKEYWORDS)) {
            pd.put("CXLXKEYWORDS", CLLXKEYWORDS.trim());
        }

        String STARTDJRQKEYWORDS = pd.getString("STARTDJRQKEYWORDS");//关键词检索条件--起始登记日期
        if (Tools.notEmpty(STARTDJRQKEYWORDS)) {

            //需要获取localDateTime格式时间，但yyyy-MM-dd格式日期字符串无法直接转换
            //字符转localdate
            LocalDate localDate = LocalDate.parse(STARTDJRQKEYWORDS, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            //localdate转date
            Date date = Date.from(localDate.atStartOfDay(ZoneOffset.ofHours(8)).toInstant());
            //date转localdatetime
            LocalDateTime STARTDJRQ = date.toInstant().atZone(ZoneOffset.ofHours(8)).toLocalDateTime();
            pd.put("STARTDJRQKEYWORDS", STARTDJRQ);
        } else {
            pd.put("STARTDJRQKEYWORDS", null);
        }

        String ENDDJRQKEYWORDS = pd.getString("ENDDJRQKEYWORDS");//关键词检索条件--结束登记日期
        if (Tools.notEmpty(ENDDJRQKEYWORDS)) {
            //需要获取localDateTime格式时间，但yyyy-MM-dd格式日期字符串无法直接转换
            //字符转localdate
            LocalDate localDate = LocalDate.parse(ENDDJRQKEYWORDS, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            //localdate转date
            Date date = Date.from(localDate.atStartOfDay(ZoneOffset.ofHours(8)).toInstant());
            //date转localdatetime
            LocalDateTime ENDDJRQ = date.toInstant().atZone(ZoneOffset.ofHours(8)).toLocalDateTime();
            pd.put("ENDDJRQKEYWORDS", ENDDJRQ);
        } else {
            pd.put("ENDDJRQKEYWORDS", null);
        }

        String YXQKEYWORDS = pd.getString("YXQKEYWORDS");//关键词检索条件--是否在有效期内
        if (Tools.notEmpty(YXQKEYWORDS)) {
            pd.put("msg", YXQKEYWORDS.trim());
            pd.put("YXQKEYWORDS", LocalDateTime.now());
        } else {
            pd.put("YXQKEYWORDS", null);
        }
        page.setPd(pd);
        List<PageData> varList = txzService.list(page);    //列出Txz列表
        map.put("varList", varList);
        map.put("page", page);
        map.put("result", errInfo);
        return map;
    }


    /**
     * 批量审批
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/updateAll")
    @ResponseBody
    public Object updateAll() throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        pd.put("GXSJ", new Date());
        pd.put("SPR", Jurisdiction.getName());
        pd.put("SPRQ", new Date());
        String DATA_IDS = pd.getString("DATA_IDS");
        if (Tools.notEmpty(DATA_IDS)) {
            String ArrayDATA_IDS[] = DATA_IDS.split(",");
            pd.put("ArrayDATA_IDS", ArrayDATA_IDS);
            txzService.updateAll(pd);
            FHLOG.save(Jurisdiction.getUsername(), "审批管理--->批量审批成功.xh:"+DATA_IDS);				//记录日志
            errInfo = "success";
        } else {
            errInfo = "error";
        }
        map.put("result", errInfo);                //返回结果
        return map;
    }

    /**
     * 一键审批
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/updateById")
    @ResponseBody
    public Object updateById() throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        pd.put("GXSJ", new Date());
        pd.put("SPRQ", new Date());
        pd.put("SPR", Jurisdiction.getName());
        txzService.updateById(pd);
        map.put("result", errInfo);                //返回结果
        FHLOG.save(Jurisdiction.getUsername(), "审批管理--->一键审批成功.xh:"+pd.getString("XH"));				//记录日志

        return map;
    }


    /**
     * 批量删除
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/deleteAll")
    @RequiresPermissions("txz:del")
    @ResponseBody
    public Object deleteAll() throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        String DATA_IDS = pd.getString("DATA_IDS");
        if (Tools.notEmpty(DATA_IDS)) {
            String ArrayDATA_IDS[] = DATA_IDS.split(",");
            txzService.deleteAll(ArrayDATA_IDS);
            FHLOG.save(Jurisdiction.getUsername(), "通行证--->批量删除成功.xh:"+DATA_IDS);				//记录日志
            errInfo = "success";
        } else {
            errInfo = "error";
        }
        map.put("result", errInfo);                //返回结果
        return map;
    }

    /**
     * 导出到excel
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/excel")
    @RequiresPermissions("toExcel")
    public ModelAndView exportExcel() throws Exception {
        ModelAndView mv = new ModelAndView();
        PageData pd = new PageData();
        pd = this.getPageData();
        Map<String, Object> dataMap = new HashMap<String, Object>();
        List<String> titles = new ArrayList<String>();
        titles.add("序号");    //1
        titles.add("通行证类型");    //2
        titles.add("通行证号码");    //3
        titles.add("号牌种类");    //4
        titles.add("号牌号码");    //5
        titles.add("车辆类型");    //6
        titles.add("机动车所有人");    //7
        titles.add("路线");    //8
        titles.add("有期日期起");    //9
        titles.add("有期日期止");    //10
        titles.add("经办人");    //11
        titles.add("更新时间");    //12
        titles.add("登记日期");    //13
        titles.add("审批人");    //14
        titles.add("审批标志");    //15
        titles.add("审批日期");    //16
        titles.add("高峰时段");    //17
        titles.add("有效时间");    //18
        titles.add("有效日期");    //19
        titles.add("路段加注");    //20
        titles.add("备注");    //21
        titles.add("打印标志 0-未打印  1-已打印");    //22
        titles.add("通行证状态（1可用0删除）");    //23
        titles.add("预留字段 手机号码");    //24
        titles.add("预留字段");    //25
        titles.add("预留字段 2019新版道路id 使用电警平台数据");    //26
        titles.add("预留字段 2019新版 禁行道路代码");    //27
        titles.add("是否注销 0-未注销  1-已注销");    //28
        titles.add("是否拒绝 0-未拒绝  1-已拒绝");    //29
        titles.add("拒绝原因");    //30
        titles.add("是否一级审批 0-null 1-已审批  2-待审批");    //31
        titles.add("一级审批人");    //32
        titles.add("是否二级审批 0-null 1-已审批  2-待审批");    //33
        titles.add("二级审批人");    //34
        titles.add("是否三级审批 0-null 1-已审批  2-待审批");    //35
        titles.add("三级审批人");    //36
        titles.add("2代表不存在  1 2 3 级道路审批");    //37
        titles.add("1-是 0-否  临时审批");    //38
        titles.add("1-是 0-否  临时审批是否");    //39
        titles.add("选择设备id");    //40
        titles.add("来源");    //41
        titles.add("选择设备名称");    //42
        titles.add("原始设备时段");    //43
        titles.add("临时通行证(1临时 0长期)");    //44
        titles.add("通行设备编号");    //45
        titles.add("道路级别");    //46
        titles.add("通行或禁行 1通行 2禁行");    //47
        titles.add("主副证 1主证 2副证");    //48
        titles.add("同类匹配，为空表示未检测");    //49
        titles.add("设备时段");    //50
        titles.add("是否已更新");    //51
        dataMap.put("titles", titles);
        List<PageData> varOList = txzService.listAll(pd);
        List<PageData> varList = new ArrayList<PageData>();
        for (int i = 0; i < varOList.size(); i++) {
            PageData vpd = new PageData();
            vpd.put("var1", varOList.get(i).getString("XH"));        //1
            vpd.put("var2", varOList.get(i).getString("TXZLX"));        //2
            vpd.put("var3", varOList.get(i).getString("TXZHM"));        //3
            vpd.put("var4", varOList.get(i).getString("HPZL"));        //4
            vpd.put("var5", varOList.get(i).getString("HPHM"));        //5
            vpd.put("var6", varOList.get(i).getString("CLLX"));        //6
            vpd.put("var7", varOList.get(i).getString("SYR"));        //7
            vpd.put("var8", varOList.get(i).getString("LX"));        //8
            vpd.put("var9", varOList.get(i).getString("YXRQS"));        //9
            vpd.put("var10", varOList.get(i).getString("YXRQZ"));        //10
            vpd.put("var11", varOList.get(i).getString("JBR"));        //11
            vpd.put("var12", varOList.get(i).getString("GXSJ"));        //12
            vpd.put("var13", varOList.get(i).getString("DJRQ"));        //13
            vpd.put("var14", varOList.get(i).getString("SPR"));        //14
            vpd.put("var15", varOList.get(i).getString("SPBJ"));        //15
            vpd.put("var16", varOList.get(i).getString("SPRQ"));        //16
            vpd.put("var17", varOList.get(i).getString("GFSD"));        //17
            vpd.put("var18", varOList.get(i).getString("YXSJ"));        //18
            vpd.put("var19", varOList.get(i).getString("YXRQ"));        //19
            vpd.put("var20", varOList.get(i).getString("LDJZ"));        //20
            vpd.put("var21", varOList.get(i).getString("BZ"));        //21
            vpd.put("var22", varOList.get(i).getString("DYBJ"));        //22
            vpd.put("var23", varOList.get(i).getString("ZT"));        //23
            vpd.put("var24", varOList.get(i).getString("YLZD1"));        //24
            vpd.put("var25", varOList.get(i).getString("YLZD2"));        //25
            vpd.put("var26", varOList.get(i).getString("YLZD3"));        //26
            vpd.put("var27", varOList.get(i).getString("YLZD4"));        //27
            vpd.put("var28", varOList.get(i).getString("ZX"));        //28
            vpd.put("var29", varOList.get(i).getString("JUJUE"));        //29
            vpd.put("var30", varOList.get(i).getString("JUETEXT"));        //30
            vpd.put("var31", varOList.get(i).getString("SPONE"));        //31
            vpd.put("var32", varOList.get(i).getString("SPONEPEOPLE"));        //32
            vpd.put("var33", varOList.get(i).getString("SPTWO"));        //33
            vpd.put("var34", varOList.get(i).getString("SPTWOPEOPLE"));        //34
            vpd.put("var35", varOList.get(i).getString("SPTHREE"));        //35
            vpd.put("var36", varOList.get(i).getString("SPTHREEPEOPLE"));        //36
            vpd.put("var37", varOList.get(i).getString("SPBJ2"));        //37
            vpd.put("var38", varOList.get(i).getString("LSSP"));        //38
            vpd.put("var39", varOList.get(i).getString("LSSPBJ"));        //39
            vpd.put("var40", varOList.get(i).getString("YLZD5"));        //40
            vpd.put("var41", varOList.get(i).getString("LY"));        //41
            vpd.put("var42", varOList.get(i).getString("YLZD6"));        //42
            vpd.put("var43", varOList.get(i).getString("YSSBSD"));        //43
            vpd.put("var44", varOList.get(i).getString("LSTXZ"));        //44
            vpd.put("var45", varOList.get(i).getString("TXSBBH"));        //45
            vpd.put("var46", varOList.get(i).getString("DLJB"));        //46
            vpd.put("var47", varOList.get(i).getString("TXJX"));        //47
            vpd.put("var48", varOList.get(i).getString("ZFZ"));        //48
            vpd.put("var49", varOList.get(i).getString("TLPP"));        //49
            vpd.put("var50", varOList.get(i).getString("SBSD"));        //50
            vpd.put("var51", varOList.get(i).getString("SFYGX"));        //51
            varList.add(vpd);
        }
        dataMap.put("varList", varList);
        ObjectExcelView erv = new ObjectExcelView();
        mv = new ModelAndView(erv, dataMap);
        FHLOG.save(Jurisdiction.getUsername(), "当前用户:"+Jurisdiction.getUsername()+"通行证--->导出表格");				//记录日志

        return mv;
    }

    @GetMapping("/fandByHPZL")
    @ResponseBody
    public Object fandByHPZL() {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        List<Map<String, Object>> list = txzService.fandByHPZL();
        map.put("result", errInfo);
        map.put("list", list);
        return map;
    }

    @GetMapping("/fandBySPZT")
    @ResponseBody
    public Object fandBySPZT() {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        List<Map<String, Object>> list = txzService.fandBySPZT();
        map.put("result", errInfo);
        map.put("list", list);
        return map;
    }

    /**
     * 01C21获取机动车基本信息
     *
     * @param jsonParam
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/GetMPSData2")
    @ResponseBody
    public Map<String, Object> GetMPSData2(@RequestBody com.alibaba.fastjson.JSONObject jsonParam) throws Exception {

        Map<String, Object> map = new HashMap<String, Object>();
        String State = "1";
        String Message = "查询成功";
        String Value = "";
        String Content = "";
        String hpzl = jsonParam.getString("hpzl");
        String hphm = jsonParam.getString("hphm");
        //封装请求对象JSON
        JSONObject mapInfo = new JSONObject();
        mapInfo.put("xtlb", "01");
        mapInfo.put("hpzl", hpzl);
        mapInfo.put("hphm", hphm);
        String dabh = jsonParam.getString("dabh");
        //判断前端传值是否需要加密
        String check = jsonParam.getString("check");
        //添加黑名单车辆先判断该号牌是否存在
        String zt = blacklistService.fandByHPId(jsonParam.getString("hphm").trim(), hpzl.trim());
        if (zt != null) {
            State = "2";
        }
        List BackInfoList = new ArrayList();
        //添加需要的参数列表
        BackInfoList.add("clsbdh");
        BackInfoList.add("fdjh");
        BackInfoList.add("qzbfqz");
        BackInfoList.add("cllx");
        BackInfoList.add("syxz");
        BackInfoList.add("syr");
        BackInfoList.add("yxqz");
        BackInfoList.add("zt");
        try {
            JSONObject data = sixInOne.getInfo(mapInfo, BackInfoList);
            System.out.println(data);
            FHLOG.save(IpUtils.getIpAddr(IpUtils.getRequestAttributes().getRequest()), hphm + "六合一请求结果:" + data);
            data.put("zt",ZtUtil.getZTInfo(data.getString("zt")));
            System.out.println(data);
            if (data.length() == 0){
                return null;
            }
            Content = data.toString();
            String syr = data.getString("syr");
            String zt2 = blacklistService.fandBySYR(syr);
            if (Tools.notEmpty(zt2)) {
                State = "2";
            }
        } catch (Exception e) {
            return null;
        }
        List<Map<String, Object>> objectMap = blacklistService.overdue(jsonParam.getString("hphm"), hpzl);
        if (objectMap != null && objectMap.size() > 0) {

            for (Map<String, Object> stringObjectMap : objectMap) {
                String yxrqz = (String) stringObjectMap.get("YXRQZ").toString();
                String zx = (String) stringObjectMap.get("ZX").toString();
                String time = DateUtil.getTime();

                //计算相隔时间
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                Date yxrqz1 = format.parse(yxrqz);
                Date endDate = format.parse(time);
                long day = (yxrqz1.getTime() - endDate.getTime()) / (24 * 60 * 60 * 1000);
                if (zx.equals("0")){
                    if (day > 0 && day >= 20) {
                        Message += "该车辆存在未过期通行证 ;";
                    }
                }

            }
        }
        map.put("State", State);
        map.put("Message", Message);
        map.put("Value", Value);
        if (check == null) {
            Content = DesEncryptor.encrypt(Content, pros.getProperty("outintertfaceKey"));
        }
        map.put("Content", Content);
        return map;
    }

//    /**
//     * 04c03 电子监控记录查询
//     *
//     * @param jsonParam
//     * @return
//     * @throws Exception
//     */
//    @PostMapping(value = "/MonitorRecord2")
//    @ResponseBody
//    public Map<String, Object> MonitorRecord2(@RequestBody com.alibaba.fastjson.JSONObject jsonParam) throws Exception {
//
//        Map<String, Object> map = new HashMap<String, Object>();
//        String State = "1";
//        String Message = "查询成功";
//        String Value = "";
//        String Content = "";
//        String hpzl = jsonParam.getString("hpzl");
//        String hphm = jsonParam.getString("hphm");
//        String xh = jsonParam.getString("xh");
//        String check = jsonParam.getString("check");
//        //封装请求对象JSON
//        JSONObject mapInfo = new JSONObject();
//        mapInfo.put("xtlb", "04");
//        mapInfo.put("hpzl", hpzl);
//        mapInfo.put("hphm", hphm);
//        mapInfo.put("xh", xh);
//        mapInfo.put("clbj", "0");
//        List BackInfoList = new ArrayList();
//        //添加需要的参数列表
//        BackInfoList.add("clbj");
//        BackInfoList.add("wfsj");
//        BackInfoList.add("wfdd");
//        BackInfoList.add("wfxw");
//        try {
//            JSONObject a = sixInOne.getInfo(mapInfo, BackInfoList);
//            if (a.length() != 0){
//                String wfxw = a.getString("wfxw");
//                a.put("wfxw", "违法行为 ：" + surveilMapper.GetWFMS(wfxw));
//                Content = a.toString();
//                System.out.println(Content);
//            }
//        } catch (Exception e) {
//            State = "0";
//            Message = e.getMessage();
//        }
//        if (check == null) {
//            Content = DesEncryptor.encrypt(Content, pros.getProperty("outintertfaceKey"));
//        }
//        map.put("State", State);
//        map.put("Message", Message);
//        map.put("Value", Value);
//        map.put("Content", Content);
//
//        return map;
//    }

    @GetMapping("/sjtj")
    @ResponseBody
    public Object sjtj(String ysqs, String yxqz,String jbr) {
        Map<String, Object> map = new HashMap<>();
        String State = "1";
        String Message = "查询成功";

        Map<String, Object> resMap = txzService.GetSjtj(ysqs, yxqz,jbr);

        map.put("State", State);
        map.put("Message", Message);
        map.put("list", resMap);
        return map;
    }

    /**
     * 列表
     *
     * @param page
     * @throws Exception
     */
    @RequestMapping(value = "/selectAll")
    @ResponseBody
    public Object selectAll(Page page) throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        String ACCOUNT = pd.getString("ACCOUNT");//关键词检索条件--账号
        if (Tools.notEmpty(ACCOUNT)) {
            pd.put("ACCOUNTKEYWORDS", ACCOUNT.trim());
        }
        String ENTERPRISENAME = pd.getString("ENTERPRISENAME");//关键词检索条件--企业名称
        if (Tools.notEmpty(ENTERPRISENAME)) {
            pd.put("ENTERPRISENAMEKEYWORDS", ENTERPRISENAME.trim());
        }
        String ZHLX = pd.getString("ZHLX");//关键词检索条件--账号类型
        if (Tools.notEmpty(ZHLX)) {
            pd.put("ZHLXKEYWORDS", ZHLX.trim());
        }
        String TJBM = pd.getString("TJBM");//关键词检索条件--添加部门
        if (Tools.notEmpty(TJBM)) {
            pd.put("TJBMKEYWORDS", TJBM.trim());
        }
        pd.put("SHZT", "1");

        page.setPd(pd);
        List<PageData> varList = accountManagementService.list(page);
        map.put("varList", varList);
        map.put("page", page);
        map.put("result", errInfo);
        return map;
    }

    /**
     * 列表
     *
     * @param page
     * @throws Exception
     */
    @RequestMapping(value = "/selectCar")
    @ResponseBody
    public Object selectCar(Page page) throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();


        String ENTERPRISEID = pd.getString("ENTERPRISEID");//关键词检索条件--号牌种类
        if (Tools.notEmpty(ENTERPRISEID)) {
            pd.put("ENTERPRISEID", ENTERPRISEID.trim());
        }

        String HPZL = pd.getString("HPZL");//关键词检索条件--号牌种类
        if (Tools.notEmpty(HPZL)) {
            pd.put("VEHTYPE", HPZL.trim());
        }
        String HPHM = pd.getString("HPHM");//关键词检索条件--号牌号码
        if (Tools.notEmpty(HPHM)) {
            pd.put("VEHNUM", HPHM.trim());
        }

        page.setPd(pd);
        List<PageData> varList = accountManagementService.selectCar(page);

        map.put("varList", varList);
        map.put("page", page);
        map.put("result", errInfo);
        return map;
    }

    /**
     * 获取车辆状态信息
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/selectMess")
    @ResponseBody
    public Map<String, Object> selectMess(@RequestBody Map<String, Object> data) throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        List<ForceVo> varList = new ArrayList<>();
        List<Object> JsonList = new ArrayList<>();

        varList = JSONArray.toList(JSONArray.fromObject(data.get("varList")), ForceVo.class);
        for (int i = 0; i < varList.size(); i++) {
            String Message = "";
            String HPHM = varList.get(i).getHPHM();
            String HPZL = varList.get(i).getHPZLNUM();
            com.alibaba.fastjson.JSONObject JSON = new com.alibaba.fastjson.JSONObject();
            JSON.put("HPHM", HPHM);
            JSON.put("HPZL", HPZL);
            //查询车辆基本信息
            org.json.JSONObject b = new org.json.JSONObject();
            b = appletsController.getSIX(HPZL, HPHM, "01");

            try {
                if (b.length() > 0) {
                    System.out.println(b);
                    JSON.put("CLLX", b.getString("cllx"));
                    JSON.put("SYR", b.getString("syr"));
                    Date date = new Date();
                    Date QZBFQZ = DateUtil.fomatDate(b.getString("qzbfqz"));
                    Date YXQZ = DateUtil.fomatDate(b.getString("yxqz"));
                    String zt2 = blacklistService.fandBySYR(b.getString("syr"));
                    JSON.put("SYXZ", b.getString("syxz"));
                    JSON.put("FDJH", b.getString("fdjh"));
                    JSON.put("QZBFQZ", b.getString("qzbfqz"));
                    JSON.put("YXQZ", b.getString("yxqz"));
                    JSON.put("CLSBDH", b.getString("clsbdh"));
                    JSON.put("ZT",b.getString("zt"));
                    if (Tools.notEmpty(zt2)) {
                        Message += "黑名单车辆 ;";
                    }
                    if (ZtUtil.getZTInfo(b.getString("zt")) != "正常") {
                        Message = Message + "车辆状态异常：" + ZtUtil.getZTInfo(b.getString("zt")) + " ;";
                    }
                } else {
                    Message += "未能查询到该车牌信息 ;";
                }
            } catch (Exception e) {
                System.out.println(e.getMessage());
            }
            String zt = blacklistService.fandByHPId(HPHM.trim(), HPZL.trim());
            if (zt != null) {
                Message += "黑名单车辆 ;";
            }
            List<Map<String, Object>> objectMap = blacklistService.overdue(HPHM.trim(), HPZL.trim());

            if (objectMap != null && objectMap.size() > 0) {
                for (Map<String, Object> stringObjectMap : objectMap) {
                    String yxrqz = (String) stringObjectMap.get("YXRQZ").toString();
                    String time = DateUtil.getTime();

                    //计算相隔时间
                    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                    Date yxrqz1 = format.parse(yxrqz);
                    Date endDate = format.parse(time);
                    long day = (yxrqz1.getTime() - endDate.getTime()) / (24 * 60 * 60 * 1000);
                    if (day > 0 && day >= 20) {
                        Message += "该车辆存在未过期通行证 ;";
                    }
                }
            }
//            //查询违法行为信息
//            org.json.JSONObject a = new org.json.JSONObject();
//            a = appletsController.getSIX(HPZL, HPHM, "04");
//            //校验信息
//            try {
//                if (a.length() > 0) {
//                    if (a.getString("wfxw") != null || a.getString("wfxw") != "") {
//                        Message += "违法行为 ：" + surveilMapper.GetWFMS(a.getString("wfxw"))+" ;";
//                    }
//                }
//            } catch (Exception e) {
//                System.out.println(e.getMessage());
//            }
            JSON.put("Message", Message);
            JsonList.add(JSON);
        }
        map.put("varList", JsonList);
        map.put("result", errInfo);
        return map;
    }

    /**
     * 查询黑名单
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/BackCar")
    @ResponseBody
    public Object BackCar(String HPHM,String HPZL,String SYR)throws Exception{
        Map<String, Object> map = new HashMap<String, Object>();
        String Message = "";
        String zt = blacklistService.fandByHPId(HPHM, HPZL);
        if (zt == null) {
            String zt2 = blacklistService.fandBySYR(SYR);
            if (zt2 != null) {
                Message += "黑名单车辆 ;";
            }
        }else {
            Message += "黑名单车辆 ;";
        }
        List<Map<String, Object>> objectMap = blacklistService.overdue(HPHM, HPZL);
        if (objectMap != null && objectMap.size() > 0) {

            for (Map<String, Object> stringObjectMap : objectMap) {
                String yxrqz = (String) stringObjectMap.get("YXRQZ").toString();
                String zx = (String) stringObjectMap.get("ZX").toString();
                String time = DateUtil.getTime();

                //计算相隔时间
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                Date yxrqz1 = format.parse(yxrqz);
                Date endDate = format.parse(time);
                long day = (yxrqz1.getTime() - endDate.getTime()) / (24 * 60 * 60 * 1000);
                if (zx.equals("0") && day > 0L && day >= 20L) {
                    Message += "该车辆存在未过期通行证 ;";
                }

            }
        }

        map.put("Message",Message);
        return map;
    }



    /**
     * 判断用户是否有权限提交
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/hasButton")
    @ResponseBody
    public Object hasButton() throws Exception {
        Map<String, String> map = new HashMap<String, String>();
        String errInfo = "true";
        String Rnumber = Jurisdiction.getRnumbers();
        if (Rnumber.contains(AccountConstant.BackKysnumber) || Rnumber.contains(AccountConstant.BackTqddnumber) || Rnumber.contains(AccountConstant.BackZxknumber)) {
            errInfo = "false";
        }
        map.put("result", errInfo);                //返回结果
        return map;
    }

    @RequestMapping({"/datalist"})
    @ResponseBody
    public Object datalist(Page page) throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String success = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        if (pd.getString("HPZLKEYWORDS") == null) {
            pd.put("HPZLKEYWORDS", pd.getString("HPZLKEYWORDS"));
        }

        String HPZLKEYWORDS = pd.getString("HPZLKEYWORDS");
        if (Tools.notEmpty(HPZLKEYWORDS)) {
            pd.put("HPZLKEYWORDS", Tools.getHpzl(HPZLKEYWORDS.trim()));
        }

        String HPHMKEYWORDS = pd.getString("HPHMKEYWORDS");
        if (Tools.notEmpty(HPHMKEYWORDS)) {
            pd.put("HPHMKEYWORDS", HPHMKEYWORDS.trim());
        }

        String JBRKEYWORDS = pd.getString("JBRKEYWORDS");
        if (Tools.notEmpty(JBRKEYWORDS)) {
            pd.put("JBRKEYWORDS", JBRKEYWORDS.trim());
        }

        String JDCSYRKEYWORDS = pd.getString("JDCSYRKEYWORDS");
        if (Tools.notEmpty(JDCSYRKEYWORDS)) {
            pd.put("JDCSYRKEYWORDS", JDCSYRKEYWORDS.trim());
        }

        String CLLXKEYWORDS = pd.getString("CLLXKEYWORDS");
        if (Tools.notEmpty(CLLXKEYWORDS)) {
            pd.put("CXLXKEYWORDS", CLLXKEYWORDS.trim());
        }

        String STARTDJRQKEYWORDS = pd.getString("STARTDJRQKEYWORDS");
        if (Tools.notEmpty(STARTDJRQKEYWORDS)) {
            pd.put("STARTDJRQKEYWORDS", STARTDJRQKEYWORDS + " 00:00:00");
        } else {
            pd.put("STARTDJRQKEYWORDS", null);
        }

        String ENDDJRQKEYWORDS = pd.getString("ENDDJRQKEYWORDS");
        if (Tools.notEmpty(ENDDJRQKEYWORDS)) {
            pd.put("ENDDJRQKEYWORDS", ENDDJRQKEYWORDS + " 23:59:59");
        } else {
            pd.put("ENDDJRQKEYWORDS", null);
        }

        String YXQKEYWORDS = pd.getString("YXQKEYWORDS");
        if (Tools.notEmpty(YXQKEYWORDS)) {
            pd.put("msg", YXQKEYWORDS.trim());
            pd.put("YXQKEYWORDS", LocalDateTime.now());
        } else {
            pd.put("YXQKEYWORDS", null);
        }

        page.setPd(pd);
        List<String> lsit = this.usersService.userlist();
        List MJMap = new ArrayList();
        List QZMap = new ArrayList();
        List countMap = new ArrayList();
        List varList = this.txzService.datalist(page);
        Iterator it = varList.iterator();
        Integer count = 0;

        while(it.hasNext()) {
            Map dataMap = (Map)it.next();
            String jbr = (String)dataMap.get("JBR");
            if (jbr != null) {
                if (!lsit.contains(jbr)) {
                    QZMap.add(dataMap);
                } else {
                    MJMap.add(dataMap);
                }

                count = count + Integer.parseInt(dataMap.get("ZS").toString());
            }
        }

        countMap.addAll(MJMap);
        countMap.addAll(QZMap);
        map.put("count", count);
        map.put("varList", countMap);
        map.put("success", success);
        map.put("page", page);
        return map;
    }
}
