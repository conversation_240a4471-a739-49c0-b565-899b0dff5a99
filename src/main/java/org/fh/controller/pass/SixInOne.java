package org.fh.controller.pass;

import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import org.fh.util.MD5;
import org.fh.util.Soap2Json;
import org.jdom.Document;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Properties;

import static org.fh.util.DbFH.getPprVue;

/**
 * auth :lj
 */
@RestController
public class SixInOne {

    private static Properties pros = getPprVue();

    public static String getToken() throws Exception {
        String username = pros.getProperty("interfaceUsername");
        //用户名+密码 MD5加密
        String password = MD5.md5(username + pros.getProperty("interfacePwd"));
        //构造参数进行请求，获取TOKEN
        HttpResponse<String> token = Unirest.get(pros.getProperty("intertfaceUrl") + "api/GetToken?username=" + username + "&password=" + password.toLowerCase())
                .header("Content-Type", "application/json")
                .header("cache-control", "no-cache")
                .header("Postman-Token", "ad3aac2e-bd3a-4169-b799-f8a9052cd387")
                .asString();

        JSONObject jsonObj = new JSONObject(token);
        JSONObject body = new JSONObject(jsonObj.getString("body"));
        String Token = body.getJSONObject("msg").getString("Token");
        return Token;
    }
    /**
     * @param map 参数， map 请求参数 , BackInfoList 选择返回参数列表 , api请求地址 GetJDCData/GetMPSData
     * @return
     * @throws Exception
     */
    public static JSONObject getInfo(JSONObject map , List BackInfoList) throws JSONException {
        String api = null;
        JSONObject BackInfo = new JSONObject();
        //判断传入参数map的xtlb是01还是04
        if (map.getString("xtlb").equals("01")){
            //判断传入参数map的hphm是否含桂，有桂则用广西接口01c21,没有则用全国接口B0101
            if (map.getString("hphm").contains("桂A")){
//                //如果是南宁车辆则查01FB21
//                if (map.getString("hphm").substring(1,2).equals("A")){
//
//                    map.put("jkid", "01FB21");
//                    api = "GetMPSData";
//                }else{
//                     map.put("jkid", "01C21");
                    map.put("jkid", "01FB21");
                    map.put("hphm", map.getString("hphm").substring(1));
                    api = "GetFBKData";
//                }

            }else {
                map.put("jkbs", "B0101");
                api = "GetJDCData";
            }
        }else {
            map.put("jkid", "04C03");
            api = "GetMPSData";
        }
        HttpResponse<String> response = null;
        try {
            System.out.println(map);
            response = Unirest.post(pros.getProperty("intertfaceUrl") + "OriginalGongAn/" + api)
                    .field("token", getToken())
                    .field("param", map)
                    .asString();
        } catch (Exception e) {
            System.out.println("result1: " + e.getMessage());
            // BackInfo.put("result", e);
        }
        String RET = null;
        try {
            RET = response.getBody();
            // System.out.println("result " + RET);
        } catch (Exception e) {
            System.out.println("result2: " + e.getMessage());
            // BackInfo.put("result", e);
        }
        // JSONObject jsonresponse = new JSONObject(response);
        // JSONObject jsonbody = new JSONObject(jsonresponse.getString("body"));
        // String msg = jsonbody.getString("msg");
        // try {
        //     RET = DesEncryptor.desDncrypt(msg, pros.getProperty("intertfaceKey"));
        // } catch (Exception e) {
        //     BackInfo.put("result", e);
        // }
        // if (jsonbody.get("success").toString().equals("true")) {
            Document document = Soap2Json.strXmlToDocument(RET.replace("<?xml version=\"1.0\" encoding=\"GBK\"?>", ""));
            //取节点
            if (document != null) {
                BackInfoList.forEach(info -> {
                    try {
                        BackInfo.put((String) info, Soap2Json.getValueByElementName(document, (String) info));
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                });
            }
        // }
        return BackInfo;
    }
}
