//package org.fh.controller.pass;
package org.fh.controller.pass;

import net.sf.json.JSONArray;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.fh.constant.AccountConstant;
import org.fh.controller.base.BaseController;
import org.fh.entity.Page;
import org.fh.entity.PageData;
import org.fh.entity.pass.PaslicenseEnterpris;
import org.fh.entity.pass.PaslicenseEnterprisVeh;
import org.fh.entity.pass.VehicleImageBean;
import org.fh.mapper.dsno2.pass.SurveilMapper;
import org.fh.service.pass.AccountManagementService;
import org.fh.service.pass.AppletsService;
import org.fh.service.pass.BlacklistService;
import org.fh.service.pass.VehicleApprovalService;
import org.fh.service.system.FHlogService;
import org.fh.util.*;
import org.json.JSONException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 说明：区域通行证--面签车辆审批
 * 作者：lj
 * 时间：2020-05-27
 * 官网：www.fhadmin.org
 */
//TODO 条件查询企业名称
@Controller
@RequestMapping("/vehicleapproval")
public class VehicleController extends BaseController {
    @Autowired
    private BlacklistService blacklistService;
    @Autowired
    private VehicleApprovalService vehicleapprovalService;
    @Autowired
    private TxzController txzController;
    @Autowired
    private AccountManagementService accountManagementService;
    @Autowired
    AppletsService appletsService;
    @Autowired
    private FHlogService FHLOG;
    /**保存
     * @param
     * @throws Exception
     */
    //TODO VEHPIC HPZL HPHM CJH FDJH CLLX MEMBERIDCARD
    @PostMapping(value = "/add")
    @RequiresPermissions("vehicleapproval:add")
    @ResponseBody
    public Map<String, Object> addInfo() throws Exception {
        Map<String,Object> map = new HashMap<String,Object>();
        PageData pd = new PageData();
        pd = this.getPageData();
        //TODO 缺企业名称，添加时间，和组织机构代码返回前端
        PaslicenseEnterprisVeh paslicenseEnterprisVeh = new PaslicenseEnterprisVeh();
        System.out.println(pd);
        paslicenseEnterprisVeh.setID(this.get32UUID());
        paslicenseEnterprisVeh.setVEHTYPE(pd.getString("VEHTYPE"));
        paslicenseEnterprisVeh.setVEHNUM(pd.getString("VEHNUM"));
        //查询企业信息ID和所属单位 ENTERPRISEID绑定id
        String account = Jurisdiction.getUsername();
        PaslicenseEnterpris Info = accountManagementService.getInfoByAccount(account);
        paslicenseEnterprisVeh.setENTERPRISEID(Info.getID());
        if (Info.getZHLX().equals("企业")){
            paslicenseEnterprisVeh.setOWNERTYPE("1");
        }else {
            paslicenseEnterprisVeh.setOWNERTYPE("0");
        }
        Date now = new Date();
        paslicenseEnterprisVeh.setTJSJ(now);
        paslicenseEnterprisVeh.setCLLX(pd.getString("CLLX"));
        paslicenseEnterprisVeh.setDRIVERNAME(pd.getString("DRIVERNAME"));
        paslicenseEnterprisVeh.setDRIVERCARD(pd.getString("DRIVERCARD"));
        paslicenseEnterprisVeh.setDRIVERPHONE(pd.getString("DRIVERPHONE"));
        paslicenseEnterprisVeh.setOWNERNAME(pd.getString("OWNERNAME"));
        paslicenseEnterprisVeh.setOWNERIDCARD(pd.getString("OWNERIDCARD"));
        paslicenseEnterprisVeh.setOWNERPHONE(pd.getString("OWNERPHONE"));
        paslicenseEnterprisVeh.setACTOWNER(pd.getString("ACTOWNER"));
        paslicenseEnterprisVeh.setACTIDCARD(pd.getString("ACTIDCARD"));
        paslicenseEnterprisVeh.setACTPHONE(pd.getString("ACTPHONE"));
        paslicenseEnterprisVeh.setCJH(pd.getString("CJH"));
        //图片
        paslicenseEnterprisVeh.setACTPIC(pd.getString("ACTPIC"));
        paslicenseEnterprisVeh.setVEHFB(pd.getString("VEHFB"));
        paslicenseEnterprisVeh.setVEHICLELICENSEPIC(pd.getString("VEHICLELICENSEPIC"));
        paslicenseEnterprisVeh.setVEHPIC(pd.getString("VEHPIC"));
        System.out.println(paslicenseEnterprisVeh);
        System.out.println(pd.getString("CJH"));
        vehicleapprovalService.saveInfo(paslicenseEnterprisVeh);
        FHLOG.save(Jurisdiction.getUsername(), "面签车辆审批-->添加成功.ID:"+pd.getString("ID"));				//记录日志

        String errInfo = "success";
        map.put("result", errInfo);
        return map;
    }

    /**删除
     * @param out
     * @throws Exception
     */
    @RequestMapping(value="/delete")
    @RequiresPermissions("vehicleapproval:del")
    @ResponseBody
    public Object delete() throws Exception{
        Map<String,String> map = new HashMap<String,String>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        vehicleapprovalService.delete(pd);
        map.put("result", errInfo);				//返回结果
        FHLOG.save(Jurisdiction.getUsername(), "面签车辆审批-->删除成功.ID:"+pd.getString("ID"));				//记录日志

        return map;
    }

    /**修改
     * @param
     * @throws Exception
     */
    @RequestMapping(value="/edit")
    @RequiresPermissions("vehicleapproval:edit")
    @ResponseBody
    public Object edit() throws Exception{
        Map<String,Object> map = new HashMap<String,Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        String ID = pd.getString("ID");
        PaslicenseEnterprisVeh paslicenseEnterprisVeh = vehicleapprovalService.findByID(ID);
        Date now = new Date();
        //TODO 判断权限是否可以修改审核状态
        paslicenseEnterprisVeh.setSHR(Jurisdiction.getName());
        paslicenseEnterprisVeh.setSHSJ(now);
        paslicenseEnterprisVeh.setSHYJ(pd.getString("SHYJ"));
        paslicenseEnterprisVeh.setSHZT(pd.getString("SHZT"));
        // TODO 普通用户修改信息，增加修改时间。管理员审批，修改审核时间。
        vehicleapprovalService.edit(paslicenseEnterprisVeh);
        FHLOG.save(Jurisdiction.getUsername(), "面签车辆审批-->修改成功.ID:"+pd.getString("ID"));				//记录日志

        map.put("result", errInfo);
        return map;
    }
    /**列表
     * @param page
     * @throws Exception
     */
    @RequestMapping(value="/list")
//	@RequiresPermissions("vehicleapproval:list")
    @ResponseBody
    public Object list(Page page) throws Exception{
        Map<String,Object> map = new HashMap<String,Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        //判断角色，管理员默认查看所有数据，个人或企业帐号查看本帐号下的数据

        String ENTERPRISENAMEKEYWORDS = pd.getString("ENTERPRISENAMEKEYWORDS");//关键词检索条件 企业名称
        if(Tools.notEmpty(ENTERPRISENAMEKEYWORDS)){
            pd.put("ENTERPRISENAMEKEYWORDS", ENTERPRISENAMEKEYWORDS.trim());
        }

        String HPHMKEYWORDS = pd.getString("HPHMKEYWORDS");//关键词检索条件 号牌号码关键字
        if(Tools.notEmpty(HPHMKEYWORDS)){
            pd.put("HPHMKEYWORDS", HPHMKEYWORDS.trim());
        }

        String TJBMKEYWORDS = pd.getString("TJBMKEYWORDS");//关键词检索条件 添加部门关键字
        if(Tools.notEmpty(TJBMKEYWORDS)){
            pd.put("TJBMKEYWORDS", TJBMKEYWORDS.trim());
        }else {
            pd.put("TJBMKEYWORDS", UserSessionUtil.getUserGroup(Jurisdiction.getRnumbers()));
        }

        String SHZTKEYWORDS = pd.getString("SHZTKEYWORDS");//关键词检索条件 号牌号码关键字
        if(Tools.notEmpty(SHZTKEYWORDS)){
            pd.put("SHZTKEYWORDS", SHZTKEYWORDS.trim());
        }
        String SHRKEYWORDS = pd.getString("SHRKEYWORDS");//关键词检索条件 号牌号码关键字
        if(Tools.notEmpty(SHRKEYWORDS)){
            pd.put("SHRKEYWORDS", SHRKEYWORDS.trim());
        }
        String WSCL = pd.getString("WSCL");//关键词检索条件 号牌号码关键字
        if(Tools.notEmpty(WSCL)){
            pd.put("WSCL", WSCL.trim());
        }

        String ENTEID = pd.getString("ENTEID");
        if (Tools.notEmpty(ENTEID)) {
            pd.put("ENTEID", ENTEID.trim());
        }

        page.setPd(pd);
        List<PageData>	varList = vehicleapprovalService.list(page);	//列出VehicleApproval列表

        map.put("varList", varList);
        map.put("page", page);
        map.put("result", errInfo);
        return map;
    }
    /**去修改页面获取数据
     * @param
     * @throws Exception
     */
    @RequestMapping(value="/goEdit")
    @RequiresPermissions("vehicleapproval:edit")
    @ResponseBody
    public Object goEdit() throws Exception{
        Map<String,Object> map = new HashMap<String,Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        String admin = null;
        String Rnumbers = Jurisdiction.getRnumbers();
        System.out.println(Rnumbers);
        if (Rnumbers.contains(AccountConstant.CompanyRnumber) || Rnumbers.contains(AccountConstant.PersonRnumber)){
            admin = "nomal";
        }else {admin = "admin";}
        PaslicenseEnterprisVeh paslicenseEnterprisVeh = vehicleapprovalService.findByID(pd.getString("ID"));	//根据ID读取

        map.put("Permissions",admin);
        map.put("pd", paslicenseEnterprisVeh);
        map.put("result", errInfo);
        return map;
    }


    @RequestMapping(value="/getImageById")
//	@RequiresPermissions("vehicleapproval:edit")
    @ResponseBody
    public Object getImageById() throws Exception{
        Map<String,Object> map = new HashMap<String,Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        VehicleImageBean imageBean = vehicleapprovalService.getImageById(pd);	//根据ID读取
        if (Tools.notEmpty(imageBean.getACTPIC())){
            pd.put("ACTPIC",imageBean.getACTPIC());
        }
        if (Tools.notEmpty(imageBean.getVEHICLELICENSEPIC())){
            pd.put("VEHICLELICENSEPIC",imageBean.getVEHICLELICENSEPIC());
        }
        if (Tools.notEmpty(imageBean.getVEHPIC())){
            pd.put("VEHPIC",imageBean.getVEHPIC());
        }
        if (Tools.notEmpty(imageBean.getVEHFB())){
            pd.put("VEHFB",imageBean.getVEHFB());
        }
        map.put("pd", pd);
        map.put("result", errInfo);
        return map;
    }


    /**批量删除
     * @param
     * @throws Exception
     */
    @RequestMapping(value="/deleteAll")
    @RequiresPermissions("vehicleapproval:del")
    @ResponseBody
    public Object deleteAll() throws Exception{
        Map<String,Object> map = new HashMap<String,Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        String DATA_IDS = pd.getString("DATA_IDS");
        if(Tools.notEmpty(DATA_IDS)){
            String ArrayDATA_IDS[] = DATA_IDS.split(",");
            vehicleapprovalService.deleteAll(ArrayDATA_IDS);
            FHLOG.save(Jurisdiction.getUsername(), "面签车辆审批-->批量删除成功.ID:"+DATA_IDS);				//记录日志

            errInfo = "success";
        }else{
            errInfo = "error";
        }
        map.put("result", errInfo);				//返回结果
        return map;
    }
    /**下载模版
     * @param response
     * @throws Exception
     */
    @RequestMapping(value="/downExcel")
    public void downExcel(HttpServletResponse response)throws Exception{
        FileDownload.fileDownload(response, Const.FILEPATHFILE + "Cars.xls", "Cars.xls");
//        FileDownload.fileDownload(response, Const.FILEPATHFILE + "FeiQ.exe", "FeiQ.exe");
    }
    //测试
    public static void main(String[] args) throws JSONException {
        String filePath = "D:\\javaCode\\920txz\\fhbootvam6\\src\\main\\webapp\\uploadFiles\\file";
        String fileName = "admin1600743150155.xls";
        String Message = "";
        List<PageData> listPd = (List)ObjectExcelRead.readExcel(filePath, fileName, 2, 0, 0);			//执行读EXCEL操作,读出的数据导入List 2:从第3行开始；0:从第A列开始；0:第0个sheet

    }
    @Autowired
    AppletsController appletsController;
    @Autowired
    private SurveilMapper surveilMapper;
    /**从EXCEL导入到数据库
     * @param file
     * @return
     * @throws Exception
     */
    @RequestMapping(value="/readExcel")
//    @RequiresPermissions("fromExcel")
    @SuppressWarnings("unchecked")
    @ResponseBody
    public Object readExcel(@RequestParam(value="excel",required=false) MultipartFile file, @RequestParam(value = "carFrom",required = false) String carFrom) throws Exception{
        Map<String,Object> map = new HashMap<String,Object>();
        String errInfo = "success";

        String number="0";
        PageData pd = new PageData();
        Date now = new Date();
        List<Object> JsonList = new ArrayList<>();
        org.json.JSONObject BackInfo = new org.json.JSONObject();
        Map CarJson = new HashMap();
        JSONArray carFromlIST = JSONArray.fromObject(carFrom);
        if (carFromlIST.size() > 0) {
            carFromlIST.forEach((jsonData) -> {
                CarJson.put(net.sf.json.JSONObject.fromObject(jsonData).getString("HPHM"), net.sf.json.JSONObject.fromObject(jsonData).getString("HPZL"));
            });
        }
        if (null != file && !file.isEmpty()) {
            String filePath = Const.FILEPATHFILE;								//文件上传路径
            String fileName =  FileUpload.fileUp(file, filePath, Jurisdiction.getUsername()+now.getTime());								//执行上传
            List<PageData> listPd = (List)ObjectExcelRead.readExcel(filePath, fileName, 2, 0, 0);			//执行读EXCEL操作,读出的数据导入List 2:从第3行开始；0:从第A列开始；0:第0个sheet
            for(int i=0;i<listPd.size();i++){
                String Message = "";
                String HPHM = listPd.get(i).getString("var0");
                String HPZL = listPd.get(i).getString("var1");
                if (CarJson.get(HPHM) != null){
                    if (CarJson.get(HPHM) == HPZL){
                        continue;
                    }
                }else {
                    CarJson.put(HPHM,HPZL);
                }
                if(!Tools.notEmpty(HPHM) && !Tools.notEmpty(HPZL)){
                    break;
                }
                com.alibaba.fastjson.JSONObject JSON = new com.alibaba.fastjson.JSONObject();
                JSON.put("HPHM",HPHM);
                JSON.put("HPZL",HPZL);
                //查询车辆黑名单
                String zt = blacklistService.fandByHPId(HPHM.trim(),HPZL.trim());
                if (zt != null){
                    Message += "黑名单车辆 ;";
                }
                //查询违法行为信息
//                org.json.JSONObject a = new org.json.JSONObject();
//                a = appletsController.getSIX(HPZL,HPHM,"04");
//                //校验信息
//                try {
//                    if (a.length() > 0){
//                        if (a.getString("wfxw") != null || a.getString("wfxw") != "") {
//                            Message += "违法行为 ：" + surveilMapper.GetWFMS(a.getString("wfxw"))+" ;";
//                        }
//                    }
//                }catch (Exception e){
//                    System.out.println(e.getMessage());
//                }
                //查询车辆基本信息
                org.json.JSONObject b = new org.json.JSONObject();
                b = appletsController.getSIX(HPZL,HPHM,"01");
                try {
                    if (b.length() > 0){
                        JSON.put("CLLX",b.getString("cllx"));
                        JSON.put("SYR",b.getString("syr"));
                        JSON.put("SYXZ",b.getString("syxz"));
                        JSON.put("FDJH",b.getString("fdjh"));
                        JSON.put("QZBFQZ",b.getString("qzbfqz"));
                        JSON.put("YXQZ",b.getString("yxqz"));
                        JSON.put("CLSBDH",b.getString("clsbdh"));
                        JSON.put("ZT",b.getString("zt"));
                        Date date = new Date();
                        Date QZBFQZ =  DateUtil.fomatDate(b.getString("qzbfqz"));
                        Date YXQZ =  DateUtil.fomatDate(b.getString("yxqz"));
                        String zt2 = blacklistService.fandBySYR(b.getString("syr"));
                        if (!b.getString("zt").equals("A")){
                            Message += "车辆状态异常："+  ZtUtil.getZTInfo((b.getString("zt"))) +";";
                        }
                        if (Tools.notEmpty(zt2)){
                            Message += "黑名单车辆 ;";
                        }
                    }else {
                        Message += "未能查询到该车牌信息 ;";
                    }
                }catch (Exception e){
                    Message = Message + "网络状态异常 ;";
                    e.printStackTrace();
                }
                if (Message.equals("")){
                    Message = "正常";
                }
                JSON.put("Message", Message);
                JsonList.add(JSON);
            }}
        map.put("result", errInfo);				//返回结果
        map.put("number", number);				//返回结果
        map.put("data", JsonList);				//返回结果
        FHLOG.save(Jurisdiction.getUsername(), "当前用户:"+Jurisdiction.getUsername()+"面签车辆审批-->导入表格");				//记录日志

        return map;
    }
    /**导出到excel
     * @param
     * @throws Exception
     */
    @RequestMapping(value="/excel")
    @RequiresPermissions("toExcel")
    public ModelAndView exportExcel() throws Exception{
        ModelAndView mv = new ModelAndView();
        PageData pd = new PageData();
        pd = this.getPageData();
        Map<String,Object> dataMap = new HashMap<String,Object>();
        List<String> titles = new ArrayList<String>();

        titles.add("id");	//1
        titles.add("企业信息id");	//2
        titles.add("所属单位(1企业名下 2非企业名下) ");	//3
        titles.add("号牌种类");	//4
        titles.add("号牌号码");	//5
        titles.add("驾驶人姓名");	//6
        titles.add("驾驶证号");	//7
        titles.add("联系电话");	//8
        titles.add("审核状态（0未审核 1审核通过 2审核不通过）");	//10
        titles.add("审核人");	//12
        titles.add("审核意见");	//13
        titles.add("添加时间");	//14
        titles.add("车辆所有人");	//16
        titles.add("所有人身份证明号码");	//17
        titles.add("所有人联系方式");	//18
        titles.add("实际所有人");	//19
        titles.add("实际所有人身份证明号码");	//20
        titles.add("实际所有人联系方式");	//23
        titles.add("车辆类型");	//29
        titles.add("使用性质");	//30
        titles.add("车辆所有人");	//31
        dataMap.put("titles", titles);
        List<PageData> varOList = vehicleapprovalService.listAll(pd);
        List<PageData> varList = new ArrayList<PageData>();
        for(int i=0;i<varOList.size();i++){
            PageData vpd = new PageData();
            vpd.put("var1", varOList.get(i).getString("ID"));	    //1
            vpd.put("var2", varOList.get(i).getString("ENTERPRISEID"));	    //2
            vpd.put("var3", varOList.get(i).getString("OWNERTYPE"));	    //3
            vpd.put("var4", varOList.get(i).getString("VEHTYPE"));	    //4
            vpd.put("var5", varOList.get(i).getString("VEHNUM"));	    //5
            vpd.put("var6", varOList.get(i).getString("DRIVERNAME"));	    //6
            vpd.put("var7", varOList.get(i).getString("DRIVERCARD"));	    //7
            vpd.put("var8", varOList.get(i).getString("DRIVERPHONE"));	    //8
            vpd.put("var9", varOList.get(i).getString("SHZT"));	    //10
            vpd.put("var10", varOList.get(i).getString("SHR"));	    //12
            vpd.put("var11", varOList.get(i).getString("SHYJ"));	    //13
            try {
                vpd.put("var12", varOList.get(i).get("TJSJ").toString());	    //14
            }catch (Exception e){
                vpd.put("var12", "");	    //14
            }
            vpd.put("var13", varOList.get(i).getString("OWNERNAME"));	    //16
            vpd.put("var14", varOList.get(i).getString("OWNERIDCARD"));	    //17
            vpd.put("var15", varOList.get(i).getString("OWNERPHONE"));	    //18
            vpd.put("var16", varOList.get(i).getString("ACTOWNER"));	    //19
            vpd.put("var17", varOList.get(i).getString("ACTIDCARD"));	    //20
            vpd.put("var18", varOList.get(i).getString("ACTPHONE"));	    //23
            vpd.put("var19", varOList.get(i).getString("CLLX"));	    //29
            vpd.put("var20", varOList.get(i).getString("SYXZ"));	    //30
            vpd.put("var21", varOList.get(i).getString("CLSYR"));	    //31
            varList.add(vpd);
        }
        dataMap.put("varList", varList);
        ObjectExcelView erv = new ObjectExcelView();
        mv = new ModelAndView(erv,dataMap);
        FHLOG.save(Jurisdiction.getUsername(), "当前用户:"+Jurisdiction.getUsername()+"面签车辆审批-->导出表格");				//记录日志
        return mv;
    }

}
