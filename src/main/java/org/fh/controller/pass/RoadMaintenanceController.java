package org.fh.controller.pass;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.fh.controller.base.BaseController;
import org.fh.entity.Page;
import org.fh.entity.PageData;
import org.fh.service.pass.RoadMaintenanceService;
import org.fh.service.system.FHlogService;
import org.fh.util.Jurisdiction;
import org.fh.util.ObjectExcelView;
import org.fh.util.Tools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 说明：区域通行证--道路信息维护
 * 作者：FH Admin QQ313596790
 * 时间：2020-06-02
 * 官网：www.fhadmin.org
 */
@Controller
@RequestMapping("/roadmaintenance")
public class RoadMaintenanceController extends BaseController {

    @Autowired
    private RoadMaintenanceService roadmaintenanceService;
    @Autowired
    private  RoadInfoController roadInfoController;
    @Autowired
    private FHlogService FHLOG;
    /**
     * 保存
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/add")
    @RequiresPermissions("roadmaintenance:add")
    @ResponseBody
    public Object add() throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        String ID=this.get32UUID();
        pd.put("ID", ID);    //主键
        pd.put("DJRQ",LocalDateTime.now());
        roadmaintenanceService.save(pd);
        FHLOG.save(Jurisdiction.getUsername(), "道路信息维护-->添加成功.ID:"+ID);				//记录日志

        map.put("result", errInfo);
        return map;
    }

    /**
     * 删除
     *
     * @param out
     * @throws Exception
     */
    @RequestMapping(value = "/delete")
    @RequiresPermissions("roadmaintenance:del")
    @ResponseBody
    public Object delete() throws Exception {
        Map<String, String> map = new HashMap<String, String>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        roadmaintenanceService.delete(pd);
        FHLOG.save(Jurisdiction.getUsername(), "道路信息维护-->删除成功.ID:"+pd.getString("ID"));				//记录日志
        map.put("result", errInfo);                //返回结果
        return map;
    }

    /**
     * 修改
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/edit")
    @RequiresPermissions("roadmaintenance:edit")
    @ResponseBody
    public Object edit() throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        pd.put("DJRQ", LocalDateTime.now());
        roadmaintenanceService.edit(pd);
        FHLOG.save(Jurisdiction.getUsername(), "道路信息维护-->修改成功.ID:"+pd.getString("ID"));				//记录日志

        map.put("result", errInfo);
        return map;
    }

    /**
     * 列表
     *
     * @param page
     * @throws Exception
     */
    @RequestMapping(value = "/list")
    @RequiresPermissions("roadmaintenance:list")
    @ResponseBody
    public Object list(Page page) throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();

        String LDMCKEYWORDS = pd.getString("LDMCKEYWORDS");  //检索条件 关键字 路段名称
        if (Tools.notEmpty(LDMCKEYWORDS)) {
            pd.put("LDMCKEYWORDS", LDMCKEYWORDS.trim());
        }
        String LDZTKEYWORDS = pd.getString("LDZTKEYWORDS"); //关键字 路段状态即 路维护状态
        if (Tools.notEmpty(LDZTKEYWORDS)) {
            pd.put("LDZTKEYWORDS", LDZTKEYWORDS.trim());
        }
        String STARTDJRQKEYWORDS = pd.getString("STARTDJRQKEYWORDS");//关键词检索条件--起始登记日期
        if (Tools.notEmpty(STARTDJRQKEYWORDS)) {
            pd.put("STARTDJRQKEYWORDS", STARTDJRQKEYWORDS.trim());
        }
        String ENDDJRQKEYWORDS = pd.getString("ENDDJRQKEYWORDS");//关键词检索条件--结束登记日期
        if (Tools.notEmpty(ENDDJRQKEYWORDS)) {
            pd.put("ENDDJRQKEYWORDS", ENDDJRQKEYWORDS.trim());
        }
        page.setPd(pd);
        List<PageData> varList = roadmaintenanceService.list(page);    //列出RoadMaintenance列表
        map.put("varList", varList);
        map.put("page", page);
        map.put("result", errInfo);
        return map;
    }





    /**
     * 列表
     *
     * @param page
     * @throws Exception
     */
    @RequestMapping(value = "/getRoadInfolist")
    @RequiresPermissions("roadmaintenance:list")
    @ResponseBody
    public Object getRoadInfoList(Page page) throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        List<PageData> roadMaintenanceList = roadmaintenanceService.listAll(pd);    //列出RoadMaintenance列表
        List<String> roadInfoList = new ArrayList<>();
        if (null != roadMaintenanceList && roadMaintenanceList.size() > 0){
            for (PageData pa : roadMaintenanceList) {
                roadInfoList.add(pa.getString("ROADID").trim());
            }
            pd.put("unboundIds",roadInfoList);
        }else {
            pd.put("unboundIds",null);
        }
        page.setPd(pd);
        Object o = roadInfoController.datalistPageByRoadIDList(page);
        return o;
    }


    /**
     * 去修改页面获取数据
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/goEdit")
    @RequiresPermissions("roadmaintenance:edit")
    @ResponseBody
    public Object goEdit() throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        pd = roadmaintenanceService.findById(pd);    //根据ID读取
        map.put("pd", pd);
        map.put("result", errInfo);
        return map;
    }

    /**
     * 批量删除
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/deleteAll")
    @RequiresPermissions("roadmaintenance:del")
    @ResponseBody
    public Object deleteAll() throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        String DATA_IDS = pd.getString("DATA_IDS");
        if (Tools.notEmpty(DATA_IDS)) {
            String ArrayDATA_IDS[] = DATA_IDS.split(",");
            roadmaintenanceService.deleteAll(ArrayDATA_IDS);
            FHLOG.save(Jurisdiction.getUsername(), "道路信息维护-->批量删除成功.ID:"+DATA_IDS);				//记录日志

            errInfo = "success";
        } else {
            errInfo = "error";
        }
        map.put("result", errInfo);                //返回结果
        return map;
    }

    /**
     * 导出到excel
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/excel")
    @RequiresPermissions("toExcel")
    public ModelAndView exportExcel() throws Exception {
        ModelAndView mv = new ModelAndView();
        PageData pd = new PageData();
        pd = this.getPageData();
        Map<String, Object> dataMap = new HashMap<String, Object>();
        List<String> titles = new ArrayList<String>();
        titles.add("ID");    //1
        titles.add("序号");    //2
        titles.add("道路ID");    //3
        titles.add("路段名称");    //4
        titles.add("是否维护完成");    //5
        titles.add("登记日期");    //6
        dataMap.put("titles", titles);
        List<PageData> varOList = roadmaintenanceService.listAll(pd);
        List<PageData> varList = new ArrayList<PageData>();
        for (int i = 0; i < varOList.size(); i++) {
            PageData vpd = new PageData();
            vpd.put("var1", varOList.get(i).getString("ID"));        //1
            vpd.put("var2", varOList.get(i).getString("XH"));        //2
            vpd.put("var3", varOList.get(i).getString("ROADID"));        //3
            vpd.put("var4", varOList.get(i).getString("LDMC"));        //4
            vpd.put("var5", varOList.get(i).getString("IS_WHWC"));        //5
            vpd.put("var6", varOList.get(i).getString("DJRQ"));        //6
            varList.add(vpd);
        }
        dataMap.put("varList", varList);
        ObjectExcelView erv = new ObjectExcelView();
        mv = new ModelAndView(erv, dataMap);
        FHLOG.save(Jurisdiction.getUsername(), "当前用户:"+Jurisdiction.getUsername()+"-->道路信息维护-->导出表格");				//记录日志

        return mv;
    }

}
