package org.fh.controller.pass;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.fh.controller.base.BaseController;
import org.fh.entity.Page;
import org.fh.entity.PageData;
import org.fh.service.pass.BlacklistService;
import org.fh.service.system.FHlogService;
import org.fh.util.DateUtil;
import org.fh.util.Jurisdiction;
import org.fh.util.ObjectExcelView;
import org.fh.util.Tools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 说明：黑名单管理
 * 作者：FH Admin QQ313596790
 * 时间：2020-06-12
 * 官网：www.fhadmin.org
 */
@Controller
@RequestMapping("/blacklist")
public class BlacklistController extends BaseController {

	@Autowired
	private BlacklistService blacklistService;

	@Autowired
	private AppletsController appletsController;
	@Autowired
	private FHlogService FHLOG;

	/**保存
	 * @param
	 * @throws Exception
	 */
	@RequestMapping(value="/add")
	@RequiresPermissions("blacklist:add")
	@ResponseBody
	public Object add() throws Exception{
		Map<String,Object> map = new HashMap<String,Object>();
		String errInfo = "success";
		PageData pd = new PageData();
		pd = this.getPageData();
	//	System.out.println(pd.getString("HPHM")+"++++++++++++++++++++++++++++++++++++");
		LocalDate localDate = LocalDate.now();
		LocalTime localTime = LocalTime.now();
		DateTimeFormatter time = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		DateFormat bf = new SimpleDateFormat("yyyyMMddHHmmssss");
		Date date = new Date();
		String format = bf.format(date);
		pd.put("XH",format);
		pd.put("GXRQ", DateUtil.str2Date(localDate.atTime(localTime).format(time)));

		//需要获取localDateTime格式时间，但yyyy-MM-dd格式日期字符串无法直接转换
		if (Tools.notEmpty(pd.getString("ENDTIME").trim())){
			//字符转localdate
			LocalDate ENDTIME = LocalDate.parse(pd.getString("ENDTIME"), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
			//localdate转date
			Date EndDate = Date.from(ENDTIME.atStartOfDay(ZoneOffset.ofHours(8)).toInstant());
			//date转localdatetime
			LocalDateTime STARTDJRQ = EndDate.toInstant().atZone(ZoneOffset.ofHours(8)).toLocalDateTime();
			pd.put("HMDSX",STARTDJRQ);
		}else {
			pd.put("HMDSX",null);
		}
		String zt = null;
		//添加所有人黑名单先判断所有人是否存在
		if (Tools.notEmpty(pd.getString("SYR"))){
			zt = blacklistService.fandBySYR(pd.getString("SYR").trim());
		}else {
			//添加黑名单车辆先判断该号牌是否存在
			zt = blacklistService.fandByHPId(pd.getString("HPHM").trim(),pd.getString("HPZL").trim());
		}
		System.out.println(zt);
		if (zt != null){
			errInfo = "该车牌已存在黑名单";
		}else {
			if (Tools.notEmpty(pd.getString("SYR"))){
				blacklistService.savesyr(pd);
				FHLOG.save(Jurisdiction.getUsername(), "黑名单管理--->添加所有人黑名单成功.xh:"+pd.getString("SYR"));				//记录日志

			}else {
				//TODO 查询六合一判断车辆基本信息
				//TODO 查询车辆信息
			org.json.JSONObject b = new org.json.JSONObject();
			b = appletsController.getSIX(pd.getString("HPZL").trim(),pd.getString("HPHM").trim(),"01");
			//校验信息
			try {
				if (b.length() > 0){
					System.out.println(b);
					System.out.println(pd);
					if (b.getString("cllx") != null || b.getString("cllx") != "") {
						if(!b.getString("cllx").trim().equals(pd.getString("CLLX").trim())){
							map.put("result", "该车辆信息不符");
							System.out.println(b.getString("cllx").trim());
							System.out.println(pd.getString("CLLX").trim());
							return map;
						}
					}
				}
			}catch (Exception e){
				System.out.println(e.getMessage());
			}

				blacklistService.save(pd);
				FHLOG.save(Jurisdiction.getUsername(), "黑名单管理--->添加所有人黑名单成功.xh:"+pd.getString("SYR"));				//记录日志

			}
		}
		System.out.println(pd);
		map.put("result", errInfo);
		return map;
	}

	/**删除
	 * @param
	 * @throws Exception
	 */
	@RequestMapping(value="/delete")
	@RequiresPermissions("blacklist:del")
	@ResponseBody
	public Object delete() throws Exception{
		Map<String,String> map = new HashMap<String,String>();
		String errInfo = "success";
		PageData pd = new PageData();
		pd = this.getPageData();
		blacklistService.delete(pd);
		map.put("result", errInfo);				//返回结果
		FHLOG.save(Jurisdiction.getUsername(), "黑名单管理--->删除成功.xh:"+pd.getString("XH"));				//记录日志
		return map;
	}

	/**修改
	 * @param
	 * @throws Exception
	 */
	@RequestMapping(value="/edit")
	@RequiresPermissions("blacklist:edit")
	@ResponseBody
	public Object edit() throws Exception{
		Map<String,Object> map = new HashMap<String,Object>();
		String errInfo = "success";
		PageData pd = new PageData();
		pd = this.getPageData();
		LocalDate localDate = LocalDate.now();
		LocalTime localTime = LocalTime.now();

		DateTimeFormatter time = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		pd.put("GXRQ", DateUtil.str2Date(localDate.atTime(localTime).format(time)));
		//需要获取localDateTime格式时间，但yyyy-MM-dd格式日期字符串无法直接转换
		if (Tools.notEmpty(pd.getString("ENDTIME").trim())){
			//字符转localdate
			LocalDate ENDTIME = LocalDate.parse(pd.getString("ENDTIME"), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
			//localdate转date
			Date EndDate = Date.from(ENDTIME.atStartOfDay(ZoneOffset.ofHours(8)).toInstant());
			//date转localdatetime
			LocalDateTime STARTDJRQ = EndDate.toInstant().atZone(ZoneOffset.ofHours(8)).toLocalDateTime();
			pd.put("HMDSX",STARTDJRQ);
		}else {
			pd.put("HMDSX",null);
		}
		String XH = null;
		//添加所有人黑名单先判断所有人是否存在
		if (Tools.notEmpty(pd.getString("SYR"))){
			XH = blacklistService.fandBySYR(pd.getString("SYR").trim());
		}else {
			//添加黑名单车辆先判断该号牌是否存在
			//TODO 查询六合一判断车辆基本信息
			//TODO 查询车辆信息
			org.json.JSONObject b = new org.json.JSONObject();
			b = appletsController.getSIX(pd.getString("HPZL").trim(),pd.getString("HPHM").trim(),"01");
			//校验信息
			try {
				if (b.length() > 0){
					if (b.getString("cllx") != null || b.getString("cllx") != "") {
						if(!b.getString("cllx").trim().equals(pd.getString("CLLX").trim())){
							System.out.println(b.getString("cllx").trim());
							System.out.println(pd.getString("CLLX").trim());
							map.put("result", "该车辆信息不符");
							return map;
						}
					}
				}
			}catch (Exception e){
				System.out.println(e.getMessage());
			}
			XH = blacklistService.fandByHPId(pd.getString("HPHM").trim(),pd.getString("HPZL").trim());
		}
		if (XH != null){
			if (XH.equals(pd.getString("XH"))){
				blacklistService.edit(pd);
				FHLOG.save(Jurisdiction.getUsername(), "黑名单管理--->修改成功.xh:"+pd.getString("XH"));				//记录日志

			}else {
				errInfo = "修改的黑名单已存在";
			}
		}else {
			blacklistService.edit(pd);
			FHLOG.save(Jurisdiction.getUsername(), "黑名单管理--->修改成功.xh:"+pd.getString("XH"));				//记录日志

		}
		map.put("result", errInfo);
		return map;
	}

	/**列表
	 * @param page
	 * @throws Exception
	 */
	@RequestMapping(value="/list")
	@RequiresPermissions("blacklist:list")
	@ResponseBody
	public Object list(Page page) throws Exception{
		Map<String,Object> map = new HashMap<String,Object>();
		String errInfo = "success";
		PageData pd = new PageData();
		pd = this.getPageData();
		String HPZL = pd.getString("HPZL");//关键词检索条件--号牌种类
		if(Tools.notEmpty(HPZL)){
			pd.put("HPZL", HPZL.trim());
		}
		String HPHM = pd.getString("HPHM");//关键词检索条件--号牌号码
		if(Tools.notEmpty(HPHM)) {
			pd.put("HPHM", HPHM.trim());
		}
		String BZ = pd.getString("BZKEYWORDS");//关键词检索条件--经办人
		if(Tools.notEmpty(BZ)){
			pd.put("BZ", BZ.trim());
		}
		String SYR = pd.getString("SRYKEYWORDS");//关键词检索条件--机动车所有人
		if(Tools.notEmpty(SYR)){
			pd.put("SYR", SYR.trim());
		}
		String STARTTIME = pd.getString("STARTTIME");//关键词检索条件--起始登记日期
		if(Tools.notEmpty(STARTTIME)){

			//需要获取localDateTime格式时间，但yyyy-MM-dd格式日期字符串无法直接转换
			//字符转localdate
			LocalDate localDate = LocalDate.parse(STARTTIME, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
			//localdate转date
			Date date = Date.from(localDate.atStartOfDay(ZoneOffset.ofHours(8)).toInstant());
			//date转localdatetime
			LocalDateTime STARTDJRQ = date.toInstant().atZone(ZoneOffset.ofHours(8)).toLocalDateTime();
			pd.put("STARTTIME",STARTDJRQ);
		}else {
			pd.put("STARTTIME",null);
		}

		String ENDTIME = pd.getString("ENDTIME");//关键词检索条件--结束登记日期
		if(Tools.notEmpty(ENDTIME)){
			//需要获取localDateTime格式时间，但yyyy-MM-dd格式日期字符串无法直接转换
			//字符转localdate
			LocalDate localDate = LocalDate.parse(ENDTIME, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
			//localdate转date
			Date date = Date.from(localDate.atStartOfDay(ZoneOffset.ofHours(8)).toInstant());
			//date转localdatetime
			LocalDateTime ENDDJRQ = date.toInstant().atZone(ZoneOffset.ofHours(8)).toLocalDateTime();
			pd.put("ENDTIME", ENDDJRQ);
		}else {
			pd.put("ENDTIME", null);
		}
		page.setPd(pd);
		List<PageData>	varList = blacklistService.list(page);	//列出Blacklist列表
		map.put("varList", varList);
		map.put("page", page);
		map.put("result", errInfo);
		return map;
	}

	 /**去修改页面获取数据
	 * @param
	 * @throws Exception
	 */
	@RequestMapping(value="/goEdit")
	@RequiresPermissions("blacklist:edit")
	@ResponseBody
	public Object goEdit() throws Exception{
		Map<String,Object> map = new HashMap<String,Object>();
		String errInfo = "success";
		PageData pd = new PageData();
		pd = this.getPageData();
		pd = blacklistService.findById(pd);	//根据ID读取
		System.out.println(pd.getString("HPHM"));

		map.put("pd", pd);
		map.put("result", errInfo);
		return map;
	}

	 /**批量删除
	 * @param
	 * @throws Exception
	 */
	@RequestMapping(value="/deleteAll")
	@RequiresPermissions("blacklist:del")
	@ResponseBody
	public Object deleteAll() throws Exception{
		Map<String,Object> map = new HashMap<String,Object>();
		String errInfo = "success";
		PageData pd = new PageData();
		pd = this.getPageData();
		String DATA_IDS = pd.getString("DATA_IDS");
		if(Tools.notEmpty(DATA_IDS)){
			String ArrayDATA_IDS[] = DATA_IDS.split(",");
			blacklistService.deleteAll(ArrayDATA_IDS);
			FHLOG.save(Jurisdiction.getUsername(), "黑名单管理--->批量删除成功.xh:"+pd.getString("DATA_IDS"));				//记录日志

			errInfo = "success";
		}else{
			errInfo = "error";
		}
		map.put("result", errInfo);				//返回结果
		return map;
	}

	 /**导出到excel
	 * @param
	 * @throws Exception
	 */
	@RequestMapping(value="/excel")
	@RequiresPermissions("toExcel")
	public ModelAndView exportExcel() throws Exception{
		ModelAndView mv = new ModelAndView();
		PageData pd = new PageData();
		pd = this.getPageData();
		Map<String,Object> dataMap = new HashMap<String,Object>();
		List<String> titles = new ArrayList<String>();
		titles.add("序号");	//1
		titles.add("号牌种类");	//2
		titles.add("号牌号码");	//3
		titles.add("车辆类型");	//4
		titles.add("所有人");	//5
		titles.add("状态");	//6
		titles.add("备注");	//7
		titles.add("更新日期");	//8
		dataMap.put("titles", titles);
		List<PageData> varOList = blacklistService.listAll(pd);
		List<PageData> varList = new ArrayList<PageData>();
		for(int i=0;i<varOList.size();i++){
			PageData vpd = new PageData();
			vpd.put("var1", varOList.get(i).getString("XH"));	    //1
			vpd.put("var2", varOList.get(i).getString("HPZL"));	    //2
			vpd.put("var3", varOList.get(i).getString("HPHM"));	    //3
			vpd.put("var4", varOList.get(i).getString("CLLX"));	    //4
			vpd.put("var5", varOList.get(i).getString("SYR"));	    //5
			vpd.put("var6", varOList.get(i).getString("ZT"));	    //6
			vpd.put("var7", varOList.get(i).getString("BZ"));	    //7
			vpd.put("var8", varOList.get(i).getString("GXRQ"));	    //8
			varList.add(vpd);
		}
		dataMap.put("varList", varList);
		ObjectExcelView erv = new ObjectExcelView();
		mv = new ModelAndView(erv,dataMap);
		FHLOG.save(Jurisdiction.getUsername(), "当前用户:"+Jurisdiction.getUsername()+"黑名单管理-->导出表格");				//记录日志


		return mv;
	}

}
