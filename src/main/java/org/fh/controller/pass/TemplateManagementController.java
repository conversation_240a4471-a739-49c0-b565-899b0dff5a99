package org.fh.controller.pass;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.fh.controller.base.BaseController;
import org.fh.entity.Page;
import org.fh.entity.PageData;
import org.fh.entity.pass.RoadPart;
import org.fh.service.pass.TemplateManagementService;
import org.fh.service.system.FHlogService;
import org.fh.util.DateUtil;
import org.fh.util.Jurisdiction;
import org.fh.util.ObjectExcelView;
import org.fh.util.Tools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;


/**
 * 说明：区域通行证--模板管理
 * 作者：FH Admin QQ313596790
 * 时间：2020-05-27
 * 官网：www.fhadmin.org
 */
@Controller
@RequestMapping("/templatemanagement")
public class TemplateManagementController extends BaseController {

	@Autowired
	private TemplateManagementService templatemanagementService;
	@Autowired
	private FHlogService FHLOG;

	/**模版道路列表
	 * @param
	 * @throws Exception
	 */
	@RequestMapping(value="/listRoad")
	@ResponseBody
	public Object listRoad(Page page) throws Exception{
		Map<String,Object> map = new HashMap<String,Object>();
		String errInfo = "success";
		PageData pd = new PageData();
		pd = this.getPageData();

		String KEYWORDS = pd.getString("KEYWORDS");
		if(Tools.notEmpty(KEYWORDS)){
			pd.put("KEYWORDS", KEYWORDS.trim().toLowerCase());
		}
		String DJ = pd.getString("DJ");//关键词检索条件--模板名称
		if(Tools.notEmpty(DJ)){
			pd.put("DJ", DJ.trim());
		}

		page.setPd(pd);

		List<PageData>	varList = templatemanagementService.listRoad(page);
		map.put("varList", varList);
		map.put("page", page);
		map.put("result", errInfo);
		return map;
	}


	/**列表
	 * @param
	 * @throws Exception
	 */
	@RequestMapping(value="/listAll")
	@ResponseBody
	public Object listAll() throws Exception{
		Map<String,Object> map = new HashMap<String,Object>();
		String errInfo = "success";
		PageData pd = new PageData();
		pd = this.getPageData();
		List<PageData>	varList = templatemanagementService.listAll(pd);
		map.put("varList", varList);
		map.put("result", errInfo);
		return map;
	}

	/**模板道路查詢
	 * @param
	 * @throws Exception
	 */
	@RequestMapping(value="/listById")
	@ResponseBody
	public Object listById() throws Exception{
		Map<String,Object> map = new HashMap<String,Object>();
		String errInfo = "success";
		PageData pd = new PageData();
		pd = this.getPageData();
		// [{ROADPARTID: "5002,5001,9999,100,1074,1071,5000,408,9999",…}]
		List<RoadPart>	varList = templatemanagementService.listById(pd);
		map.put("varList", varList);
		map.put("result", errInfo);
		return map;
	}

	/**新增
	 * @param
	 * @throws Exception
	 */
	@RequestMapping(value="/add")
//	@RequiresPermissions("templatemanagement:add")
	@ResponseBody
	public Object add() throws Exception{
		Map<String,Object> map = new HashMap<String,Object>();
		String errInfo = "success";
		PageData pd = new PageData();
		pd = this.getPageData();
		String ID=this.get32UUID();
		LocalDate localDate = LocalDate.now();
		LocalTime localTime = LocalTime.now();
		DateTimeFormatter time = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		pd.put("ID",ID);//主键
		pd.put("TJSJ",DateUtil.str2Date(localDate.atTime(localTime).format(time)));//添加日期
		templatemanagementService.save(pd);
		FHLOG.save(Jurisdiction.getUsername(), "模板管理-->新增成功.ID:"+ID);				//记录日志

		map.put("result", errInfo);
		return map;
	}

	/**删除
	 * @param
	 * @throws Exception
	 */
	@RequestMapping(value="/delete")
	@RequiresPermissions("templatemanagement:del")
	@ResponseBody
	public Object delete() throws Exception{
		Map<String,String> map = new HashMap<String,String>();
		String errInfo = "success";
		PageData pd = new PageData();
		pd = this.getPageData();
		templatemanagementService.delete(pd);
		FHLOG.save(Jurisdiction.getUsername(), "模板管理-->删除成功.ID:"+pd.getString("ID"));				//记录日志

		map.put("result", errInfo);				//返回结果
		return map;
	}

	/**修改
	 * @param
	 * @throws Exception
	 */
	@RequestMapping(value="/edit")
	@RequiresPermissions("templatemanagement:edit")
	@ResponseBody
	public Object edit() throws Exception{
		Map<String,Object> map = new HashMap<String,Object>();
		String errInfo = "success";
		PageData pd = new PageData();
		pd = this.getPageData();
		templatemanagementService.edit(pd);
		FHLOG.save(Jurisdiction.getUsername(), "模板管理-->修改成功.ID:"+pd.getString("ID"));				//记录日志

		map.put("result", errInfo);
		return map;
	}

	/**列表
	 * @param page
	 * @throws Exception
	 */
	@RequestMapping(value="/list")
//	@RequiresPermissions("templatemanagement:list")
	@ResponseBody
	public Object list(Page page) throws Exception{
		Map<String,Object> map = new HashMap<String,Object>();
		String errInfo = "success";
		PageData pd = new PageData();
		pd = this.getPageData();
		String MBMCKEYWORDS = pd.getString("MBMCKEYWORDS");//关键词检索条件--模板名称
		if(Tools.notEmpty(MBMCKEYWORDS)){
			pd.put("MBMCKEYWORDS", MBMCKEYWORDS.trim());
		}
		String MBLXKEYWORDS = pd.getString("MBLXKEYWORDS");//关键词检索条件--模板类型
		if(Tools.notEmpty(MBLXKEYWORDS)){
			pd.put("MBLXKEYWORDS", MBLXKEYWORDS.trim());
		}
		String WEBKEYWORDS = pd.getString("WEBKEYWORDS");//关键词检索条件--是否是外网
		if(Tools.notEmpty(WEBKEYWORDS)){
			pd.put("WEBKEYWORDS", WEBKEYWORDS.trim());
		}
		String STARTTJSJKEYWORDS = pd.getString("STARTTJSJKEYWORDS");//关键词检索条件--起始添加时间
		if(Tools.notEmpty(STARTTJSJKEYWORDS)){
			STARTTJSJKEYWORDS = STARTTJSJKEYWORDS.trim();
			//需要获取localDateTime格式时间，但无法直接转换
			//字符转localdate
			LocalDate localDate = LocalDate.parse(STARTTJSJKEYWORDS, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
			//localdate转date
			Date date = Date.from(localDate.atStartOfDay(ZoneOffset.ofHours(8)).toInstant());
			//date转localdatetime
			LocalDateTime STARTTJSJ = date.toInstant().atZone(ZoneOffset.ofHours(8)).toLocalDateTime();
			pd.put("STARTTJSJKEYWORDS",STARTTJSJ);
		}else{
			pd.put("STARTTJSJKEYWORDS",null);
		}
		String ENDTJSJKEYWORDS = pd.getString("ENDTJSJKEYWORDS");//关键词检索条件--终止添加时间
		if(Tools.notEmpty(ENDTJSJKEYWORDS)){
			ENDTJSJKEYWORDS=ENDTJSJKEYWORDS.trim();
			//需要获取localDateTime格式时间，但yyyy-MM-dd格式日期字符串无法直接转换
			//字符转localdate
			LocalDate localDate = LocalDate.parse(ENDTJSJKEYWORDS, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
			//localdate转date
			Date date = Date.from(localDate.atStartOfDay(ZoneOffset.ofHours(8)).toInstant());
			//date转localdatetime
			LocalDateTime ENDTJSJ = date.toInstant().atZone(ZoneOffset.ofHours(8)).toLocalDateTime();

			pd.put("ENDTJSJKEYWORDS",ENDTJSJ);
		}else {
			pd.put("ENDTJSJKEYWORDS",null);
		}
		page.setPd(pd);
		List<PageData>	varList = templatemanagementService.list(page);	//列出TemplateManagement列表
		map.put("varList", varList);
		map.put("page", page);
		map.put("result", errInfo);
		return map;
	}

	 /**去修改页面获取数据
	 * @param
	 * @throws Exception
	 */
	@RequestMapping(value="/goEdit")
	@RequiresPermissions("templatemanagement:edit")
	@ResponseBody
	public Object goEdit() throws Exception{
		Map<String,Object> map = new HashMap<String,Object>();
		String errInfo = "success";
		PageData pd = new PageData();
		pd = this.getPageData();
		pd = templatemanagementService.findById(pd);	//根据ID读取
		map.put("pd", pd);
		map.put("result", errInfo);
		return map;
	}

	 /**批量删除
	 * @param
	 * @throws Exception
	 */
	@RequestMapping(value="/deleteAll")
	@RequiresPermissions("templatemanagement:del")
	@ResponseBody
	public Object deleteAll() throws Exception{
		Map<String,Object> map = new HashMap<String,Object>();
		String errInfo = "success";
		PageData pd = new PageData();
		pd = this.getPageData();
		String DATA_IDS = pd.getString("DATA_IDS");
		if(Tools.notEmpty(DATA_IDS)){
			String ArrayDATA_IDS[] = DATA_IDS.split(",");
			templatemanagementService.deleteAll(ArrayDATA_IDS);
			FHLOG.save(Jurisdiction.getUsername(), "模板管理-->批量删除成功.ID:"+pd.getString("DATA_IDS"));				//记录日志

			errInfo = "success";
		}else{
			errInfo = "error";
		}
		map.put("result", errInfo);				//返回结果
		return map;
	}

	 /**导出到excel
	 * @param
	 * @throws Exception
	 */
	@RequestMapping(value="/excel")
	@RequiresPermissions("toExcel")
	public ModelAndView exportExcel() throws Exception{
		ModelAndView mv = new ModelAndView();
		PageData pd = new PageData();
		pd = this.getPageData();
		Map<String,Object> dataMap = new HashMap<String,Object>();
		List<String> titles = new ArrayList<String>();
		titles.add("id");	//1
		titles.add("模板名称");	//2
		titles.add("排序");	//3
		titles.add("添加时间");	//4
		titles.add("1通行 0禁行");	//5
		titles.add("是否是外网 1是 0否");	//6
		dataMap.put("titles", titles);
		List<PageData> varOList = templatemanagementService.listAll(pd);
		List<PageData> varList = new ArrayList<PageData>();
		for(int i=0;i<varOList.size();i++){
			PageData vpd = new PageData();
			vpd.put("var1", varOList.get(i).getString("ID"));	    //1
			vpd.put("var2", varOList.get(i).getString("MBMC"));	    //2
			vpd.put("var3", varOList.get(i).getString("PX"));	    //3
			vpd.put("var4", varOList.get(i).getString("TJSJ"));	    //4
			vpd.put("var5", varOList.get(i).getString("LX"));	    //5
			vpd.put("var6", varOList.get(i).getString("WEB"));	    //6
			varList.add(vpd);
		}
		dataMap.put("varList", varList);
		ObjectExcelView erv = new ObjectExcelView();
		mv = new ModelAndView(erv,dataMap);
		FHLOG.save(Jurisdiction.getUsername(), "当前用户:"+Jurisdiction.getUsername()+"-->模板管理-->导出表格:");				//记录日志
			//记录日志

		return mv;
	}

}
