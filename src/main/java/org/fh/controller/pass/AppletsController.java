package org.fh.controller.pass;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import org.fh.constant.CLLXCONSTANT;
import org.fh.controller.base.BaseController;
import org.fh.entity.Page;
import org.fh.entity.PageData;
import org.fh.entity.pass.*;
import org.fh.mapper.dsno2.pass.AccountManagementMapper;
import org.fh.mapper.dsno2.pass.PASLICENSEMapper;
import org.fh.mapper.dsno2.pass.SurveilMapper;
import org.fh.mapper.dsno2.pass.TxzMapper;
import org.fh.service.pass.*;
import org.fh.service.system.FHlogService;
import org.fh.util.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 小程序接口
 */
@Slf4j
@RestController
@RequestMapping("/Paslicense")
@SuppressWarnings("all")
public class AppletsController extends BaseController {

    @Autowired
    AppletsService appletsService;
    @Autowired
    private FHlogService FHLOG;
    @Autowired
    AccountManagementService accountManagementService;
    @Autowired
    PASLICENSEService paslicenseService;
    @Autowired
    TxzController txzController;
    @Autowired
    TxzMapper txzMapper;
    @Autowired
    BlacklistService blacklistService;
    @Autowired
    private SurveilMapper surveilMapper;
    @Autowired
    private RoadInfoService roadInfoService;
    private static Properties pros = DbFH.getPprVue();
    @Autowired
    private VehicleApprovalService vehicleapprovalService;
    @Autowired
    private AccountManagementService accountmanagementService;
    @Autowired
    private TxzService txzService;
    @Autowired
    private ApprovalManagementService approvalManagementService;
    @Resource
    protected SixInOne sixInOne;
    @Autowired
    AccountManagementMapper accountManagementMapper;
    @Autowired
    PASLICENSEMapper paslicenseMapper;

    public String DesDncrypt(String param) {
        String info = "";
        try {
            info = DesEncryptor.desDncrypt(param, pros.getProperty("outintertfaceKey"));
        } catch (Exception e) {
            info = e.getMessage();
        } finally {
            return info;
        }
    }

    /**
     * 1.6 绑定车辆信息
     *
     * @param jsonParam
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/BindCar")
    @ResponseBody
    public Map<String, Object> BindCar(@RequestBody String strParam) throws Exception {
        JSONObject jsonParam = JSONObject.parseObject(DesDncrypt(strParam));
        Map<String, Object> map = new HashMap<String, Object>();
        String State = "0";
        String Message = "添加失败";
        String Value = "";
        String Content = "";
        List<PageData> varList = new ArrayList<>();
        org.json.JSONObject BackInfo = new org.json.JSONObject();
        String HPHM = jsonParam.getString("HPHM");
        String HPZL = jsonParam.getString("HPZL");
        if (!Tools.notEmpty(jsonParam.getString("CJH"))) {
            Message = "车架号不能为空";
            map.put("State", State);
            map.put("Message", Message);
            map.put("Value", Value);
            map.put("Content", Content);
            return map;
        }
        if (!Tools.notEmpty(HPZL)) {
            Message = "号牌种类不能为空";
            map.put("State", State);
            map.put("Message", Message);
            map.put("Value", Value);
            map.put("Content", Content);
            return map;
        }
        if (!Tools.notEmpty(HPHM)) {
            Message = "号牌号码不能为空";
            map.put("State", State);
            map.put("Message", Message);
            map.put("Value", Value);
            map.put("Content", Content);
            return map;
        } else {
            //判断车辆是否被重复绑定
            //如果传入企业主键，那就用企业主键去查有没有用绑定过
            if (Tools.notEmpty(jsonParam.getString("ENTERPRISEID"))) {
                Page page1 = new Page();
                PageData pd1 = new PageData();
                pd1.put("ENTERPRISEID", jsonParam.getString("ENTERPRISEID"));
                pd1.put("VEHNUM", HPHM);
                pd1.put("VEHTYPE", HPZL);
                page1.setPd(pd1);
                varList = appletsService.findByEN(page1);
            } else {
                Page page = new Page();
                PageData pd = new PageData();
                String MEMBERIDCARD = jsonParam.getString("MEMBERIDCARD");
                pd.put("MEMBERIDCARD", MEMBERIDCARD);
                pd.put("VEHNUM", HPHM);
                pd.put("VEHTYPE", HPZL);
                page.setPd(pd);
                varList = appletsService.findByAHE(page);
            }
            if (varList.size() > 0) {
                Message = "添加失败,该车辆已存在该名下";
            }
            //TODO 企业绑定车辆不做限制 ，个人只能绑定本人车辆
            else {
                //TODO 查询6合一接口校验信息
                //通过hpzl,hphm查询01c21
                org.json.JSONObject mapInfo = new org.json.JSONObject();
                mapInfo.put("xtlb", "01");
                mapInfo.put("hpzl", HPZL);
                mapInfo.put("hphm", HPHM);
                mapInfo.put("dabh", "");
                //添加需要的参数列表
                List BackInfoList = new ArrayList();
                BackInfoList.add("clsbdh");
                BackInfoList.add("fdjh");
                BackInfoList.add("qzbfqz");
                BackInfoList.add("cllx");
                BackInfoList.add("syxz");
                BackInfoList.add("syr");
                BackInfoList.add("yxqz");
                try {
                    System.out.println("mapInfo : " + mapInfo.toString());
                    BackInfo = sixInOne.getInfo(mapInfo, BackInfoList);
                    System.out.println(BackInfo);
                    FHLOG.save(IpUtils.getIpAddr(IpUtils.getRequestAttributes().getRequest()), HPHM + "六合一请求结果:" + BackInfo);
                } catch (Exception e) {
                    State = "0";
                    Message = e.getMessage();
                }
                if (BackInfo.length() != 0) {
                    if (!BackInfo.getString("clsbdh").contains(jsonParam.getString("CJH"))) {
                        Message = "提供绑定的信息不符";
                        map.put("State", State);
                        map.put("Message", Message);
                        map.put("Value", Value);
                        map.put("Content", Content);
                        return map;
                    }
                } else if (!HPHM.startsWith("桂A")){
                    Message = "未能查到该车辆信息";
                    map.put("State", State);
                    map.put("Message", Message);
                    map.put("Value", Value);
                    map.put("Content", Content);
                    return map;
                }
                PaslicenseEnterprisVeh paslicenseEnterprisVeh = new PaslicenseEnterprisVeh();
//                if (Tools.notEmpty(jsonParam.getString("ENTERPRISEID"))) {
//                    if (HPZL.equals("02")){
//                        paslicenseEnterprisVeh.setSHZT("1");
//                    }else {
//                        paslicenseEnterprisVeh.setSHZT("0");
//                    }
//                }
                paslicenseEnterprisVeh.setENTERPRISEID(jsonParam.getString("ENTERPRISEID"));
                paslicenseEnterprisVeh.setID(this.get32UUID());
                paslicenseEnterprisVeh.setVEHTYPE(HPZL);
                paslicenseEnterprisVeh.setVEHNUM(HPHM);
                Date now = new Date();
                paslicenseEnterprisVeh.setTJSJ(now);
                paslicenseEnterprisVeh.setSHZT("0");
//                paslicenseEnterprisVeh.setOWNERNAME(BackInfo.getString("syr"));
                paslicenseEnterprisVeh.setCJH(jsonParam.getString("CJH"));
                paslicenseEnterprisVeh.setFDJH(jsonParam.getString("FDJH"));
                paslicenseEnterprisVeh.setCLLX(BackInfo.length() > 0 ? BackInfo.getString("cllx") : "");
                paslicenseEnterprisVeh.setMEMBERIDCARD(jsonParam.getString("MEMBERIDCARD"));
                paslicenseEnterprisVeh.setVEHICLELICENSEPIC(jsonParam.getString("CLPIC"));
                try {
                    System.out.println(paslicenseEnterprisVeh);
                    vehicleapprovalService.saveInfo(paslicenseEnterprisVeh);
                    FHLOG.save(IpUtils.getIpAddr(IpUtils.getRequestAttributes().getRequest()),
                            "操作人身份证号码:" + jsonParam.getString("MEMBERIDCARD") + ":绑定车辆信息" + "HPHM:" + HPHM + "HPZL:" + HPZL);                //记录日志
                    State = "1";
                    Message = "添加成功";
                } catch (Exception e) {
                    Message = e.getMessage();
                }
            }
        }
        map.put("State", State);
        map.put("Message", Message);
        map.put("Value", Value);
        map.put("Content", Content);
        return map;
    }

    /**
     * 添加审批外地车辆
     *
     * @param strParam
     * @return
     */
    @PostMapping(value = "/addApproval")
    @ResponseBody
    public Object addApproval(@RequestBody String strParam) throws Exception {
        JSONObject jsonParam = JSONObject.parseObject(DesDncrypt(strParam));
        Map<String, Object> map = new HashMap<String, Object>();
        String State = "0";
        String Message = "添加失败";
        String Value = "";
        String Content = "";
        List<PageData> varList = null;
        String HPZL = jsonParam.getString("HPZL");//号牌种类
        String HPHM = jsonParam.getString("HPHM");//号牌号码
        String cjh = jsonParam.getString("CJH");//车架号
        String vehiclelicensepic = jsonParam.getString("VEHICLELICENSEPIC");//行驶证图片
        String FDJH = jsonParam.getString("FDJH");//发动机号
        String ownername = jsonParam.getString("OWNERNAME"); //车辆所有人
        String CLLX = jsonParam.getString("CLLX");//车辆类型
        String enterpriseid = jsonParam.getString("ENTERPRISEID");//企业信息id
        String memberidcard = jsonParam.getString("MEMBERIDCARD");//实际操作人身份证号
        String verb = jsonParam.getString("VERB");//行驶证副本
        String SHZT = jsonParam.getString("SHZT"); //审核状态
        String ownertype = jsonParam.getString("OWNERTYPE");//所属单位(1企业名下 2非企业名下)
        Date nsyxq = jsonParam.getDate("NSYXQ");
        Date now = new Date();
        if (!Tools.notEmpty(ownername)) {
            Message = "车辆所有人不能为空";
            map.put("State", State);
            map.put("Message", Message);
            map.put("Value", Value);
            map.put("Content", Content);
            return map;
        }
        if (!Tools.notEmpty(cjh)) {
            Message = "车架号不能为空";
            map.put("State", State);
            map.put("Message", Message);
            map.put("Value", Value);
            map.put("Content", Content);
            return map;
        }
        if (!Tools.notEmpty(CLLX)) {
            Message = "车辆类型不能为空";
            map.put("State", State);
            map.put("Message", Message);
            map.put("Value", Value);
            map.put("Content", Content);
            return map;
        }
        if (!Tools.notEmpty(verb)) {
            Message = "行驶证副本照片不能为空";
            map.put("State", State);
            map.put("Message", Message);
            map.put("Value", Value);
            map.put("Content", Content);
            return map;
        }
        if (!Tools.notEmpty(vehiclelicensepic)) {
            Message = "行驶证照片不能为空";
            map.put("State", State);
            map.put("Message", Message);
            map.put("Value", Value);
            map.put("Content", Content);
            return map;
        }
        if (!Tools.notEmpty(HPZL)) {
            Message = "号牌种类不能为空";
            map.put("State", State);
            map.put("Message", Message);
            map.put("Value", Value);
            map.put("Content", Content);
            return map;
        }
        if (!Tools.notEmpty(HPHM)) {
            Message = "号牌号码不能为空";
            map.put("State", State);
            map.put("Message", Message);
            map.put("Value", Value);
            map.put("Content", Content);
            return map;
        } else {
            //判断车辆是否被重复绑定
            //如果传入企业主键，那就用企业主键去查有没有用绑定过
            if (Tools.notEmpty(enterpriseid)) {
                Page page1 = new Page();
                PageData pd1 = new PageData();
                pd1.put("ENTERPRISEID", enterpriseid);
                pd1.put("VEHNUM", HPHM);
                pd1.put("VEHTYPE", HPZL);
                page1.setPd(pd1);
                varList = appletsService.findByEN(page1);
            } else {
                Page page = new Page();
                PageData pd = new PageData();
                pd.put("MEMBERIDCARD", memberidcard);
                pd.put("VEHNUM", HPHM);
                pd.put("VEHTYPE", HPZL);
                page.setPd(pd);
                varList = appletsService.findByAHE(page);
            }
            if (varList.size() > 0) {
                Message = "添加失败,该车辆已存在该名下";
                map.put("State", State);
                map.put("Message", Message);
                map.put("Value", Value);
                map.put("Content", Content);
                return map;
            } else {
                PaslicenseEnterprisVeh paslicenseEnterprisVeh = new PaslicenseEnterprisVeh();
                paslicenseEnterprisVeh.setVEHTYPE(HPZL); //号牌种类
                paslicenseEnterprisVeh.setVEHNUM(HPHM); //号牌号码
                paslicenseEnterprisVeh.setVEHICLELICENSEPIC(vehiclelicensepic);//行驶证图片
                paslicenseEnterprisVeh.setSHSJ(now);//审核时间
                paslicenseEnterprisVeh.setTJSJ(now);//添加时间
                paslicenseEnterprisVeh.setXGSJ(now);//修改时间
                paslicenseEnterprisVeh.setFDJH(FDJH);//发动机号
                paslicenseEnterprisVeh.setCJH(cjh);//车架号
                paslicenseEnterprisVeh.setCLLX(CLLX);//车辆类
                paslicenseEnterprisVeh.setVEHFB(verb);//行驶证副本
                paslicenseEnterprisVeh.setENTERPRISEID(enterpriseid); //企业信息id
                paslicenseEnterprisVeh.setOWNERTYPE(ownertype); //所属单位(1企业名下 2非企业名下)
                paslicenseEnterprisVeh.setID(this.get32UUID());
                paslicenseEnterprisVeh.setSHZT("3");//审核状态
                paslicenseEnterprisVeh.setOWNERNAME(ownername); //车辆所有人
                paslicenseEnterprisVeh.setMEMBERIDCARD(memberidcard); //实际操作人身份证号
                paslicenseEnterprisVeh.setWSCL("1");  //是否外省车辆（0否 1是）
                paslicenseEnterprisVeh.setNSYXQ(nsyxq); // 年审有效期
                if (!HPHM.contains("桂A") && nsyxq != null) {
                    // 外地车辆自动审批
                    if (nsyxq.after(now)) {
                        // 自动审核通过
                        paslicenseEnterprisVeh.setSHZT("1");
                        paslicenseEnterprisVeh.setSHR("自动通过");
                        paslicenseEnterprisVeh.setSHSJ(now);
                    } else {
                        // 自动审核不通过
                        paslicenseEnterprisVeh.setSHZT("2");
                        paslicenseEnterprisVeh.setSHR("不通过");
                        paslicenseEnterprisVeh.setSHSJ(now);
                    }
                }
                try {
                    vehicleapprovalService.saveInfo(paslicenseEnterprisVeh);
                    FHLOG.save(IpUtils.getIpAddr(IpUtils.getRequestAttributes().getRequest()),
                            "操作人身份证号码:" + jsonParam.getString("MEMBERIDCARD") + ":绑定车辆信息" + "HPHM:" + HPHM + "HPZL:" + HPZL);                //记录日志
                    State = "1";
                    Message = "添加成功";
                } catch (Exception e) {
                    Message = e.getMessage();
                }
            }
            map.put("State", State);
            map.put("Message", Message);
            map.put("Value", Value);
            map.put("Content", Content);
            return map;
        }

    }

    /**
     * 完善面签信息
     *
     * @param jsonParam
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/UpdateEnterInfo")
    @ResponseBody
    public Map<String, Object> UpdateEnterInfo(@RequestBody String strParam) throws Exception {
        JSONObject jsonParam = JSONObject.parseObject(DesDncrypt(strParam));
        Map<String, Object> map = new HashMap<String, Object>();
        String State = "1";
        String Message = "操作成功";
        String Value = "";
        String Content = "";
        if (!Tools.notEmpty(jsonParam.getString("ID"))) {
            State = "0";
            Message = "ID不能为空";
            map.put("State", State);
            map.put("Message", Message);
            map.put("Value", Value);
            map.put("Content", Content);
            return map;
        }
        if (!Tools.notEmpty(jsonParam.getString("ACCOUNT"))) {
            State = "0";
            Message = "面签账号不能为空";
            map.put("State", State);
            map.put("Message", Message);
            map.put("Value", Value);
            map.put("Content", Content);
            return map;
        } else {
            PaslicenseEnterpris paslicenseEnterpris = accountmanagementService.getInfoByAccountID(jsonParam.getString("ID"), jsonParam.getString("ACCOUNT"));
            String ZHLX = paslicenseEnterpris.getZHLX();
            // if (ZHLX.equals("企业")) {
            //     paslicenseEnterpris.setSHZT("3");
            // } else {
            //     paslicenseEnterpris.setSHZT("1");
            // }
            paslicenseEnterpris.setOWNERPHONE(jsonParam.getString("OWNERPHONE"));
            paslicenseEnterpris.setOWNERNAME(jsonParam.getString("OWNERNAME"));
            paslicenseEnterpris.setENTERPRISENAME(jsonParam.getString("ENTERPRISENAME"));
            paslicenseEnterpris.setORGANIZATIONCODE(jsonParam.getString("ORGANIZATIONCODE"));
            paslicenseEnterpris.setMANAGERNAME(jsonParam.getString("MANAGERNAME"));
            paslicenseEnterpris.setMANAGERPHONE(jsonParam.getString("MANAGERPHONE"));
            paslicenseEnterpris.setORGANIZATIONPIC(jsonParam.getString("ORGANIZATIONPIC"));
            paslicenseEnterpris.setAPPLICATIONPIC(jsonParam.getString("APPLICATIONPIC"));
            paslicenseEnterpris.setENTERPRISEADDRESS(jsonParam.getString("ENTERPRISEADDRESS"));

            Date now = new Date();
            paslicenseEnterpris.setXGSJ(now);
            paslicenseEnterpris.setUNITORCONTRACTPIC(jsonParam.getString("UNITORCONTRACTPIC"));
            try {
                accountmanagementService.updatePaslicenseEnterpris(paslicenseEnterpris);
                FHLOG.save(IpUtils.getIpAddr(IpUtils.getRequestAttributes().getRequest()),
                        "面签ID:" + jsonParam.getString("ID") + ":" + "完善面签信息");
            } catch (Exception e) {
                State = "0";
                Message = e.getMessage();
            }
        }

        map.put("State", State);
        map.put("Message", Message);
        map.put("Value", Value);
        map.put("Content", Content);

        return map;
    }

    /**
     * 1.8 完善車輛信息
     *
     * @param jsonParam
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/UpdateBindCar")
    @ResponseBody
    public Map<String, Object> UpdateBindCar(@RequestBody String strParam) throws Exception {
        JSONObject jsonParam = JSONObject.parseObject(DesDncrypt(strParam));
        Map<String, Object> map = new HashMap<String, Object>();
        String State = "0";
        String Message = "添加成功";
        String Value = "";
        String Content = "";
        //判空
        if (!Tools.notEmpty(jsonParam.getString("ID"))) {
            Message = "ID不能为空";
            map.put("State", State);
            map.put("Message", Message);
            map.put("Value", Value);
            map.put("Content", Content);
            return map;
        }
        if (!Tools.notEmpty(jsonParam.getString("HPZL"))) {
            Message = "号牌种类不能为空";
            map.put("State", State);
            map.put("Message", Message);
            map.put("Value", Value);
            map.put("Content", Content);
            return map;
        }
        if (!Tools.notEmpty(jsonParam.getString("HPHM"))) {
            Message = "号牌号码不能为空";
            map.put("State", State);
            map.put("Message", Message);
            map.put("Value", Value);
            map.put("Content", Content);
            return map;
        }
        if (!Tools.notEmpty(jsonParam.getString("ACTIDCARD"))) {
            Message = "会员身份证号码不能为空";
            map.put("State", State);
            map.put("Message", Message);
            map.put("Value", Value);
            map.put("Content", Content);
            return map;
        }
        if (!Tools.notEmpty(jsonParam.getString("VEHICLELICENSEPIC"))) {
            Message = "行驶证照片不能为空";
            map.put("State", State);
            map.put("Message", Message);
            map.put("Value", Value);
            map.put("Content", Content);
            return map;
        }
        if (!Tools.notEmpty(jsonParam.getString("VEHPIC"))) {
            Message = "车辆照片不能为空";
            map.put("State", State);
            map.put("Message", Message);
            map.put("Value", Value);
            map.put("Content", Content);
            return map;
        }
        if (!Tools.notEmpty(jsonParam.getString("VEHFB"))) {
            Message = "行驶证副本或车辆委托书不能为空";
            map.put("State", State);
            map.put("Message", Message);
            map.put("Value", Value);
            map.put("Content", Content);
            return map;
        }
        if (!Tools.notEmpty(jsonParam.getString("OWNERNAME"))) {
            Message = "车辆所有人信息-姓名不能为空";
            map.put("State", State);
            map.put("Message", Message);
            map.put("Value", Value);
            map.put("Content", Content);
            return map;
        }
        if (!Tools.notEmpty(jsonParam.getString("OWNERIDCARD"))) {
            Message = "车辆所有人信息-身份证不能为空";
            map.put("State", State);
            map.put("Message", Message);
            map.put("Value", Value);
            map.put("Content", Content);
            return map;
        }
        if (!Tools.notEmpty(jsonParam.getString("OWNERPHONE"))) {
            Message = "车辆所有人信息-手机号不能为空";
            map.put("State", State);
            map.put("Message", Message);
            map.put("Value", Value);
            map.put("Content", Content);
            return map;
        }
        /* if (!Tools.notEmpty(jsonParam.getString("ACTOWNER"))) {
            Message = "实际使用人信息-姓名不能为空";
            map.put("State", State);
            map.put("Message", Message);
            map.put("Value", Value);
            map.put("Content", Content);
            return map;
        }
        if (!Tools.notEmpty(jsonParam.getString("ACTPHONE"))) {
            Message = "实际使用人信息-手机号不能为空";
            map.put("State", State);
            map.put("Message", Message);
            map.put("Value", Value);
            map.put("Content", Content);
            return map;
        }
        if (!Tools.notEmpty(jsonParam.getString("ACTPIC"))) {
            Message = "实际使用人信息-身份证图片不能为空";
            map.put("State", State);
            map.put("Message", Message);
            map.put("Value", Value);
            map.put("Content", Content);
            return map;
        } */ else {
            //查询数据
            PaslicenseEnterprisVeh paslicenseEnterprisVeh = new PaslicenseEnterprisVeh();
            paslicenseEnterprisVeh.setID(jsonParam.getString("ID"));
//            paslicenseEnterprisVeh.setVEHTYPE(jsonParam.getString("HPZL"));
//            paslicenseEnterprisVeh.setVEHNUM(jsonParam.getString("HPHM"));
//            paslicenseEnterprisVeh.setACTIDCARD(jsonParam.getString("ACTIDCARD"));
            PaslicenseEnterprisVeh Info = vehicleapprovalService.findByInfo(paslicenseEnterprisVeh);
            // 更新数据
            Info.setVEHICLELICENSEPIC(jsonParam.getString("VEHICLELICENSEPIC"));
            Info.setVEHPIC(jsonParam.getString("VEHPIC"));
            Info.setVEHFB(jsonParam.getString("VEHFB"));
            Info.setACTIDCARD(jsonParam.getString("ACTIDCARD"));
            if (Tools.notEmpty(jsonParam.getString("OWNERTYPE"))) {
                Info.setOWNERTYPE(jsonParam.getString("OWNERTYPE"));
            }
            Info.setOWNERNAME(jsonParam.getString("OWNERNAME"));
            Info.setOWNERIDCARD(jsonParam.getString("OWNERIDCARD"));
            Info.setOWNERPHONE(jsonParam.getString("OWNERPHONE"));
            Info.setACTOWNER(jsonParam.getString("ACTOWNER"));
            Info.setACTPHONE(jsonParam.getString("ACTPHONE"));
            Info.setACTPIC(jsonParam.getString("ACTPIC"));
            Info.setCLWTS(jsonParam.getString("CLWTS"));
            Info.setMEMBERIDCARD(jsonParam.getString("MEMBERIDCARD"));
            Info.setENTERPRISEID(jsonParam.getString("ENTERPRISEID"));
            Info.setNSYXQ(jsonParam.getDate("NSYXQ"));
            Date now = new Date();
            Info.setXGSJ(now);
            if (jsonParam.getString("HPHM").contains("桂A")) {
                String shzt = "3";
                org.json.JSONObject BackInfo = new org.json.JSONObject();
                org.json.JSONObject mapInfo = new org.json.JSONObject();
                mapInfo.put("xtlb", "01");
                mapInfo.put("hpzl", Info.getVEHTYPE());
                mapInfo.put("hphm", Info.getVEHNUM());
                mapInfo.put("dabh", "");
                List BackInfoList = new ArrayList();
                BackInfoList.add("syr");
                BackInfoList.add("cllx");

                try {
                    System.out.println("mapInfo : " + mapInfo.toString());
                    BackInfo = sixInOne.getInfo(mapInfo, BackInfoList);
                    System.out.println(BackInfo);
                    FHLOG.save(IpUtils.getIpAddr(IpUtils.getRequestAttributes().getRequest()), Info.getVEHNUM() + "六合一请求结果:" + BackInfo);
                } catch (Exception e) {
                    Message = e.getMessage();
                }

                String SYR = "";
                String CLLX = Info.getCLLX();
                if (BackInfo.length() > 0) {
                    SYR = BackInfo.getString("syr");
                    CLLX = BackInfo.getString("cllx");
                    Info.setCLLX(CLLX);
                }

                try {
                    String CLLX_NAME = (String)CLLXCONSTANT.CLLX.get(CLLX);
                    if (Tools.isEmpty(CLLX_NAME)) {
                        System.out.println("CLLX IS NULL: " + CLLX);
                    }
                    if (Tools.notEmpty(jsonParam.getString("ENTERPRISEID"))) {
                        String ENTERPRISENAME = this.accountManagementMapper.selectById(jsonParam.getString("ENTERPRISEID"));
                        // 查询不到车辆信息 || 面签账号名称=所有人
                        if (SYR == "" || ENTERPRISENAME.equals(SYR)) {
                            // CLLX_NAME=null || CLLX_NAME不包含自卸
                            if (Tools.isEmpty(CLLX_NAME) || !CLLX_NAME.contains("自卸")) {
                                if (jsonParam.getString("HPZL").equals("02") || jsonParam.getString("HPZL").equals("52")) {
                                    shzt = "1";
                                    Info.setSHR("自动通过");
                                    Info.setSHSJ(now);
                                }
                            }
                        }
                    }
                } catch (Exception var19) {
                    Exception e = var19;
                    e.printStackTrace();
                }
                Info.setSHZT(shzt);
            }
            try {
                vehicleapprovalService.edit(Info);
                FHLOG.save(IpUtils.getIpAddr(IpUtils.getRequestAttributes().getRequest()),
                        "车辆ID:" + jsonParam.getString("ID") + "" + "完善车辆信息");

                State = "1";
            } catch (Exception e) {
                Message = e.getMessage();
            }
        }

        map.put("State", State);
        map.put("Message", Message);
        map.put("Value", Value);
        map.put("Content", Content);

        return map;
    }

    /**
     * 查询路段信息接口
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/GetRoadData")
    @ResponseBody
    public Map<String, Object> GetRoadData(@RequestBody String strParam) throws Exception {

        JSONObject jsonParam = JSONObject.parseObject(DesDncrypt(strParam));
        Map<String, Object> map = new HashMap<String, Object>();
        String Message = "";
        String State = "";
        Map<String, Object> params = new HashMap();
        String LDMC = jsonParam.getString("LDMC");
        if (Tools.notEmpty(LDMC)) {
            params.put("LDMC", LDMC);
        }
        String DJ = jsonParam.getString("DJ");
        if (Tools.notEmpty(DJ)) {
            String ArrayDATA_IDS[] = DJ.split(",");
            List<Integer> ids = new ArrayList<>();
            for (String string : ArrayDATA_IDS) {
                ids.add(Integer.parseInt(string));
            }
            params.put("ids", ids);
        }
        List<RoadVo> varList = appletsService.datalistPageByDJ(params);
        if (varList.size() == 0) {
            Message = "操作失敗";
            State = "0";
        } else {
            String dataToJsonStr = new Gson().toJson(varList);
            String Content = DesEncryptor.encrypt(dataToJsonStr, pros.getProperty("outintertfaceKey"));
            map.put("Content", Content);
            Message = "操作成功";
            State = "1";
        }
        map.put("State", State);
        map.put("Message", Message);
        return map;

    }


    /**
     * 获取面签企业信息
     *
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/GetEnterInfo")
    @ResponseBody
    public Map<String, Object> GetEnterInfo(@RequestBody String strParam) throws Exception {
        JSONObject jsonParam = JSONObject.parseObject(DesDncrypt(strParam));
        Map<String, Object> map = new HashMap<String, Object>();
        String Message = "";
        String State = "";
        PageData pd = new PageData();
        pd = this.getPageData();
        String account = jsonParam.getString("ACCOUNT");
        String password = jsonParam.getString("PASSWORD");
        pd.put("ACCOUNT", account);
        pd.put("PASSWORD", password);
        pd = appletsService.findPass(pd);
        if (pd == null || "".equals(pd)) {
            Message = "操作失敗";
            State = "0";
        } else {
            String Content = DesEncryptor.encrypt(JSONObject.toJSON(pd).toString(), pros.getProperty("outintertfaceKey"));
            map.put("Content", Content);
            Message = "操作成功";
            State = "1";
        }
        map.put("State", State);
        map.put("Message", Message);
        return map;
    }


    /**
     * 删除面签账号绑定车辆
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/DeleteCar")
    @ResponseBody
    public Map<String, Object> DeleteCar(@RequestBody String strParam) throws Exception {
        JSONObject jsonParam = JSONObject.parseObject(DesDncrypt(strParam));

        Map<String, Object> map = new HashMap<String, Object>();
        AtomicReference<String> Message = new AtomicReference<>("操作成功");
        AtomicReference<String> State = new AtomicReference<>("1");
        PageData pd = new PageData();
        String DATA_IDS = jsonParam.getString("DATA_IDS");
        if (Tools.notEmpty(DATA_IDS)) {
            List<CarIdVo> list;
            list = JSONArray.toList(JSONArray.fromObject(jsonParam.getString("DATA_IDS")), CarIdVo.class);//这里的t是Class<T>

            for (int i = 0; i < list.size(); i++) {
                CarIdVo data = list.get(i);
//                JSONObject data = JSONObject.parseObject(String.valueOf(list.get(i)));
                pd.put("VEHNUM", data.getVEHNUM());
                pd.put("ID", data.getID());

                try {
                    vehicleapprovalService.delete(pd);
                    FHLOG.save(IpUtils.getIpAddr(IpUtils.getRequestAttributes().getRequest()),
                            "车辆ID:" + jsonParam.getString("DATA_IDS") + ":" + "删除面签账号绑定车辆");
                } catch (Exception e) {
                    Message.set("操作失敗");
                    State.set("0");
                    e.printStackTrace();
                }
            }
//            list.forEach(data -> {
//                System.out.println(data);
//                pd.put("VEHNUM",data.getVEHNUM());
//                pd.put("ID",data.getVEHNUM());
//                try {
//                    vehicleapprovalService.delete(pd);
//                } catch (Exception e) {
//                    Message.set("操作失敗");
//                    State.set("0");
//                    e.printStackTrace();
//                }
//            });
        }
//        if (Tools.notEmpty(DATA_IDS)) {
//            String ArrayDATA_IDS[] = DATA_IDS.split(",");
//            vehicleapprovalService.deleteAll(ArrayDATA_IDS);
//            Message = "操作成功";
//            State = "1";
//        } else {
//            Message = "操作失敗";
//            State = "0";
//        }
        map.put("State", State);
        map.put("Message", Message);
        return map;
    }

    /**
     * 1.7 获取所有绑定车辆列表
     *
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/GetBindCarList")
    @ResponseBody
    public Map<String, Object> GetBindCarList(@RequestBody String strParam) throws Exception {
        JSONObject jsonParam = JSONObject.parseObject(DesDncrypt(strParam));
        Map<String, Object> map = new HashMap<String, Object>();
        String Message = "";
        String State = "";
        PageData pd = new PageData();
        Page page = new Page();
        List varList = new ArrayList();

        String VEHNUM = jsonParam.getString("HPHM");

        String ENTERPRISEID = jsonParam.getString("ENTERPRISEID");
        if (Tools.notEmpty(ENTERPRISEID)) {
            pd.put("ENTERPRISEID", ENTERPRISEID);
            if (Tools.notEmpty(VEHNUM)) {
                pd.put("VEHNUM", VEHNUM);
            }
        } else {
            String MEMBERIDCARD = jsonParam.getString("MEMBERIDCARD");
            if (Tools.notEmpty(MEMBERIDCARD)) {
                pd.put("MEMBERIDCARD", MEMBERIDCARD);
            }
            if (Tools.notEmpty(VEHNUM)) {
                pd.put("VEHNUM", VEHNUM);
            }
        }
        String PageIndex = jsonParam.getString("PageIndex");
        String PageSize = jsonParam.getString("PageSize");
        page.setPd(pd);
        page.setCurrentPage(Integer.parseInt(PageIndex));
        page.setShowCount(Integer.parseInt((PageSize)));
        try {
            varList = appletsService.findByAHEList(page);
            String Content = DesEncryptor.encrypt(JSONObject.toJSON(varList).toString(), pros.getProperty("outintertfaceKey"));
            map.put("Content", Content);
            map.put("PageIndex", page.getCurrentPage());
            map.put("PageSize", page.getShowCount());
            if (varList.size() == 0) {
                Message = "绑定车辆列表为空";
                State = "0";
            } else {
                Message = "操作成功";
                State = "1";
            }
        } catch (Exception e) {
            Message = e.getMessage();
            e.printStackTrace();
        }
        map.put("State", State);
        map.put("Message", Message);
        return map;
    }

    /**
     * 添加区域通行证信息
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/AddPaslicenseInfo")
    @ResponseBody
    public Object AddPaslicenseInfo(@RequestBody String strParam) throws Exception {
        //TODO 添加长期通行证时需查看是否有未审核通行证
        JSONObject jsonParam = JSONObject.parseObject(DesDncrypt(strParam));
        Map<String, Object> map = new HashMap<String, Object>();
        String Message = "";
        String State = "";
        PageData pd = new PageData();
        pd = this.getPageData();
        List<PASLICENSEWithBLOBs> txzList = new ArrayList<>();
        List<String> TxzXhList = new ArrayList<>();
        List<String> hphm = new ArrayList<>();
        List<PASLICENSEWithBLOBs> list = new ArrayList<>();
        txzList = JSONArray.toList(JSONArray.fromObject(jsonParam.getString("txzList")), PASLICENSEWithBLOBs.class);//这里的t是Class<T>
        for (int i = 0; i < txzList.size(); i++) {
            JSONObject data = JSONObject.parseObject(String.valueOf(txzList.get(i)));
            PASLICENSEWithBLOBs TXZ = new PASLICENSEWithBLOBs();
            Thread.sleep(650L);
            //循环睡眠
            DateFormat bf = new SimpleDateFormat("yyyyMMddHHmmssss");
            Date date = new Date();
            String format = bf.format(date);
            //生成时间戳
            Random r = new Random();
            //生成随机数
            SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");
            // 00:00:00 - 23:59:59
            String yxrqs = data.getString("YXRQS");
            String yxrqz = data.getString("YXRQZ");
            //TODO 添加yxrq字段
            TXZ.setYxrq(yxrqs.substring(0,10) + '至' + yxrqz.substring(0,10));

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            //java.util.Date对象
            Date time = sdf.parse(yxrqs);
            Date time1 = sdf.parse(yxrqz);

            TXZ.setXh(this.get32UUID());
            TxzXhList.add(TXZ.getXh());

            TXZ.setTxzhm(format + r.nextInt(1000));
            TXZ.setTxzlx(data.getString("TXZLX"));
            TXZ.setHpzl(data.getString("HPZL"));
            TXZ.setHphm(data.getString("HPHM"));
            hphm.add(TXZ.getHphm());
            String CLLX = "";
            if (data.getString("LSTXZ").equals("1")) {
                List resmap = this.paslicenseService.trafficStatus(data.getString("HPHM"), data.getString("HPZL"), data.getString("LSTXZ"));
                Iterator var27 = resmap.iterator();

                while(var27.hasNext()) {
                    Map stringObjectMap = (Map)var27.next();
                    if (stringObjectMap != null && stringObjectMap.size() > 0 && stringObjectMap.get("zs") != null) {
                        Integer zs = (Integer)stringObjectMap.get("zs");
                        if (zs > 6) {
                            Message = Message + "该车辆本月办理临时通行证已超过6次;";
                        }
                    }
                }

                if (Message.contains("该车辆本月办理临时通行证已超过6次;")) {
                    continue;
                }
            } else {
                List resmap = new ArrayList();
                List objectMap = this.blacklistService.highstate(data.getString("HPHM"), data.getString("HPZL"), data.getString("LSTXZ"));
                if (objectMap != null && objectMap.size() > 0) {
                    Iterator var44 = objectMap.iterator();

                    while(var44.hasNext()) {
                        Map stringObjectMap = (Map)var44.next();
                        String YXRQZ = stringObjectMap.get("YXRQZ").toString();
                        String TIME = DateUtil.getTime();
                        SimpleDateFormat format2 = new SimpleDateFormat("yyyy-MM-dd");
                        Date yxrqz1 = format2.parse(YXRQZ);
                        Date endDate = format2.parse(TIME);
                        long day = (yxrqz1.getTime() - endDate.getTime()) / 86400000L;
                        if (day > 0L && day >= 20L) {
                            resmap.add("1");
                        }
                    }

                    if (resmap.size() > 0) {
                        Message = Message + "该车辆存在未过期通行证,未到续办时间,可在到期前20天内续办;";
                        continue;
                    }
                }
            }

            //判断是否为包含桂，包含桂则核查信息
            org.json.JSONObject b = new org.json.JSONObject();
            if (data.getString("HPHM").equals("桂A")) {
                b = getSIX(data.getString("HPZL"), data.getString("HPHM"), "01");
            }
            if (b != null && b.length() > 0) {
                //校验信息
                try {
                    TXZ.setCllx(b.getString("cllx"));
                    TXZ.setSyr(b.getString("syr"));
                    CLLX = (String)CLLXCONSTANT.CLLX.get(b.getString("cllx"));
                } catch (Exception e) {
                    Message = Message + "系统繁忙请稍后重试;";
                    continue;
                }
            } else {
                TXZ.setCllx(data.getString("CLLX"));
                TXZ.setSyr(data.getString("SYR"));
                CLLX = (String) CLLXCONSTANT.CLLX.get(data.getString("CLLX"));
            }

            String ENTERPRISEID;
            String IdCard;
            if (Tools.notEmpty(data.getString("ENTERPRISEID"))) {
                if (Tools.notEmpty(CLLX) && !CLLX.contains("自卸")) {
                    ENTERPRISEID = this.accountManagementService.selectId(data.getString("ENTERPRISEID"));
                    if (!Tools.notEmpty(ENTERPRISEID)) {
                        Message = Message + "面签账号未通过审批,无法办理通行证;";
                        continue;
                    }

                    IdCard = this.appletsService.selectCard(data.getString("HPHM"), data.getString("HPZL"), data.getString("ENTERPRISEID"));
                    if (!Tools.notEmpty(IdCard)) {
                        Message = Message + data.getString("HPHM") + "该车辆未通过审批,无法办理通行证;";
                        continue;
                    }
                }

                if (!data.getString("HPHM").contains("桂A")) {
                    ENTERPRISEID = this.accountManagementService.selectId(data.getString("ENTERPRISEID"));
                    if (!Tools.notEmpty(ENTERPRISEID)) {
                        Message = Message + "面签账号未通过审批,无法办理通行证;";
                        continue;
                    }

                    IdCard = this.appletsService.selectCard(data.getString("HPHM"), data.getString("HPZL"), data.getString("ENTERPRISEID"));
                    if (!Tools.notEmpty(IdCard)) {
                        Message = Message + data.getString("HPHM") + "该车辆未通过审批,无法办理通行证;";
                        continue;
                    }
                } else if (data.getString("LSTXZ").equals("0")) {
                    ENTERPRISEID = this.accountManagementService.selectId(data.getString("ENTERPRISEID"));
                    if (!Tools.notEmpty(ENTERPRISEID)) {
                        Message = Message + "面签账号未通过审批,无法办理长期通行证;";
                        continue;
                    }

                    IdCard = this.appletsService.selectCard(data.getString("HPHM"), data.getString("HPZL"), data.getString("ENTERPRISEID"));
                    if (!Tools.notEmpty(IdCard)) {
                        Message = Message + data.getString("HPHM") + "该车辆未通过审批,无法办理长期通行证;";
                        continue;
                    }
                }
            } else {
                if (Tools.notEmpty(CLLX) && CLLX.contains("自卸")) {
                    if (!CLLX.contains("轻型自卸货车")) {
                        // 非轻型自卸
                        Message = Message + data.getString("HPHM") + "该车辆不符合线上办理通行证要求，请到线下进行办理;";
                        continue;
                    }
                    if (!data.getString("HPHM").contains("桂A")) {
                        // 非省内
                        Message = Message + data.getString("HPHM") + "当前功能不支持，请准备好相关材料到南宁市民中心（南宁市良庆区玉洞大道33号）C座四楼交警支队机动大队C4002号窗口办理，咨询电话0771-4953272;";
                        continue;
                    }
                    if (DateUtil.getDaySub(yxrqs, yxrqz) >= 30) {
                        // 由于是00:00:00 - 23:59:59，所以判断条件要判断29天的
                        Message = Message + data.getString("HPHM") + "轻型自卸货车只能申请一个月内的通行证，请修改时间重新提交;";
                        continue;
                    }
                }

                if (!data.getString("HPHM").contains("桂A")) {
                    ENTERPRISEID = this.appletsService.selectByIdCard(data.getString("HPHM"), data.getString("HPZL"));
                    if (!ENTERPRISEID.equals("1")) {
                        Message = Message + data.getString("HPHM") + "该车辆未绑定成功，请尝试到车辆列表-勾选该车辆删除，再重新绑定车辆。";
                        continue;
                    }

                    if (data.getString("LSTXZ").equals("0")) {
                        Message = Message + data.getString("HPHM") + "该车辆属于非桂A车辆，请办理临时通行证和五类路通行证;";
                        continue;
                        // IdCard = this.appletsService.GetByIdCard(data.getString("HPHM"), data.getString("HPZL"));
                        // if (!Tools.notEmpty(IdCard)) {
                        //     Message = Message + data.getString("HPHM") + "该车辆未补全身份信息,无法办理长期通行证;";
                        //     continue;
                        // }
                        //
                        // String MEMBERIDCARD = null;
                        //
                        // try {
                        //     MEMBERIDCARD = data.getString("MEMBERIDCARD");
                        // } catch (Exception var38) {
                        //     var38.printStackTrace();
                        // }
                        //
                        // if (IdCard != null && MEMBERIDCARD != null && !MEMBERIDCARD.equals(IdCard)) {
                        //     Message = Message + data.getString("HPHM") + "非车主本人操作不可办理长期通行证;";
                        //     continue;
                        // }
                    }
                } else if (data.getString("LSTXZ").equals("0")) {
                    ENTERPRISEID = this.appletsService.GetIdCard(data.getString("HPHM"), data.getString("HPZL"));
                    if (!Tools.notEmpty(ENTERPRISEID)) {
                        Message = Message + "该车辆未绑定面签账号，无法办理长期通行证;";
                        continue;
                    }

                    IdCard = this.accountManagementService.selectId(ENTERPRISEID);
                    if (!Tools.notEmpty(IdCard)) {
                        Message = Message + "面签账号未通过审批,无法办理长期通行证;";
                        continue;
                    }

                    IdCard = this.appletsService.selectCard(data.getString("HPHM"), data.getString("HPZL"), ENTERPRISEID);
                    if (!Tools.notEmpty(IdCard)) {
                        Message = Message + data.getString("HPHM") + "该车辆未通过审批,无法办理长期通行证;";
                        continue;
                    }

                    String MEMBERIDCARD = null;

                    try {
                        MEMBERIDCARD = data.getString("MEMBERIDCARD");
                    } catch (Exception var37) {
                        var37.printStackTrace();
                    }

                    if (MEMBERIDCARD != null && IdCard != null && !MEMBERIDCARD.equals(IdCard)) {
                        Message = Message + data.getString("HPHM") + "非车主本人操作不可办理长期通行证;";
                        continue;
                    }
                }
            }

            TXZ.setYlzd5(data.getString("YLZD5"));
            TXZ.setTxsbbh(txzController.GetDev(data.getString("YLZD5")));
            TXZ.setYlzd6(data.getString("YLZD6"));
            TXZ.setDljb(data.getString("DLJB"));
            TXZ.setYlzd3(this.txzController.GetDlsh(data.getString("YLZD5")));
            TXZ.setDjrq(new Date());
            TXZ.setGxsj(new Date());
            TXZ.setYxrqs(time);
            TXZ.setYxrqz(time1);
            TXZ.setYssbsd(data.getString("YSSBSD"));
            TXZ.setJbr(data.getString("JBR"));
            TXZ.setLstxz(data.getString("LSTXZ"));
            TXZ.setAuditing_status("1");
            list.add(TXZ);
        }
        if (list.size() == 0) {
            State = "0";
        } else {

            try {
                paslicenseMapper.insertList(list);
                FHLOG.save(IpUtils.getIpAddr(IpUtils.getRequestAttributes().getRequest()),
                        "号牌号码:" + hphm + "" + "添加区域通行证信息 序号:" + TxzXhList.toString());

                Message = "操作成功";
                State = "1";
            } catch (Exception e) {
                log.error("添加区域通行证信息失败：{}", strParam, e);
                throw new RuntimeException(e);
            }
        }
        map.put("State", State);
        map.put("Message", Message);
        return map;
    }


    /**
     * 获取待审批通行证列表
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/GetPaslicenseSPList")
    @ResponseBody
    public Map<String, Object> GetPaslicenseSPList(@RequestBody String strParam) throws Exception {
        JSONObject jsonParam = JSONObject.parseObject(DesDncrypt(strParam));
        Map<String, Object> map = new HashMap<String, Object>();
        String Message = "";
        String State = "";
        PageData pd = new PageData();
        Page page = new Page();
        pd = this.getPageData();
        String ENTERPRISEID = jsonParam.getString("ENTERPRISEID");
        if (Tools.notEmpty(ENTERPRISEID)) {
            pd.put("ENTERPRISEID", ENTERPRISEID);
        } else {
            String MEMBERIDCARD = jsonParam.getString("MEMBERIDCARD");
            if (Tools.notEmpty(MEMBERIDCARD)) {
                pd.put("MEMBERIDCARD", MEMBERIDCARD);
            }
        }
        String HPHM = jsonParam.getString("HPHM");
        if (Tools.notEmpty(HPHM)) {
            pd.put("HPHM", HPHM.trim());
        }
        String PageIndex = jsonParam.getString("PageIndex");
        String PageSize = jsonParam.getString("PageSize");
        page.setPd(pd);
        page.setCurrentPage(Integer.parseInt(PageIndex));
        page.setShowCount(Integer.parseInt((PageSize)));

        List<PageData> varList = txzMapper.listPageselectByList(page);
        String Content = DesEncryptor.encrypt(JSONObject.toJSON(varList).toString(), pros.getProperty("outintertfaceKey"));
        map.put("Content", Content);
        map.put("PageIndex", page.getCurrentPage());
        map.put("PageSize", page.getShowCount());
        if (varList.size() == 0) {
            Message = "操作失败";
            State = "0";
        } else {
            Message = "操作成功";
            State = "1";
        }
        map.put("State", State);
        map.put("Message", Message);
        return map;
    }

    /**
     * 获取电子通行证列表
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/GetPaslicenseList")
    @ResponseBody
    public Map<String, Object> GetPaslicenseList(@RequestBody String strParam) throws Exception {
        JSONObject jsonParam = JSONObject.parseObject(DesDncrypt(strParam));
        Map<String, Object> map = new HashMap<String, Object>();
        String Message = "";
        String State = "";
        PageData pd = new PageData();
        Page page = new Page();
        pd = this.getPageData();

        String ENTERPRISEID = jsonParam.getString("ENTERPRISEID");
        if (Tools.notEmpty(ENTERPRISEID)) {
            pd.put("ENTERPRISEID", ENTERPRISEID);
        } else {
            String MEMBERIDCARD = jsonParam.getString("MEMBERIDCARD");
            if (Tools.notEmpty(MEMBERIDCARD)) {
                pd.put("MEMBERIDCARD", MEMBERIDCARD);
            }
        }
        String HPHM = jsonParam.getString("HPHM");
        if (Tools.notEmpty(HPHM)) {
            pd.put("HPHM", HPHM.trim());
        }
        String PageIndex = jsonParam.getString("PageIndex");
        String PageSize = jsonParam.getString("PageSize");
        page.setPd(pd);
        page.setCurrentPage(Integer.parseInt(PageIndex));
        page.setShowCount(Integer.parseInt((PageSize)));
        List<PageData> varList = txzMapper.listPageselectList(page);
        String Content = DesEncryptor.encrypt(JSONObject.toJSON(varList).toString(), pros.getProperty("outintertfaceKey"));
        map.put("Content", Content);
        map.put("PageIndex", page.getCurrentPage());
        map.put("PageSize", page.getShowCount());
        if (varList.size() == 0) {
            Message = "操作失败";
            State = "0";
        } else {
            Message = "操作成功";
            State = "1";
        }
        map.put("State", State);
        map.put("Message", Message);
        return map;
    }


    /**
     * 获取电子通行证详细
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/GetPaslicenseInfo")
    @ResponseBody
    public Object GetPaslicenseInfo(@RequestBody String strParam) throws Exception {
        JSONObject jsonParam = JSONObject.parseObject(DesDncrypt(strParam));
        Map<String, Object> map = new HashMap<String, Object>();
        String Message = "";
        String State = "";
        PageData pd = new PageData();
        pd = this.getPageData();
        PASLICENSEWithBLOBs record = paslicenseService.selectListById(jsonParam.getString("XH"));
        if (record == null || "".equals(record)) {
            Message = "操作失败";
            State = "0";
        } else {
            String Content = DesEncryptor.encrypt(JSONObject.toJSON(record).toString(), pros.getProperty("outintertfaceKey"));
            map.put("Content", Content);
            Message = "操作成功";
            State = "1";
        }
        map.put("State", State);
        map.put("Message", Message);
        return map;
    }


    /**
     * 查询最新的一条通行证
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/GetLastPaslicenseInfo")
    @ResponseBody
    public Object GetLastPaslicenseInfo(@RequestBody String strParam) throws Exception {
        JSONObject jsonParam = JSONObject.parseObject(DesDncrypt(strParam));
        Map<String, Object> map = new HashMap<String, Object>();
        String Message = "";
        String State = "";
        PageData pd = new PageData();
        pd = this.getPageData();
        String hpzl = jsonParam.getString("HPZL");
        String hphm = jsonParam.getString("HPHM");
        String MEMBERIDCARD = jsonParam.getString("MEMBERIDCARD");
        String ENTERPRISEID = null;

        try {
            ENTERPRISEID = jsonParam.getString("ENTERPRISEID");
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (ENTERPRISEID != null) {
            MEMBERIDCARD = null;
        }
        PASLICENSEWithBLOBs record = paslicenseService.selectById(hpzl, hphm, MEMBERIDCARD, ENTERPRISEID);

        if (record == null || "".equals(record)) {
            Message = "操作失败";
            State = "0";
        } else {
            String Content = DesEncryptor.encrypt(JSONObject.toJSON(record).toString(), pros.getProperty("outintertfaceKey"));
            map.put("Content", Content);
            Message = "操作成功";
            State = "1";
        }
        map.put("State", State);
        map.put("Message", Message);
        return map;
    }

    //---------------9.2修改新增--------------------------

    /**
     * 小程序修改密码接口  需要参数密码经过md5加密 ACCOUNT OLDPASSWORD  NEWPASSWORD
     *
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/UpdatePwd")
    @ResponseBody
    public Map<String, Object> UpdatePwd(@RequestBody String strParam) throws Exception {
        JSONObject jsonParam = JSONObject.parseObject(DesDncrypt(strParam));
        Map<String, Object> map = new HashMap<String, Object>();
        String Message = "";
        String State = "";
        PageData pd = new PageData();
        pd = this.getPageData();
        String account = jsonParam.getString("ACCOUNT");
        String password = jsonParam.getString("OLDPASSWORD");
        pd.put("ACCOUNT", account);
        pd.put("PASSWORD", password);
        //校验帐号密码是否一样并取出id
        PageData pd2 = appletsService.findPass(pd);
        if (pd2 == null || "".equals(pd)) {
            Message = "帐号或者密码不对";
            State = "0";
        } else {
            pd.put("ID", pd2.getString("ID"));
            //修改密码
            pd.put("PASSWORD", jsonParam.getString("NEWPASSWORD"));
            accountmanagementService.edit(pd);
            FHLOG.save(IpUtils.getIpAddr(IpUtils.getRequestAttributes().getRequest()),
                    "修改账号:" + jsonParam.getString("ACCOUNT") + "" + "密码");

            Message = "操作成功";
            State = "1";
        }
        map.put("State", State);
        map.put("Message", Message);
        return map;
    }

    /**
     * 根据车辆主键获取车辆详细信息
     *
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/GetInfoById")
    @ResponseBody
    public Map<String, Object> GetInfoById(@RequestBody String strParam) throws Exception {
        JSONObject jsonParam = JSONObject.parseObject(DesDncrypt(strParam));
        Map<String, Object> map = new HashMap<String, Object>();
        String Message = "操作失败";
        String State = "0";
        String ID = jsonParam.getString("ID");

        try {
            PaslicenseEnterprisVeh data = vehicleapprovalService.GetInfoById(ID);
            net.sf.json.JSONObject jsonObject = net.sf.json.JSONObject.fromObject(data);
            if (data.getSHSJ() != null) {
                jsonObject.put("SHSJ", DateUtil.FormateTimeToLong(data.getSHSJ().toString()));
            } else {
                jsonObject.remove("SHSJ");
            }
            if (data.getTJSJ() != null) {
                jsonObject.put("TJSJ", DateUtil.FormateTimeToLong(data.getTJSJ().toString()));
            } else {
                jsonObject.remove("TJSJ");
            }
            if (data.getXGSJ() != null) {
                jsonObject.put("XGSJ", DateUtil.FormateTimeToLong(data.getXGSJ().toString()));
            } else {
                jsonObject.remove("XGSJ");
            }
            if (data.getSWITCHTIME() != null) {
                jsonObject.put("SWITCHTIME", DateUtil.FormateTimeToLong(data.getSWITCHTIME().toString()));
            } else {
                jsonObject.remove("SWITCHTIME");
            }
            if (data.getNSYXQ() != null) {
                jsonObject.put("NSYXQ", DateUtil.FormateTimeToLong(data.getNSYXQ().toString()));
            } else {
                jsonObject.remove("NSYXQ");
            }
            String dataToJsonStr = new Gson().toJson(jsonObject);
            String Content = DesEncryptor.encrypt(dataToJsonStr, pros.getProperty("outintertfaceKey"));
            map.put("Content", Content);
            Message = "操作成功";
            State = "1";
        } catch (Exception e) {
            Message = e.getMessage();
            System.out.println(Message);
        }
        map.put("State", State);
        map.put("Message", Message);
        return map;
    }

    /**
     * 获取道路模板列表
     *
     * @param strParam
     * @return
     * @throws Exception
     */
    @PostMapping("/fndByRoad")
    @ResponseBody
    public Object fndByRoad(@RequestBody String strParam) throws Exception {
        JSONObject jsonParam = JSONObject.parseObject(DesDncrypt(strParam));
        Map<String, Object> map = new HashMap<String, Object>();
        String Message = "";
        String State = "";
        PageData pd = new PageData();
        pd = this.getPageData();

        String name = null;
        if (jsonParam != null) {
            name = jsonParam.getString("NAME");
        }
        List<Road> list = roadInfoService.fndByRoad(name);

        if (list.size() == 0) {
            Message = "操作失败";
            State = "0";
        } else {
            String dataToJsonStr = new Gson().toJson(list);

            String Content = DesEncryptor.encrypt(dataToJsonStr, pros.getProperty("outintertfaceKey"));
            map.put("Content", Content);
            Message = "操作成功";
            State = "1";
        }

        map.put("State", State);
        map.put("Message", Message);
        return map;

    }
//
    public org.json.JSONObject getSIX(String hpzl, String hphm, String count) throws Exception {
        org.json.JSONObject mapInfo = new org.json.JSONObject();
        org.json.JSONObject a = new org.json.JSONObject();
        List BackInfoList = new ArrayList();
        mapInfo.put("xtlb", count);
        mapInfo.put("hpzl", hpzl);
        mapInfo.put("hphm", hphm);
        if (count.equals("04")) {
            //添加需要的参数列表
            mapInfo.put("clbj", "0");
            BackInfoList.add("clbj");
            BackInfoList.add("wfsj");
            BackInfoList.add("wfdd");
            BackInfoList.add("wfxw");
        } else {
            mapInfo.put("dabh", "");
            BackInfoList.add("clsbdh");
            BackInfoList.add("fdjh");
            BackInfoList.add("qzbfqz");
            BackInfoList.add("cllx");
            BackInfoList.add("syxz");
            BackInfoList.add("syr");
            BackInfoList.add("zt");
            BackInfoList.add("yxqz");
        }
        try {
            a = sixInOne.getInfo(mapInfo, BackInfoList);
            FHLOG.save(IpUtils.getIpAddr(IpUtils.getRequestAttributes().getRequest()), hphm + "六合一请求结果:" + a);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        return a;
    }


    /**
     * 通行证状态
     */
    @PostMapping("/trafficStatus")
    @ResponseBody
    public Object trafficStatus(@RequestBody String strParam) throws Exception {
        JSONObject jsonObject = JSONObject.parseObject(DesDncrypt(strParam));
        Map<String, Object> map = new HashMap<String, Object>();
        JSONObject Content = new JSONObject();
        String enable = "可办理";
        String bz = "0";
        String Message = "操作成功";
        String State = "1";
        String hphm = jsonObject.getString("hphm");
        String hpzl = jsonObject.getString("hpzl");
        String lstxz = jsonObject.getString("lx");
        Content.put("cllx", "外省");
        if (hphm.contains("桂A")) {
            new org.json.JSONObject();
            org.json.JSONObject b = this.getSIX(hpzl, hphm, "01");

            try {
                if (b.length() > 0) {
                    if (b.getString("cllx") != null || b.getString("cllx") != "") {
                        Content.put("cllx", b.getString("cllx"));
                    }

                    if (ZtUtil.getZTInfo(b.getString("zt")) != "正常") {
                        bz = "1";
                        enable = "不可办理, " + ZtUtil.getZTInfo(b.getString("zt"));
                        Message = "操作成功";
                        State = "1";
                        Content.put("enable", enable);
                        Content.put("bz", bz);
                        map.put("Message", Message);
                        map.put("State", State);
                        map.put("Content", DesEncryptor.encrypt(Content.toString(), pros.getProperty("outintertfaceKey")));
                        return map;
                    }
                }
            } catch (Exception e) {
                System.out.println(e.getMessage());
            }
        }

        String pageData = this.blacklistService.fandByHPId(hphm, hpzl);
        if (pageData != null) {
            bz = "4";
            enable = "不可办理";
            Message = "操作成功";
            State = "1";
            Content.put("enable", enable);
            Content.put("bz", bz);
            map.put("Message", Message);
            map.put("State", State);
            map.put("Content", DesEncryptor.encrypt(Content.toString(), pros.getProperty("outintertfaceKey")));
            return map;
        }

        if (lstxz.equals("0")) {
            List<Map<String, Object>> objectMap = blacklistService.highstate(hphm, hpzl, lstxz);
            if (objectMap != null && objectMap.size() > 0) {
                for (Map<String, Object> stringObjectMap : objectMap) {
                    String yxrqz = (String) stringObjectMap.get("YXRQZ").toString();
                    String zx = (String) stringObjectMap.get("ZX").toString();
                    String time = DateUtil.getTime();
                    boolean V = DateUtil.compareDate(yxrqz, time);
                    if (zx.equals("1")) {
                        Content.put("enable", enable);
                        Content.put("bz", bz);
                        map.put("Message", Message);
                        map.put("State", State);
                        map.put("Content", DesEncryptor.encrypt(Content.toString(), pros.getProperty("outintertfaceKey")));
                    }
                    if (!V) {
                        Content.put("enable", enable);
                        Content.put("bz", bz);
                        map.put("Message", Message);
                        map.put("State", State);
                        map.put("Content", DesEncryptor.encrypt(Content.toString(), pros.getProperty("outintertfaceKey")));
                        return map;
                    } else if (DateUtil.getDaySub(time, yxrqz) > 20L) {
                        bz = "2";
                        enable = "不可办理";
                        Message = "操作成功";
                        State = "1";
                        Content.put("enable", enable);
                        Content.put("bz", bz);
                        map.put("Message", Message);
                        map.put("State", State);
                        map.put("Content", DesEncryptor.encrypt(Content.toString(), pros.getProperty("outintertfaceKey")));
                        return map;
                    }
                }
            }
        }
//        //TODO 查询6合一接口校验信息
//        org.json.JSONObject a = new org.json.JSONObject();
//        a = getSIX(hpzl, hphm,"04");
//        //校验信息
//        try {
//            if (a.length() > 0) {
//                if (a.getString("wfxw") != null || a.getString("wfxw") != "") {
//                    bz = "1";
//                    enable = "不可办理";
//                    Message = "操作成功";
//                    State = "1";
//                    Content.put("enable", enable);
//                    Content.put("bz", bz);
//                    map.put("Message", Message);
//                    map.put("State", State);
//                    map.put("Content", DesEncryptor.encrypt(Content.toString(), pros.getProperty("outintertfaceKey")));
//                    return map;
//                }
//            }
//        } catch (Exception e) {
//            System.out.println(e.getMessage());
//        }
//        //查询结束

        List<Map<String, Object>> resmap = paslicenseService.trafficStatus(hphm, hpzl, lstxz);
        for (Map<String, Object> stringObjectMap : resmap) {
            if (stringObjectMap != null && stringObjectMap.size() > 0 && stringObjectMap.get("zs") != null) {
                Integer zs = (Integer)stringObjectMap.get("zs");
                if (zs > 6) {
                    bz = "3";
                    enable = "不可办理";
                    Message = "操作成功";
                    State = "1";
                    Content.put("enable", enable);
                    Content.put("bz", bz);
                    map.put("Message", Message);
                    map.put("State", State);
                    map.put("Content", DesEncryptor.encrypt(Content.toString(), pros.getProperty("outintertfaceKey")));
                    return map;
                }
            }
        }

        if (Content.getString("cllx").equals("该号牌未查询出车辆类型信息")) {
            bz = "5";
            enable = "不可办理";
            Message = "操作成功";
            State = "1";
            Content.put("enable", enable);
            Content.put("bz", bz);
            map.put("Message", Message);
            map.put("State", State);
            map.put("Content", DesEncryptor.encrypt(Content.toString(), pros.getProperty("outintertfaceKey")));
            return map;
        } else {
            Content.put("enable", enable);
            Content.put("bz", bz);
            map.put("Message", Message);
            map.put("State", State);
            map.put("Content", DesEncryptor.encrypt(Content.toString(), pros.getProperty("outintertfaceKey")));
            return map;
        }
    }

    /**
     * 获取IP
     */
    @GetMapping("/GetIP")
    public static Object GetIP() throws Exception {
        String IP = IpUtils.getIpAddr(IpUtils.getRequestAttributes().getRequest());
        return IP;
    }

    /**
     * 获取道路选择条数统计    由于线上数据库单表数据超百万，一次性查再处理数据会OOM 采用多线程分批查表
     */
    @PostMapping("/GetRoadRow")
    public Object GetRoadRow(String ysqs, String yxqz, String jbr) throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        //Callable用于产生结果
        List<Callable<Map>> tasks = new ArrayList<>();
        Map<String, Object> finalMap = new LinkedHashMap<>();
        //查询数据库总数量
        int num = 6000;//一次查询多少条

        //查询数据库总数量
        int count = txzMapper.getAllCount();
        //需要查询的次数
        int times = count / num;
        if (count % num != 0) {
            times = times + 1;
        }
        //开始页数  连接的是orcle的数据库  封装的分页方式
        int bindex = 1;
        for (int i = 0; i < times; i++) {
            Callable<Map> qfe = new ThredQuery(bindex, num, ysqs, yxqz, jbr);
            tasks.add(qfe);
            bindex++;
        }
        //定义固定长度的线程池  防止线程过多
        ExecutorService executorService = Executors.newFixedThreadPool(15);
        //Future用于获取结果
        List<Future<Map>> futures = executorService.invokeAll(tasks);
        //处理线程返回结果
        if (futures != null && futures.size() > 0) {
            for (Future<Map> future : futures) {
                //通过迭代器
                Iterator<Map.Entry<String, Object>> iterator = future.get().entrySet().iterator();
                while (iterator.hasNext()) {
                    Map.Entry<String, Object> entry = iterator.next();
                    //判断大字典是否含有key，有则增加vlue
                    if (finalMap.containsKey(entry.getKey())) {
                        finalMap.put(entry.getKey(), Integer.parseInt(entry.getValue().toString()) + Integer.parseInt(finalMap.get(entry.getKey()).toString()));
                    } else {
                        finalMap.put(entry.getKey(), Integer.parseInt(entry.getValue().toString()));
                    }
                }
            }
        }
        executorService.shutdown();//关闭线程池
        System.out.println(finalMap);
        map.put("asd", "1");
        map.put("roadList", finalMap);
        map.put("errInfo", errInfo);
        return map;
    }


    /**
     * 警务通通行证查询接口
     */
    @RequestMapping(value = "/GetPaslicense")
    @ResponseBody
    public Map<String, Object> GetPaslicense(@RequestBody String strParam) throws Exception {
        JSONObject jsonObject = JSONObject.parseObject(DesDncrypt(strParam));
        Map<String, Object> map = new HashMap<String, Object>();
        String Message = "";
        String State = "";

        PASLICENSEWithBLOBs record = new PASLICENSEWithBLOBs();

        String HPZL = jsonObject.getString("HPZL");
        if (Tools.notEmpty(HPZL)) {
            record.setHpzl(jsonObject.getString("HPZL"));
        }

        String HPHM = jsonObject.getString("HPHM");
        if (Tools.notEmpty(HPHM)) {
            record.setHphm(jsonObject.getString("HPHM"));
        }

        String TXZHM = jsonObject.getString("TXZHM");
        if (Tools.notEmpty(TXZHM)) {
            record.setTxzhm(jsonObject.getString("TXZHM"));
        }

        List<PASLICENSEWithBLOBs> varList = appletsService.selectByIdHphm(record);

        for (int i = 0; i < varList.size(); i++) {
            String a = varList.get(i).getHphm();
            String b = varList.get(i).getHpzl();
            String c = varList.get(i).getTxzhm();
            FHLOG.save(IpUtils.getIpAddr(IpUtils.getRequestAttributes().getRequest()),
                    "用户：" + jsonObject.getString("USERNAME") + "==>" + "查询了[" + "号牌号码：" + a + "，号牌种类：" + b + "，通行证号码：" + c + "]的通行证信息");
        }
        String Content = DesEncryptor.encrypt(JSONObject.toJSON(varList).toString(), pros.getProperty("outintertfaceKey"));

        map.put("Content", Content);
        if (varList.size() == 0) {
            Message = "经核查,该车牌号码无有效车辆通行证";
            State = "0";
        } else {
            Message = "查询成功";
            State = "1";
        }
        map.put("State", State);
        map.put("Message", Message);
        return map;
    }
    @RequestMapping({"/GetEffectiveList"})
    @ResponseBody
    public Map GetEffectiveList(@RequestBody String strParam) throws Exception {
        JSONObject jsonParam = JSONObject.parseObject(this.DesDncrypt(strParam));
        Map map = new HashMap();
        String Message = "";
        String State = "";
        PASLICENSEWithBLOBs paslicenseWithBLOBs = new PASLICENSEWithBLOBs();
        String HPHM = jsonParam.getString("HPHM");
        paslicenseWithBLOBs.setHphm(HPHM);
        String HPZL = jsonParam.getString("HPZL");
        if (Tools.notEmpty(HPZL)) {
            paslicenseWithBLOBs.setHpzl(HPZL);
        }

        try {
            List varList = this.appletsService.selectListIdCar(paslicenseWithBLOBs);
            String Content = DesEncryptor.encrypt(JSONObject.toJSON(varList).toString(), pros.getProperty("outintertfaceKey"));
            map.put("Content", Content);
            if (varList.size() == 0) {
                Message = "经核查,该车牌号码无有效车辆通行证";
                State = "2";
            } else {
                Message = "操作成功";
                State = "1";
            }
        } catch (Exception var11) {
            Exception e = var11;
            Message = e.getMessage();
            State = "0";
            e.printStackTrace();
        }

        map.put("State", State);
        map.put("Message", Message);
        return map;
    }

}
