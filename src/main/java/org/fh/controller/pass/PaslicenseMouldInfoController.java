package org.fh.controller.pass;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.fh.controller.base.BaseController;
import org.fh.entity.Page;
import org.fh.entity.PageData;
import org.fh.service.pass.PaslicenseMouldInfoService;
import org.fh.service.system.FHlogService;
import org.fh.util.Jurisdiction;
import org.fh.util.ObjectExcelView;
import org.fh.util.Tools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import java.util.*;

/**
 * 说明：模板和路段信息关联模块--模板明细
 * 作者：FH Admin QQ313596790
 * 时间：2020-06-13
 * 官网：www.fhadmin.org
 */
@Controller
@RequestMapping("/paslicensemouldinfo")
public class PaslicenseMouldInfoController extends BaseController {

    @Autowired
    private PaslicenseMouldInfoService paslicensemouldinfoService;
    @Autowired
    private RoadInfoController roadInfoController;
    @Autowired
    private FHlogService FHLOG;

    /**
     * 保存
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/add")
//	@RequiresPermissions("paslicensemouldinfo:add")
    @ResponseBody
    public Object add() throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        String id=  this.get32UUID();
        String roadID = (String) pd.get("RoadID");
        String MBID = (String) pd.get("MBID");
        pd.put("ROADPARTID", roadID);
        pd.put("ID",id);    //主键
        List<String> rode =paslicensemouldinfoService.getById(roadID,MBID);
        if (rode.size()==0){
            paslicensemouldinfoService.save(pd);
            FHLOG.save(Jurisdiction.getUsername(), "模板管理-->添加模板明细-->添加成功.id:"+id);				//记录日志

        }else{
            errInfo = "道路已存在模板";
        }
        map.put("result", errInfo);
        return map;
    }

    /**
     * 删除
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/delete")
    @RequiresPermissions("paslicensemouldinfo:del")
    @ResponseBody
    public Object delete() throws Exception {
        Map<String, String> map = new HashMap<String, String>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        paslicensemouldinfoService.delete(pd);
        FHLOG.save(Jurisdiction.getUsername(), "模板管理-->添加模板明细-->删除成功.id:"+pd.getString("ID"));				//记录日志

        map.put("result", errInfo);                //返回结果
        return map;
    }

    /**
     * 修改
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/edit")
    @RequiresPermissions("paslicensemouldinfo:edit")
    @ResponseBody
    public Object edit() throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();
        paslicensemouldinfoService.edit(pd);
        FHLOG.save(Jurisdiction.getUsername(), "模板管理-->添加模板明细-->修改成功.id:"+pd.getString("ID"));				//记录日志

        map.put("result", errInfo);
        return map;
    }


    /**
     * 根据模板ID获取路段id列表
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/list")
//    @RequiresPermissions("paslicensemouldinfo:list")
    @ResponseBody
    public Object getRoadIDListByMBID(Page page) throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        PageData pd = new PageData();
        String errInfo = "success";
        pd = this.getPageData();

        String KEYWORDS = pd.getString("KEYWORDS");
        if(Tools.notEmpty(KEYWORDS)){
            pd.put("KEYWORDS", KEYWORDS.trim().toLowerCase());
        }
        String DLJBKEYWORDS = pd.getString("DLJBKEYWORDS");//关键词检索条件--道路等级
        if (Tools.notEmpty(DLJBKEYWORDS)) {
            pd.put("DLJBKEYWORDS", DLJBKEYWORDS.trim());
        }

        List<String> varList = new ArrayList<>();
        String mbid = pd.getString("MBID");
        if (Tools.notEmpty(mbid)) {
            varList = paslicensemouldinfoService.findByMBId(mbid.trim());    //根据模板ID读取路段ID列表
        }

        String msg = pd.getString("msg");
        if (Tools.notEmpty(msg)) {
            if (null != varList && varList.size() > 0) {
                pd.put("ids",varList);
            } else {
                page.setTotalResult(0);
                map.put("page", page);
                map.put("varList", varList);
                map.put("result", errInfo);
                return map;
            }
        }
        if (Tools.isEmpty(msg)){
            if (null != varList && varList.size() > 0) {
                pd.put("unboundIds",varList);
            }
        }
        page.setPd(pd);
        Object o = roadInfoController.datalistPageByRoadIDList(page);
        return o;
    }


    /**
     * 批量添加
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/deleteAll")
//    @RequiresPermissions("paslicensemouldinfo:del")
    @ResponseBody
    public Object deleteAll() throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String errInfo = "success";
        PageData pd = new PageData();
        pd = this.getPageData();

        String MBID = pd.getString("MBID");
        if (Tools.notEmpty(MBID)) {
            MBID = MBID.trim();
        }

        List<PageData> varlist = new ArrayList<>();
        String DATA_IDS = pd.getString("DATA_IDS");
        if (Tools.notEmpty(DATA_IDS)) {
            String ArrayDATA_IDS[] = DATA_IDS.split(",");
            for (String roadID : ArrayDATA_IDS) {
                PageData pds = new PageData();
                pds.put("ID", this.get32UUID());
                pds.put("MBID", MBID);
                pds.put("ROADPARTID", roadID);
                varlist.add(pds);
            }
            paslicensemouldinfoService.addBatch(varlist);
            FHLOG.save(Jurisdiction.getUsername(), "模板管理-->添加模板明细-->批量新增成功.id:"+pd.getString("DATA_IDS"));				//记录日志


        }
        map.put("result", errInfo);
        return map;
    }

    /**
     * 导出到excel
     *
     * @param
     * @throws Exception
     */
    @RequestMapping(value = "/excel")
    @RequiresPermissions("toExcel")
    public ModelAndView exportExcel() throws Exception {
        ModelAndView mv = new ModelAndView();
        PageData pd = new PageData();
        pd = this.getPageData();
        Map<String, Object> dataMap = new HashMap<String, Object>();
        List<String> titles = new ArrayList<String>();
        titles.add("id");    //1
        titles.add("模板ID");    //2
        titles.add("路段信息id");    //3
        dataMap.put("titles", titles);
        List<PageData> varOList = paslicensemouldinfoService.listAll(pd);
        List<PageData> varList = new ArrayList<PageData>();
        for (int i = 0; i < varOList.size(); i++) {
            PageData vpd = new PageData();
            vpd.put("var1", varOList.get(i).getString("ID"));        //1
            vpd.put("var2", varOList.get(i).getString("MBID"));        //2
            vpd.put("var3", varOList.get(i).getString("ROADPARTID"));        //3
            varList.add(vpd);
        }
        dataMap.put("varList", varList);
        ObjectExcelView erv = new ObjectExcelView();
        mv = new ModelAndView(erv, dataMap);
        FHLOG.save(Jurisdiction.getUsername(), "当前用户:"+Jurisdiction.getUsername()+"模板明细-->导出表格");				//记录日志
        return mv;
    }

}
