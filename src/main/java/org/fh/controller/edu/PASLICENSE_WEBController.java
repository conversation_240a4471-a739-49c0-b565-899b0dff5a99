package org.fh.controller.edu;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.apache.shiro.authz.annotation.RequiresPermissions;

import org.fh.controller.base.BaseController;
import org.fh.entity.Page;
import org.fh.util.DateUtil;
import org.fh.util.ObjectExcelView;
import org.fh.util.Tools;
import org.fh.entity.PageData;
import org.fh.service.edu.PASLICENSE_WEBService;

/** 
 * 说明：PASLICENSE_WEB
 * 作者：FH Admin QQ313596790
 * 时间：2020-05-22
 * 官网：www.fhadmin.org
 */
@Controller
@RequestMapping("/paslicense_web")
public class PASLICENSE_WEBController extends BaseController {
	
	@Autowired
	private PASLICENSE_WEBService paslicense_webService;
	
	/**保存
	 * @param
	 * @throws Exception
	 */
	@RequestMapping(value="/add")
	@RequiresPermissions("paslicense_web:add")
	@ResponseBody
	public Object add() throws Exception{
		Map<String,Object> map = new HashMap<String,Object>();
		String errInfo = "success";
		PageData pd = new PageData();
		pd = this.getPageData();
		pd.put("PASLICENSE_WEB_ID", this.get32UUID());	//主键
		paslicense_webService.save(pd);
		map.put("result", errInfo);
		return map;
	}
	
	/**删除
	 * @param out
	 * @throws Exception
	 */
	@RequestMapping(value="/delete")
	@RequiresPermissions("paslicense_web:del")
	@ResponseBody
	public Object delete() throws Exception{
		Map<String,String> map = new HashMap<String,String>();
		String errInfo = "success";
		PageData pd = new PageData();
		pd = this.getPageData();
		paslicense_webService.delete(pd);
		map.put("result", errInfo);				//返回结果
		return map;
	}
	
	/**修改
	 * @param
	 * @throws Exception
	 */
	@RequestMapping(value="/edit")
	@RequiresPermissions("paslicense_web:edit")
	@ResponseBody
	public Object edit() throws Exception{
		Map<String,Object> map = new HashMap<String,Object>();
		String errInfo = "success";
		PageData pd = new PageData();
		pd = this.getPageData();
		paslicense_webService.edit(pd);
		map.put("result", errInfo);
		return map;
	}
	
	/**列表
	 * @param page
	 * @throws Exception
	 */
	@RequestMapping(value="/list")
	@RequiresPermissions("paslicense_web:list")
	@ResponseBody
	public Object list(Page page) throws Exception{
		Map<String,Object> map = new HashMap<String,Object>();
		String errInfo = "success";
		PageData pd = new PageData();
		pd = this.getPageData();
		String KEYWORDS = pd.getString("KEYWORDS");						//关键词检索条件
		if(Tools.notEmpty(KEYWORDS))pd.put("KEYWORDS", KEYWORDS.trim());
		page.setPd(pd);
		List<PageData>	varList = paslicense_webService.list(page);	//列出PASLICENSE_WEB列表
		map.put("varList", varList);
		map.put("page", page);
		map.put("result", errInfo);
		return map;
	}
	
	 /**去修改页面获取数据
	 * @param
	 * @throws Exception
	 */
	@RequestMapping(value="/goEdit")
	@RequiresPermissions("paslicense_web:edit")
	@ResponseBody
	public Object goEdit() throws Exception{
		Map<String,Object> map = new HashMap<String,Object>();
		String errInfo = "success";
		PageData pd = new PageData();
		pd = this.getPageData();
		pd = paslicense_webService.findById(pd);	//根据ID读取
		map.put("pd", pd);
		map.put("result", errInfo);
		return map;
	}	
	
	 /**批量删除
	 * @param
	 * @throws Exception
	 */
	@RequestMapping(value="/deleteAll")
	@RequiresPermissions("paslicense_web:del")
	@ResponseBody
	public Object deleteAll() throws Exception{
		Map<String,Object> map = new HashMap<String,Object>();
		String errInfo = "success";
		PageData pd = new PageData();		
		pd = this.getPageData();
		String DATA_IDS = pd.getString("DATA_IDS");
		if(Tools.notEmpty(DATA_IDS)){
			String ArrayDATA_IDS[] = DATA_IDS.split(",");
			paslicense_webService.deleteAll(ArrayDATA_IDS);
			errInfo = "success";
		}else{
			errInfo = "error";
		}
		map.put("result", errInfo);				//返回结果
		return map;
	}
	
	 /**导出到excel
	 * @param
	 * @throws Exception
	 */
	@RequestMapping(value="/excel")
	@RequiresPermissions("toExcel")
	public ModelAndView exportExcel() throws Exception{
		ModelAndView mv = new ModelAndView();
		PageData pd = new PageData();
		pd = this.getPageData();
		Map<String,Object> dataMap = new HashMap<String,Object>();
		List<String> titles = new ArrayList<String>();
		titles.add("序号");	//1
		titles.add("通行证类型");	//2
		titles.add("通行证号码");	//3
		titles.add("号牌种类");	//4
		titles.add("号牌号码");	//5
		titles.add("车辆类型");	//6
		titles.add("机动车所有人");	//7
		titles.add("路线");	//8
		titles.add("有期日期起");	//9
		titles.add("有期日期起");	//10
		titles.add("经办人");	//11
		titles.add("更新时间");	//12
		titles.add("登记日期");	//13
		titles.add("审批人");	//14
		titles.add("审批标志(1审批通过 0未审批 2审批不通过)");	//15
		titles.add("审批日期");	//16
		titles.add("高峰时段");	//17
		titles.add("有效时间");	//18
		titles.add("有效日期");	//19
		titles.add("路段加注");	//20
		titles.add("备注");	//21
		titles.add("打印标志 0-未打印  1-已打印");	//22
		titles.add("通行证状态（1可用0删除）");	//23
		titles.add("预留字段 手机号码");	//24
		titles.add("预留字段");	//25
		titles.add("预留字段 2019新版道路id 使用电警平台数据 no");	//26
		titles.add("预留字段 2019新版 禁行道路代码");	//27
		titles.add("是否注销 0-未注销  1-已注销");	//28
		titles.add("是否拒绝 0-未拒绝  1-已拒绝");	//29
		titles.add("拒绝原因");	//30
		titles.add("是否一级审批 0-null 1-已审批  2-待审批");	//31
		titles.add("一级审批人");	//32
		titles.add("是否二级审批 0-null 1-已审批  2-待审批");	//33
		titles.add("二级审批人");	//34
		titles.add("是否三级审批 0-null 1-已审批  2-待审批");	//35
		titles.add("三级审批人");	//36
		titles.add("2代表不存在  1 2 3 级道路审批");	//37
		titles.add("1-是 0-否  临时审批");	//38
		titles.add("1-是 0-否  临时审批是否");	//39
		titles.add("设备时段");	//40
		titles.add("来源");	//41
		titles.add("原始设备时段");	//42
		titles.add("临时通行证(1临时 0长期)");	//43
		titles.add("通行设备编号");	//44
		titles.add("道路级别");	//45
		titles.add("通行或禁行 1通行 2禁行");	//46
		titles.add("备注47");	//47
		titles.add("备注48");	//48
		titles.add("选择设备id");	//49
		titles.add("选择设备名称");	//50
		dataMap.put("titles", titles);
		List<PageData> varOList = paslicense_webService.listAll(pd);
		List<PageData> varList = new ArrayList<PageData>();
		for(int i=0;i<varOList.size();i++){
			PageData vpd = new PageData();
			vpd.put("var1", varOList.get(i).getString("XH"));	    //1
			vpd.put("var2", varOList.get(i).getString("TXZLX"));	    //2
			vpd.put("var3", varOList.get(i).getString("TXZHM"));	    //3
			vpd.put("var4", varOList.get(i).getString("HPZL"));	    //4
			vpd.put("var5", varOList.get(i).getString("HPHM"));	    //5
			vpd.put("var6", varOList.get(i).getString("CLLX"));	    //6
			vpd.put("var7", varOList.get(i).getString("SYR"));	    //7
			vpd.put("var8", varOList.get(i).getString("LX"));	    //8
			vpd.put("var9", varOList.get(i).getString("YXRQS"));	    //9
			vpd.put("var10", varOList.get(i).getString("YXRQZ"));	    //10
			vpd.put("var11", varOList.get(i).getString("JBR"));	    //11
			vpd.put("var12", varOList.get(i).getString("GXSJ"));	    //12
			vpd.put("var13", varOList.get(i).getString("DJRQ"));	    //13
			vpd.put("var14", varOList.get(i).getString("SPR"));	    //14
			vpd.put("var15", varOList.get(i).getString("SPBJ"));	    //15
			vpd.put("var16", varOList.get(i).getString("SPRQ"));	    //16
			vpd.put("var17", varOList.get(i).getString("GFSD"));	    //17
			vpd.put("var18", varOList.get(i).getString("YXSJ"));	    //18
			vpd.put("var19", varOList.get(i).getString("YXRQ"));	    //19
			vpd.put("var20", varOList.get(i).getString("LDJZ"));	    //20
			vpd.put("var21", varOList.get(i).getString("BZ"));	    //21
			vpd.put("var22", varOList.get(i).getString("DYBJ"));	    //22
			vpd.put("var23", varOList.get(i).getString("ZT"));	    //23
			vpd.put("var24", varOList.get(i).getString("YLZD1"));	    //24
			vpd.put("var25", varOList.get(i).getString("YLZD2"));	    //25
			vpd.put("var26", varOList.get(i).getString("YLZD3"));	    //26
			vpd.put("var27", varOList.get(i).getString("YLZD4"));	    //27
			vpd.put("var28", varOList.get(i).getString("ZX"));	    //28
			vpd.put("var29", varOList.get(i).getString("JUJUE"));	    //29
			vpd.put("var30", varOList.get(i).getString("JUETEXT"));	    //30
			vpd.put("var31", varOList.get(i).getString("SPONE"));	    //31
			vpd.put("var32", varOList.get(i).getString("SPONEPEOPLE"));	    //32
			vpd.put("var33", varOList.get(i).getString("SPTWO"));	    //33
			vpd.put("var34", varOList.get(i).getString("SPTWOPEOPLE"));	    //34
			vpd.put("var35", varOList.get(i).getString("SPTHREE"));	    //35
			vpd.put("var36", varOList.get(i).getString("SPTHREEPEOPLE"));	    //36
			vpd.put("var37", varOList.get(i).getString("SPBJ2"));	    //37
			vpd.put("var38", varOList.get(i).getString("LSSP"));	    //38
			vpd.put("var39", varOList.get(i).getString("LSSPBJ"));	    //39
			vpd.put("var40", varOList.get(i).getString("SBSD"));	    //40
			vpd.put("var41", varOList.get(i).getString("LY"));	    //41
			vpd.put("var42", varOList.get(i).getString("YSSBSD"));	    //42
			vpd.put("var43", varOList.get(i).getString("LSTXZ"));	    //43
			vpd.put("var44", varOList.get(i).getString("TXSBBH"));	    //44
			vpd.put("var45", varOList.get(i).getString("DLJB"));	    //45
			vpd.put("var46", varOList.get(i).getString("TXJX"));	    //46
			vpd.put("var47", varOList.get(i).getString("YGZ"));	    //47
			vpd.put("var48", varOList.get(i).getString("GZSJ"));	    //48
			vpd.put("var49", varOList.get(i).getString("YLZD5"));	    //49
			vpd.put("var50", varOList.get(i).getString("YLZD6"));	    //50
			varList.add(vpd);
		}
		dataMap.put("varList", varList);
		ObjectExcelView erv = new ObjectExcelView();
		mv = new ModelAndView(erv,dataMap);
		return mv;
	}
	
}
