package org.fh.entity.pass;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * PASLICENSE
 *
 * <AUTHOR>
@Data
public class PASLICENSE implements Serializable {
    /**
     * 序号
     */
    private String xh;

    /**
     * 通行证类型
     */
    private String txzlx;

    /**
     * 通行证号码
     */
    private String txzhm;

    /**
     * 号牌种类
     */
    private String hpzl;

    /**
     * 号牌号码
     */
    private String hphm;

    /**
     * 车辆类型
     */
    private String cllx;

    /**
     * 机动车所有人
     */
    private String syr;

    /**
     * 路线
     */
    private String lx;

    /**
     * 有期日期起
     */

    private Date yxrqs;

    /**
     * 有期日期止
     */

    private Date yxrqz;

    /**
     * 经办人
     */
    private String jbr;

    /**
     * 更新时间
     */
    private Date gxsj;

    /**
     * 登记日期
     */
    private Date djrq;

    /**
     * 审批人
     */
    private String spr;

    /**
     * 审批标志
     */
    private String spbj;

    /**
     * 审批日期
     */
    private Date sprq;

    /**
     * 高峰时段
     */
    private String gfsd;

    /**
     * 有效时间
     */
    private String yxsj;

    /**
     * 有效日期
     */
    private String yxrq;

    /**
     * 路段加注
     */
    private String ldjz;

    /**
     * 备注
     */
    private String bz;

    /**
     * 打印标志 0-未打印  1-已打印
     */
    private String dybj;

    /**
     * 通行证状态（1可用0删除）
     */
    private String zt;

    /**
     * 预留字段 手机号码
     */
    private String ylzd1;

    /**
     * 预留字段
     */
    private String ylzd2;

    /**
     * 预留字段 2019新版道路id 使用电警平台数据
     */
    private String ylzd3;

    /**
     * 预留字段 2019新版 禁行道路代码
     */
    private String ylzd4;

    /**
     * 是否注销 0-未注销  1-已注销
     */
    private String zx;

    /**
     * 是否拒绝 0-未拒绝  1-已拒绝
     */
    private String jujue;

    /**
     * 拒绝原因
     */
    private String juetext;

    /**
     * 是否一级审批 0-null 1-已审批  2-待审批
     */
    private String spone;

    /**
     * 一级审批人
     */
    private String sponepeople;

    /**
     * 是否二级审批 0-null 1-已审批  2-待审批
     */
    private String sptwo;

    /**
     * 二级审批人
     */
    private String sptwopeople;

    /**
     * 是否三级审批 0-null 1-已审批  2-待审批
     */
    private String spthree;

    /**
     * 三级审批人
     */
    private String spthreepeople;

    /**
     * 2代表不存在  1 2 3 级道路审批
     */
    private String spbj2;

    /**
     * 1-是 0-否  临时审批
     */
    private String lssp;

    /**
     * 1-是 0-否  临时审批是否
     */
    private String lsspbj;

    /**
     * 来源
     */
    private String ly;

    private String ylzd5;

    public String getYlzd5() {
        return ylzd5;
    }

    public void setYlzd5(String ylzd5) {
        this.ylzd5 = ylzd5;
    }

    public String getYlzd6() {
        return ylzd6;
    }

    public void setYlzd6(String ylzd6) {
        this.ylzd6 = ylzd6;
    }

    private String ylzd6;

    /**
     * 原始设备时段
     */
    private String yssbsd;

    /**
     * 临时通行证(1临时 0长期)
     */
    private String lstxz;

    /**
     * 道路级别
     */
    private String dljb;

    /**
     * 通行或禁行 1通行 2禁行
     */
    private String txjx;

    /**
     * 主副证 1主证 2副证
     */
    private String zfz;

    /**
     * 同类匹配，为空表示未检测
     */
    private String tlpp;

    /**
     * 是否已更新
     */
    private String sfygx;
    /**
     * 违法行为
     */
    private String wfxw;
    private String message;

    private String auditing_status;
    private static final long serialVersionUID = 1L;

}