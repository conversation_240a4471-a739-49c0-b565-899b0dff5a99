package org.fh.entity.pass;


import java.io.Serializable;

/**
 * PASLICENSE
 * <AUTHOR>
public class PASLICENSEWithBLOBs extends PASLICENSE implements Serializable {
    /**
     * 选择设备id
     */
    private String ylzd5;

    /**
     * 选择设备名称
     */
    private String ylzd6;

    /**
     * 通行设备编号
     */
    private String txsbbh;

    /**
     * 设备时段
     */
    private String sbsd;

    /**
     * 判断用户是否有权限更改
     */
    private Boolean permissions;

    private static final long serialVersionUID = 1L;

    public Boolean getPermissions(){return permissions;}
    public void setPermissions(Boolean permissions){this.permissions = permissions;}

    public String getYlzd5() {
        return ylzd5;
    }

    public void setYlzd5(String ylzd5) {
        this.ylzd5 = ylzd5;
    }

    public String getYlzd6() {
        return ylzd6;
    }

    public void setYlzd6(String ylzd6) {
        this.ylzd6 = ylzd6;
    }

    public String getTxsbbh() {
        return txsbbh;
    }

    public void setTxsbbh(String txsbbh) {
        this.txsbbh = txsbbh;
    }

    public String getSbsd() {
        return sbsd;
    }

    public void setSbsd(String sbsd) {
        this.sbsd = sbsd;
    }



    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        org.fh.entity.pass.PASLICENSEWithBLOBs other = (org.fh.entity.pass.PASLICENSEWithBLOBs) that;
        return (this.getXh() == null ? other.getXh() == null : this.getXh().equals(other.getXh()))
            && (this.getTxzlx() == null ? other.getTxzlx() == null : this.getTxzlx().equals(other.getTxzlx()))
            && (this.getTxzhm() == null ? other.getTxzhm() == null : this.getTxzhm().equals(other.getTxzhm()))
            && (this.getHpzl() == null ? other.getHpzl() == null : this.getHpzl().equals(other.getHpzl()))
            && (this.getHphm() == null ? other.getHphm() == null : this.getHphm().equals(other.getHphm()))
            && (this.getCllx() == null ? other.getCllx() == null : this.getCllx().equals(other.getCllx()))
            && (this.getSyr() == null ? other.getSyr() == null : this.getSyr().equals(other.getSyr()))
            && (this.getLx() == null ? other.getLx() == null : this.getLx().equals(other.getLx()))
            && (this.getYxrqs() == null ? other.getYxrqs() == null : this.getYxrqs().equals(other.getYxrqs()))
            && (this.getYxrqz() == null ? other.getYxrqz() == null : this.getYxrqz().equals(other.getYxrqz()))
            && (this.getJbr() == null ? other.getJbr() == null : this.getJbr().equals(other.getJbr()))
            && (this.getGxsj() == null ? other.getGxsj() == null : this.getGxsj().equals(other.getGxsj()))
            && (this.getDjrq() == null ? other.getDjrq() == null : this.getDjrq().equals(other.getDjrq()))
            && (this.getSpr() == null ? other.getSpr() == null : this.getSpr().equals(other.getSpr()))
            && (this.getSpbj() == null ? other.getSpbj() == null : this.getSpbj().equals(other.getSpbj()))
            && (this.getSprq() == null ? other.getSprq() == null : this.getSprq().equals(other.getSprq()))
            && (this.getGfsd() == null ? other.getGfsd() == null : this.getGfsd().equals(other.getGfsd()))
            && (this.getYxsj() == null ? other.getYxsj() == null : this.getYxsj().equals(other.getYxsj()))
            && (this.getYxrq() == null ? other.getYxrq() == null : this.getYxrq().equals(other.getYxrq()))
            && (this.getLdjz() == null ? other.getLdjz() == null : this.getLdjz().equals(other.getLdjz()))
            && (this.getBz() == null ? other.getBz() == null : this.getBz().equals(other.getBz()))
            && (this.getDybj() == null ? other.getDybj() == null : this.getDybj().equals(other.getDybj()))
            && (this.getZt() == null ? other.getZt() == null : this.getZt().equals(other.getZt()))
            && (this.getYlzd1() == null ? other.getYlzd1() == null : this.getYlzd1().equals(other.getYlzd1()))
            && (this.getYlzd2() == null ? other.getYlzd2() == null : this.getYlzd2().equals(other.getYlzd2()))
            && (this.getYlzd3() == null ? other.getYlzd3() == null : this.getYlzd3().equals(other.getYlzd3()))
            && (this.getYlzd4() == null ? other.getYlzd4() == null : this.getYlzd4().equals(other.getYlzd4()))
            && (this.getZx() == null ? other.getZx() == null : this.getZx().equals(other.getZx()))
            && (this.getJujue() == null ? other.getJujue() == null : this.getJujue().equals(other.getJujue()))
            && (this.getJuetext() == null ? other.getJuetext() == null : this.getJuetext().equals(other.getJuetext()))
            && (this.getSpone() == null ? other.getSpone() == null : this.getSpone().equals(other.getSpone()))
            && (this.getSponepeople() == null ? other.getSponepeople() == null : this.getSponepeople().equals(other.getSponepeople()))
            && (this.getSptwo() == null ? other.getSptwo() == null : this.getSptwo().equals(other.getSptwo()))
            && (this.getSptwopeople() == null ? other.getSptwopeople() == null : this.getSptwopeople().equals(other.getSptwopeople()))
            && (this.getSpthree() == null ? other.getSpthree() == null : this.getSpthree().equals(other.getSpthree()))
            && (this.getSpthreepeople() == null ? other.getSpthreepeople() == null : this.getSpthreepeople().equals(other.getSpthreepeople()))
            && (this.getSpbj2() == null ? other.getSpbj2() == null : this.getSpbj2().equals(other.getSpbj2()))
            && (this.getLssp() == null ? other.getLssp() == null : this.getLssp().equals(other.getLssp()))
            && (this.getLsspbj() == null ? other.getLsspbj() == null : this.getLsspbj().equals(other.getLsspbj()))
            && (this.getLy() == null ? other.getLy() == null : this.getLy().equals(other.getLy()))
            && (this.getYssbsd() == null ? other.getYssbsd() == null : this.getYssbsd().equals(other.getYssbsd()))
            && (this.getLstxz() == null ? other.getLstxz() == null : this.getLstxz().equals(other.getLstxz()))
            && (this.getDljb() == null ? other.getDljb() == null : this.getDljb().equals(other.getDljb()))
            && (this.getTxjx() == null ? other.getTxjx() == null : this.getTxjx().equals(other.getTxjx()))
            && (this.getZfz() == null ? other.getZfz() == null : this.getZfz().equals(other.getZfz()))
            && (this.getTlpp() == null ? other.getTlpp() == null : this.getTlpp().equals(other.getTlpp()))
            && (this.getSfygx() == null ? other.getSfygx() == null : this.getSfygx().equals(other.getSfygx()))
            && (this.getYlzd5() == null ? other.getYlzd5() == null : this.getYlzd5().equals(other.getYlzd5()))
            && (this.getYlzd6() == null ? other.getYlzd6() == null : this.getYlzd6().equals(other.getYlzd6()))
            && (this.getTxsbbh() == null ? other.getTxsbbh() == null : this.getTxsbbh().equals(other.getTxsbbh()))
            && (this.getSbsd() == null ? other.getSbsd() == null : this.getSbsd().equals(other.getSbsd()))
            && (this.getAuditing_status() == null ? other.getAuditing_status() == null : this.getAuditing_status().equals(other.getAuditing_status()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getXh() == null) ? 0 : getXh().hashCode());
        result = prime * result + ((getTxzlx() == null) ? 0 : getTxzlx().hashCode());
        result = prime * result + ((getTxzhm() == null) ? 0 : getTxzhm().hashCode());
        result = prime * result + ((getHpzl() == null) ? 0 : getHpzl().hashCode());
        result = prime * result + ((getHphm() == null) ? 0 : getHphm().hashCode());
        result = prime * result + ((getCllx() == null) ? 0 : getCllx().hashCode());
        result = prime * result + ((getSyr() == null) ? 0 : getSyr().hashCode());
        result = prime * result + ((getLx() == null) ? 0 : getLx().hashCode());
        result = prime * result + ((getYxrqs() == null) ? 0 : getYxrqs().hashCode());
        result = prime * result + ((getYxrqz() == null) ? 0 : getYxrqz().hashCode());
        result = prime * result + ((getJbr() == null) ? 0 : getJbr().hashCode());
        result = prime * result + ((getGxsj() == null) ? 0 : getGxsj().hashCode());
        result = prime * result + ((getDjrq() == null) ? 0 : getDjrq().hashCode());
        result = prime * result + ((getSpr() == null) ? 0 : getSpr().hashCode());
        result = prime * result + ((getSpbj() == null) ? 0 : getSpbj().hashCode());
        result = prime * result + ((getSprq() == null) ? 0 : getSprq().hashCode());
        result = prime * result + ((getGfsd() == null) ? 0 : getGfsd().hashCode());
        result = prime * result + ((getYxsj() == null) ? 0 : getYxsj().hashCode());
        result = prime * result + ((getYxrq() == null) ? 0 : getYxrq().hashCode());
        result = prime * result + ((getLdjz() == null) ? 0 : getLdjz().hashCode());
        result = prime * result + ((getBz() == null) ? 0 : getBz().hashCode());
        result = prime * result + ((getDybj() == null) ? 0 : getDybj().hashCode());
        result = prime * result + ((getZt() == null) ? 0 : getZt().hashCode());
        result = prime * result + ((getYlzd1() == null) ? 0 : getYlzd1().hashCode());
        result = prime * result + ((getYlzd2() == null) ? 0 : getYlzd2().hashCode());
        result = prime * result + ((getYlzd3() == null) ? 0 : getYlzd3().hashCode());
        result = prime * result + ((getYlzd4() == null) ? 0 : getYlzd4().hashCode());
        result = prime * result + ((getZx() == null) ? 0 : getZx().hashCode());
        result = prime * result + ((getJujue() == null) ? 0 : getJujue().hashCode());
        result = prime * result + ((getJuetext() == null) ? 0 : getJuetext().hashCode());
        result = prime * result + ((getSpone() == null) ? 0 : getSpone().hashCode());
        result = prime * result + ((getSponepeople() == null) ? 0 : getSponepeople().hashCode());
        result = prime * result + ((getSptwo() == null) ? 0 : getSptwo().hashCode());
        result = prime * result + ((getSptwopeople() == null) ? 0 : getSptwopeople().hashCode());
        result = prime * result + ((getSpthree() == null) ? 0 : getSpthree().hashCode());
        result = prime * result + ((getSpthreepeople() == null) ? 0 : getSpthreepeople().hashCode());
        result = prime * result + ((getSpbj2() == null) ? 0 : getSpbj2().hashCode());
        result = prime * result + ((getLssp() == null) ? 0 : getLssp().hashCode());
        result = prime * result + ((getLsspbj() == null) ? 0 : getLsspbj().hashCode());
        result = prime * result + ((getLy() == null) ? 0 : getLy().hashCode());
        result = prime * result + ((getYssbsd() == null) ? 0 : getYssbsd().hashCode());
        result = prime * result + ((getLstxz() == null) ? 0 : getLstxz().hashCode());
        result = prime * result + ((getDljb() == null) ? 0 : getDljb().hashCode());
        result = prime * result + ((getTxjx() == null) ? 0 : getTxjx().hashCode());
        result = prime * result + ((getZfz() == null) ? 0 : getZfz().hashCode());
        result = prime * result + ((getTlpp() == null) ? 0 : getTlpp().hashCode());
        result = prime * result + ((getSfygx() == null) ? 0 : getSfygx().hashCode());
        result = prime * result + ((getYlzd5() == null) ? 0 : getYlzd5().hashCode());
        result = prime * result + ((getYlzd6() == null) ? 0 : getYlzd6().hashCode());
        result = prime * result + ((getTxsbbh() == null) ? 0 : getTxsbbh().hashCode());
        result = prime * result + ((getSbsd() == null) ? 0 : getSbsd().hashCode());
        result = prime * result + ((getAuditing_status() == null) ? 0 : getAuditing_status().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", ylzd5=").append(ylzd5);
        sb.append(", ylzd6=").append(ylzd6);
        sb.append(", txsbbh=").append(txsbbh);
        sb.append(", sbsd=").append(sbsd);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}