package org.fh.entity.pass;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/7/28
 */
@Data
@NoArgsConstructor
public class VehicleVo {
    /**
     * 号牌种类
     */
    private String HPZL;
    /**
     * 号牌号码
     */
    private String HPHM;
    /**
     * 机动车状态
     */
    private String ZT;
    /**
     * 身份证明号码
     */
    private String SFZMHM;
    /**
     * 身份证明名称
     */
    private String SFZMMC;

    private Date  QZBFQZ;
    private String CLLX;
    private String SYXZ;
    private String SYR;
    private String SJHM;

    public Date getQZBFQZ() {
        return QZBFQZ;
    }

    public void setQZBFQZ(Date QZBFQZ) {
        this.QZBFQZ = QZBFQZ;
    }

    public String getCLLX() {
        return CLLX;
    }

    public void setCLLX(String CLLX) {
        this.CLLX = CLLX;
    }

    public String getSYXZ() {
        return SYXZ;
    }

    public void setSYXZ(String SYXZ) {
        this.SYXZ = SYXZ;
    }

    public String getSYR() {
        return SYR;
    }

    public void setSYR(String SYR) {
        this.SYR = SYR;
    }

    public String getSJHM() {
        return SJHM;
    }

    public void setSJHM(String SJHM) {
        this.SJHM = SJHM;
    }

    public Date getYXQZ() {
        return YXQZ;
    }

    public void setYXQZ(Date YXQZ) {
        this.YXQZ = YXQZ;
    }

    private Date YXQZ;

    public String getHPZL() {
        return HPZL;
    }

    public String getHPHM() {
        return HPHM;
    }

    public String getZT() {
        return ZT;
    }

    public String getSFZMHM() {
        return SFZMHM;
    }

    public String getSFZMMC() {
        return SFZMMC;
    }

    public void setHPZL(String HPZL) {
        this.HPZL = HPZL;
    }

    public void setHPHM(String HPHM) {
        this.HPHM = HPHM;
    }

    public void setZT(String ZT) {
        this.ZT = ZT;
    }

    public void setSFZMHM(String SFZMHM) {
        this.SFZMHM = SFZMHM;
    }

    public void setSFZMMC(String SFZMMC) {
        this.SFZMMC = SFZMMC;
    }
}
