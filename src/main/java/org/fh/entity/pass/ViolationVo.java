package org.fh.entity.pass;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/7/28
 * VIO_VIOLATION 违法记录表
 */
@Data
@NoArgsConstructor
public class ViolationVo {
    /**
     * 号牌种类
     */
    private String HPZL;
    /**
     * 号牌号码
     */
    private String HPHM;
    /**
     * 违法地址
     */
    private String WFDZ;
    /**
     * 违法行为
     */
    private String WFXW;
    /**
     * 违法记分数
     */
    private String WFJFS;
    /**
     * 交款标记
     */
    private String JKBJ;
    /**
     * 违法时间
     */
    private Date WFSJ;
}
