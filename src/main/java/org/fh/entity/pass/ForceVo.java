package org.fh.entity.pass;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
/**
 * <AUTHOR>
 * @date 2020/7/28
 * VIO_FORCE 强制措施表
 */
@Data
@NoArgsConstructor
public class ForceVo {
    /**
     * 号牌种类
     */
    private String HPZL;
    private String HPZLNUM;
    /**
     * 号牌号码
     */
    private String HPHM;
    /**
     * 违法地址
     */
    private String WFDZ;
    /**
     * 违法行为1
     */
    private String WFXW1;

    /**
     * 0-否;1-是;2-未核查
     */
    private String HCBJ;
    /**
     * 违法时间
     */
    private Date WFSJ;
    private String RN;
}
