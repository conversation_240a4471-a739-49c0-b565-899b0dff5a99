package org.fh.entity.pass;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/8/17
 * PASLICENSE_ENTERPRIS 通行证_企业信息表
 */
@Data
@ToString
@NoArgsConstructor
public class PaslicenseEnterpris implements Serializable {
    private String ID;
    private String ACCOUNT;  //账号
    private String PASSWORD;   //密码
    private String ENTERPRISENAME;  //企业名称
    private Date TJSJ;  //添加时间
    private String ORGANIZATIONCODE;  //组织机构代码
    private String OWNERNAME;  //法人姓名
    private String OWNERPHONE;  //法人联系电话
    private String MANAGERNAME;  //管理人姓名
    private String MANAGERPHONE;  //管理人电话
    private String ENTERPRISEADDRESS;  //企业地址
    private String ORGANIZATIONPIC;  // 组织机构代码证图片
    private String APPLICATIONPIC;  //申请书图片
    private String UNITORCONTRACTPIC;  //单位证明图片
    private String SHZT;  //审核状态（0未审核 1审核通过 2审核不通过）
    private Date SHSJ;  //审核时间
    private String SHR;  //审核人
    private String SHYJ;  //审核意见
    private Date XGSJ;  //修改时间
    private String ZHLX;  //账号类型(企业 个人)
    private String SWITCH;  //交换标记(0未交换 1已交换)
    private Date SWITCHTIME;  //交换时间
    private String TJBM;  //添加部门
    private String TJR;  //添加人


}
