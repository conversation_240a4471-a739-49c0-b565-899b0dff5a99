package org.fh.entity.pass;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * PASLICENSE_ROADPART
 * <AUTHOR>
@Data
public class PaslicenseRoadpart implements Serializable {
    /**
     * id
     */
    private String id;

    /**
     * 路段名称
     */
    private String ldmc;

    /**
     * 排序
     */
    private String px;

    /**
     * 六合一(0否1是)
     */
    private String lhy;

    /**
     * 拼音
     */
    private String py;

    /**
     * 等级
     */
    private String dj;

    /**
     * 显示 1显示 0不显示
     */
    private String xs;

    /**
     * 上级id
     */
    private String sjid;

    /**
     * 状态 0正常 1删除
     */
    private String zt;

    /**
     * 交换 0未交换 1已交换
     */
    private String jh;

    /**
     * 道路环数
     */
    private String dlhs;

    /**
     * 更新时间
     */
    private Date gxsj;

    private static final long serialVersionUID = 1L;
}