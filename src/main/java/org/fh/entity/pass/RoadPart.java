package org.fh.entity.pass;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;


public class RoadPart implements Serializable {


    private  String ROADPARTID;


    private  String LDMC;

    private String SD1;
    private String SD2;
    private String SD3;
    private String SD4;

    @JsonProperty("ROADPARTID")
    public String getROADPARTID() {
        return ROADPARTID;
    }

    public void setROADPARTID(String ROADPARTID) {
        this.ROADPARTID = ROADPARTID;
    }
    @JsonProperty("LDMC")
    public String getLDMC() {
        return LDMC;
    }

    public void setLDMC(String LDMC) {
        this.LDMC = LDMC;
    }

    public String getSD1() {
        return SD1;
    }

    public void setSD1(String SD1) {
        this.SD1 = SD1;
    }

    public String getSD2() {
        return SD2;
    }

    public void setSD2(String SD2) {
        this.SD2 = SD2;
    }

    public String getSD3() {
        return SD3;
    }

    public void setSD3(String SD3) {
        this.SD3 = SD3;
    }

    public String getSD4() {
        return SD4;
    }

    public void setSD4(String SD4) {
        this.SD4 = SD4;
    }
}
