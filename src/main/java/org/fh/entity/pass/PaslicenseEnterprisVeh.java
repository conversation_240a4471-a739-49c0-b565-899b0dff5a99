package org.fh.entity.pass;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/8/18
 * PASLICENSE_ENTERPRIS 通行证_面签车辆信息表
 */
@Data
@ToString
//@NoArgsConstructor
public class PaslicenseEnterprisVeh implements Serializable {

    private String ID;
    private String ENTERPRISEID;  //企业信息id
    private String OWNERTYPE;   //所属单位(1企业名下 2非企业名下)
    private String VEHTYPE;  //号牌种类
    private String VEHNUM;  //号牌号码
    private String DRIVERNAME;  //驾驶人姓名
    private String DRIVERCARD;  //驾驶证号
    private String DRIVERPHONE;  //联系电话
    private String VEHICLELICENSEPIC;  //行驶证图片
    private String SHZT;  //审核状态（0未审核 1审核通过 2审核不通过 3已完善待审批）
    private Date SHSJ;  //审核时间
    private String SHR;  //审核人
    private String SHYJ;  //审核意见
    private Date TJSJ;  //添加时间
    private Date XGSJ;  //修改时间
    private String OWNERNAME;  //车辆所有人
    private String OWNERIDCARD;  //所有人身份证明号码
    private String OWNERPHONE;  //所有人联系方式
    private String ACTOWNER;  //实际所有人
    private String ACTIDCARD;  //实际所有人身份证明号码
    private String ACTPIC;  //实际所有人身份证图片
    private String DRIVERPIC;  //驾驶人身份证明图片
    private String ACTPHONE;  //实际所有人联系方式
    private String SWITCH;  //交换标记(0未交换 1已交换)
    private Date SWITCHTIME;  //交换时间
    private String MAXPASSLEVEL;  //最大通行道路权限
    private String VEHPIC;  //行驶证车辆照片
    private String VEHFB;  //行驶证副本
    private String CLLX;  //车辆类型
    private String SYXZ;  //使用性质
    private String CLSYR;  //车辆所有人
    private String CLWTS;  //非账号名下车辆委托书
    private String CJH;  //车架号
    private String FDJH;  //发动机号
    private String MEMBERIDCARD;  //实际操作人身份证号
    private String WSCL;  //是否外省车辆（0否 1是）
    private Date NSYXQ; // 年审有效期
}
