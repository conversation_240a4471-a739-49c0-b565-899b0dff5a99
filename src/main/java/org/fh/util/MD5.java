package org.fh.util;

import org.springframework.util.DigestUtils;

import java.security.MessageDigest;

/**
 * 说明：MD5处理
 * 作者：FH Admin Q313596790
 * 官网：www.fhadmin.org
 */
public class MD5 {

	public static String md5(String str) {
		try {
			MessageDigest md = MessageDigest.getInstance("MD5");
			md.update(str.getBytes());
			byte b[] = md.digest();

			int i;

			StringBuffer buf = new StringBuffer("");
			for (int offset = 0; offset < b.length; offset++) {
				i = b[offset];
				if (i < 0)
					i += 256;
				if (i < 16)
					buf.append("0");
				buf.append(Integer.toHexString(i));
			}
			str = buf.toString();
		} catch (Exception e) {
			e.printStackTrace();

		}
		return str.toUpperCase();
	}
	public static void main(String[] args) {
		System.out.println(md5("<EMAIL>"+"123456"));
		System.out.println(md5("888888"));
		String md5 = DigestUtils.md5DigestAsHex("888888".getBytes());
		System.out.println(md5);
	}
}
