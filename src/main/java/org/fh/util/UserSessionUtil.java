package org.fh.util;

import org.fh.constant.AccountConstant;

/**
 * 获取用户角色工具类
 *
 * <AUTHOR>
 */
public class UserSessionUtil {

    /**判断用户分组 科研所、秩序科或者特勤大队
     * @return
     */
    public static String getUserGroup(String Rnumbers){
        if (Rnumbers.contains(AccountConstant.SystemKysnumber) || Rnumbers.contains(AccountConstant.BackKysnumber))
        {
            return AccountConstant.kys;//用户是否在系统组或者备用组的秩序科
        }else if (Rnumbers.contains(AccountConstant.SystemZxknumber) || Rnumbers.contains(AccountConstant.BackZxknumber))
        {
            return AccountConstant.zxk;
        }else {
            return AccountConstant.tqdd;
        }
    }
}
