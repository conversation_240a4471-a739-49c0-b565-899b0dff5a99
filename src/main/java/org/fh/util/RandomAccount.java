package org.fh.util;

import java.io.UnsupportedEncodingException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Random;

/**
 * 随机账号生成
 */
public class RandomAccount {
    /**
     * 获取账号
     * @return
     */
    public  static  String getAccount(){

        //获取当前时间
        LocalDateTime time=LocalDateTime.now();
        //时间格式化
        DateTimeFormatter dtf2 = DateTimeFormatter.ofPattern("yyyyMMdd");
        //获取时间格式化后的
        String strDate2 = dtf2.format(time);
        //获取随机生成的两位字母或字母和数字组合
        String randomString = getStringRandom(1);
        String account = randomString+strDate2 + getStringRandom(4);
        return account;
    }

    //自动生成名字（中文）
    private static String getRandomJianHan(int len) {
        String ret = "";
        for (int i = 0; i < len; i++) {
            String str = null;
            int hightPos, lowPos; // 定义高低位
            Random random = new Random();
            hightPos = (176 + Math.abs(random.nextInt(39))); // 获取高位值
            lowPos = (161 + Math.abs(random.nextInt(93))); // 获取低位值
            byte[] b = new byte[2];
            b[0] = (new Integer(hightPos).byteValue());
            b[1] = (new Integer(lowPos).byteValue());
            try {
                str = new String(b, "GBK"); // 转成中文
            } catch (UnsupportedEncodingException ex) {
                ex.printStackTrace();
            }
            ret += str;
        }
        return ret;
    }

    //生成随机用户名，数字和字母组成,
    private static String getStringRandom(int length) {

        StringBuilder val = new StringBuilder();
        Random random = new Random();

        //参数length，表示生成几位随机数
        for (int i = 0; i < length; i++) {

            // String charOrNum = random.nextInt(2) % 2 == 0 ? "char" : "num";
            // //输出字母还是数字
            // if ("char".equalsIgnoreCase(charOrNum)) {
            //     //输出是大写字母还是小写字母
            //     int temp = random.nextInt(2) % 2 == 0 ? 65 : 97;
            //     val += (char) (random.nextInt(26) + temp);
            //
            // } else if ("num".equalsIgnoreCase(charOrNum)) {
                val.append(random.nextInt(10));
            //
            // }

        }
        return val.toString();

    }
}

