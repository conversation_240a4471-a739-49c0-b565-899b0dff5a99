package org.fh.util;

import com.mysql.cj.jdbc.Blob;
import com.mysql.cj.jdbc.Clob;
import sun.misc.BASE64Encoder;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.Reader;
import java.sql.SQLException;

public class BLOBToBase64 {

    public static String blobToBase64(Blob blob) {
        String result = "";
        if (null != blob) {
            try {
                InputStream msgContent = blob.getBinaryStream();
                ByteArrayOutputStream output = new ByteArrayOutputStream();
                byte[] buffer = new byte[100];
                int n = 0;
                while (-1 != (n = msgContent.read(buffer))) {
                    output.write(buffer, 0, n);
                }
                result = new BASE64Encoder().encode(output.toByteArray());
                output.close();
            } catch (SQLException e) {
                e.printStackTrace();
            } catch (Exception e) {
                e.printStackTrace();
            }
            return result;
        } else {
            return null;
        }
    }
    /**
     * 数据库Clob对象转换为String
     */
    public static String clobToString(Clob clob) {
        try {
            Reader inStreamDoc = clob.getCharacterStream();

            char[] tempDoc = new char[(int) clob.length()];
            inStreamDoc.read(tempDoc);
            inStreamDoc.close();

            return new String(tempDoc);
        } catch (IOException e) {
            e.printStackTrace();
        } catch (SQLException es) {
            es.printStackTrace();
        }
        return null;
    }

    /**
     *  Base64编码.
     */
    public static String base64Encode(byte[] input) {

        BASE64Encoder encoder = new BASE64Encoder();
        return encoder.encode(input);
    }
}
