package org.fh.util;

import java.util.HashMap;
import java.util.Map;

/**
 * 说明：常量
 * 作者：FH Admin Q313596790
 * 官网：www.fhadmin.org
 */
public class Const {
	
	public static final String SESSION_USER = "SESSION_USER";						//session用的用户
	public static final String SESSION_USERROL = "SESSION_USERROL";					//用户对象(包含角色信息)
	public static final String SESSION_ROLE_RIGHTS = "SESSION_ROLE_RIGHTS";			//角色菜单权限
	public static final String SHIROSET = "SHIROSET";								//菜单权限标识
	public static final String SESSION_USERNAME = "USERNAME";						//用户名
	public static final String SESSION_U_NAME = "SESSION_U_NAME";					//用户姓名
	public static final String SESSION_ROLE = "SESSION_ROLE";						//主职角色信息
	public static final String SESSION_RNUMBERS = "RNUMBERS";						//角色编码数组
	public static final String SESSION_ALLMENU = "SESSION_ALLMENU";					//全部菜单
	public static final String SKIN = "SKIN";										//用户皮肤
	
	public static final String SYSSET = "config/sysSet.ini";						//系统设置配置文件路径
	public static final String SYSNAME = "sysName";									//系统名称
	public static final String SHOWCOUNT = "showCount";								//每页条数
	
	public static final String FILEPATHFILE = "C:/uploadFiles/file/";					//文件上传路径
	public static final String FILEPATHIMG = "uploadFiles/imgs/";					//图片上传路径
	
	public static final String FILEACTIVITI = "uploadFiles/activitiFile/";			//工作流生成XML和PNG目录
	
	public static final String DEPARTMENT_IDS = "DEPARTMENT_IDS";					//当前用户拥有的最高部门权限集合
	public static final String DEPARTMENT_ID = "DEPARTMENT_ID";						//当前用户拥有的最高部门权限

	public final static Map ZT_MAP = new HashMap() {{
		put("A", "正常");
		put("B", "转出");
		put("C", "被盗抢");
		put("D", "停驶");
		put("E", "注销");
		put("G", "违法未处理");
		put("H", "海关监管");
		put("I", "事故未处理");
		put("J", "嫌疑车");
		put("K", "查封");
		put("L", "扣留");
		put("M", "达到报废标准");
		put("N", "事故逃逸");
		put("O", "锁定");
		put("P", "达到报废标准公告牌证作废");
		put("Q", "逾期未检验");
	}};
}
