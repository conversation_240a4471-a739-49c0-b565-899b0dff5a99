package org.fh.util;

import java.io.FileNotFoundException;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.TimeZone;

public class DB {

    //主表
    public static Connection getConnection(){
        Connection conn = null;
        String DRIVER="oracle.jdbc.driver.OracleDriver";
        // 127.0.0.1是本机地址，XE是精简版Oracle的默认数据库名(公安网原始数据库)
        String url = "****************************************";
        System.out.println(url);
        // 用户名,系统默认的账户名
        String user = "mjjusapp";
        // 你安装时选设置的密码
        String password = "apP5143";
        try {
            // 加载数据库驱动程序
            Class.forName(DRIVER);
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
        try {
            // 获得Connection对象
            conn = DriverManager.getConnection(url, user, password);
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return conn;
    }
    //从表
    public static Connection getConnection2(){
        Connection conn = null;
        String DRIVER="oracle.jdbc.driver.OracleDriver";
        String url = "*************************************";
        System.out.println(url);
        String user = "MJJADAPP";
        String password = "root123";
        try {
            Class.forName(DRIVER);
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
        try {
            conn = DriverManager.getConnection(url, user, password);
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return conn;
    }

    public static List<List<Object>> tableInput() throws FileNotFoundException,
            SQLException {
        List<List<Object>> FindList = new ArrayList<List<Object>>();
        Connection con = getConnection();
        PreparedStatement pre = null;
        ResultSet resultSet = null;
        String sql = "SELECT ID,ENTERPRISEID,OWNERTYPE,VEHTYPE,VEHNUM,DRIVERNAME,DRIVERCARD,DRIVERPHONE" +
                ",VEHICLELICENSEPIC,SHZT,SHSJ,SHR,SHYJ,TJSJ,XGSJ,OWNERNAME,OWNERIDCARD,OWNERPHONE,ACTOWNER" +
                ",ACTIDCARD,ACTPIC,DRIVERPIC,ACTPHONE,SWITCH,SWITCHTIME,MAXPASSLEVEL,VEHPIC,VEHFB,CLLX" +
                ",SYXZ,CLSYR,CLWTS FROM MJJADAPP.PASLICENSE_ENTERPRISE_VEH";
        try {
            pre = con.prepareStatement(sql);
            resultSet = pre.executeQuery();
            String[] columu = {"ID","ENTERPRISEID","OWNERTYPE","VEHTYPE","VEHNUM","DRIVERNAME","DRIVERCARD","DRIVERPHONE",
            "VEHICLELICENSEPIC","SHZT","SHSJ","SHR","SHYJ","TJSJ","XGSJ","OWNERNAME","OWNERIDCARD","OWNERPHONE","ACTOWNER"
                    ,"ACTIDCARD","ACTPIC","DRIVERPIC","ACTPHONE","SWITCH","SWITCHTIME","MAXPASSLEVEL","VEHPIC","VEHFB"
                    ,"CLLX","SYXZ","CLSYR","CLWTS"};
            int i=0;
            System.out.println(resultSet.getRow());
            while (resultSet.next()) {
                List<Object> minList = new ArrayList<Object>();
                for(String each:columu){
                    minList.add(resultSet.getObject(each));
                }
                FindList.add(minList);
                i++;
                if(i%500==0){           //设置的每次提交大小为10000
                    executeManySql(FindList);
//                    System.out.println(FindList);
                    FindList.removeAll(FindList);
                    System.out.println(i);

                }
            }
            executeManySql(FindList);//最后别忘了提交剩余的
            return FindList;
        } catch (SQLException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } finally {
            try {
                pre.close();// 关闭Statement
            } catch (SQLException e) {
                e.printStackTrace();
            }
            try {
                con.close();// 关闭Connection
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    public static void executeManySql(List<List<Object>> FindList) throws SQLException {
        Connection con = getConnection2();
        con.setAutoCommit(false);
        Statement stat = null;
        String A = "insert into \"MJJADAPP\".\"PASLICENSE_ENTERPRISE_VEH\"(ID,ENTERPRISEID,OWNERTYPE,VEHTYPE,VEHNUM,DRIVERNAME,DRIVERCARD,DRIVERPHONE,VEHICLELICENSEPIC,SHZT,SHSJ,SHR,SHYJ,TJSJ,XGSJ,OWNERNAME,OWNERIDCARD,OWNERPHONE,ACTOWNER,ACTIDCARD,ACTPIC,DRIVERPIC,ACTPHONE,SWITCH,SWITCHTIME,MAXPASSLEVEL,VEHPIC,VEHFB,CLLX,SYXZ,CLSYR,CLWTS) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        System.out.println(A);
        PreparedStatement pst = (PreparedStatement) con
                .prepareStatement("insert into \"MJJADAPP\".\"PASLICENSE_ENTERPRISE_VEH\"(ID,ENTERPRISEID,OWNERTYPE,VEHTYPE,VEHNUM,DRIVERNAME,DRIVERCARD,DRIVERPHONE,VEHICLELICENSEPIC,SHZT,SHSJ,SHR,SHYJ,TJSJ,XGSJ,OWNERNAME,OWNERIDCARD,OWNERPHONE,ACTOWNER,ACTIDCARD,ACTPIC,DRIVERPIC,ACTPHONE,SWITCH,SWITCHTIME,MAXPASSLEVEL,VEHPIC,VEHFB,CLLX,SYXZ,CLSYR,CLWTS) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
        for (List<Object> minList: FindList) {
            for(int i=0;i<minList.size();i++){
                pst.setObject(i+1, minList.get(i));
            }
            // 把一个SQL命令加入命令列表
            pst.addBatch();
        }
        // 执行批量更新
        try {
            pst.executeBatch();
            // 语句执行完毕，提交本事务
            con.commit();
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            pst.close();
            con.close();//一定要记住关闭连接，不然mysql回应为too many connection自我保护而断开。
        }
    }
    public static void getDB()throws FileNotFoundException,
            SQLException {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss:SS");
        TimeZone t = sdf.getTimeZone();
        t.setRawOffset(0);
        sdf.setTimeZone(t);
        Long startTime = System.currentTimeMillis();
        //此段为要放置测取时间的函数
//        mysqlConnection.executeSql("TRUNCATE table disease_drug_associate_view");
        List<List<Object>> newDrug = tableInput();

        Long endTime = System.currentTimeMillis();
        System.out.println("用时：" + sdf.format(new Date(endTime - startTime)));
    }
    public static void main(String[] args) throws FileNotFoundException,
            SQLException {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss:SS");
        TimeZone t = sdf.getTimeZone();
        t.setRawOffset(0);
        sdf.setTimeZone(t);
        Long startTime = System.currentTimeMillis();
        //此段为要放置测取时间的函数
//        mysqlConnection.executeSql("TRUNCATE table disease_drug_associate_view");
//        List<List<Object>> newDrug = tableInput();
        String A = "insert into \"MJJADAPP\".\"PASLICENSE_ENTERPRISE_VEH\"(ID,ENTERPRISEID,OWNERTYPE,VEHTYPE,VEHNUM,DRIVERNAME,DRIVERCARD,DRIVERPHONE,VEHICLELICENSEPIC,SHZT,SHSJ,SHR,SHYJ,TJSJ,XGSJ,OWNERNAME,OWNERIDCARD,OWNERPHONE,ACTOWNER,ACTIDCARD,ACTPIC,DRIVERPIC,ACTPHONE,SWITCH,SWITCHTIME,MAXPASSLEVEL,VEHPIC,VEHFB,CLLX,SYXZ,CLSYR,CLWTS) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        System.out.println(A.replaceAll("\\?","\"1\""));
        Long endTime = System.currentTimeMillis();
        System.out.println("用时：" + sdf.format(new Date(endTime - startTime)));
    }
}
