package org.fh.util;

import org.apache.commons.lang3.StringUtils;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import javax.crypto.spec.IvParameterSpec;
import java.security.Key;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class DesEncryptor {

    ///声明
    private static final String ALGORITHM = "DES";
    private static final String CIPHER_TRANSFORMAT = "DES/CBC/PKCS5Padding";
    private static final String ENCODING = "UTF-8";

    /**
     * 加密数据
     *
     * @param data 待加密数据
     * @param key  密钥(8位字符的base64)
     * @return 加密后的数据
     */
    public static String encrypt(String data, String key) throws Exception {
        String newKey = RestoreKey(key);
        ///还原密钥
        DESKeySpec dks = new DESKeySpec(newKey.getBytes(ENCODING));  ///实例化Des密钥
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(ALGORITHM); /// 实例化密钥工厂
        Key secretKey = keyFactory.generateSecret(dks);
        ///向量偏移量
        IvParameterSpec iv = new IvParameterSpec(newKey.getBytes(ENCODING));
        /// 实例化Cipher对象，它用于完成实际的加密操作
        Cipher cipher = Cipher.getInstance(CIPHER_TRANSFORMAT);
        cipher.init(Cipher.ENCRYPT_MODE, secretKey, iv); // 初始化Cipher对象，设置为加密模式
        // 执行加密操作。加密后的结果通常都会用Base64编码进行传输
        byte[] result=cipher.doFinal(data.getBytes());
        return  new BASE64Encoder().encode(result);
    }


    ///DES 解密 Dncrypt
    public static String desDncrypt(String message, String key) throws Exception {
        String newKey = RestoreKey(key);
        ///秘钥
        DESKeySpec desKeySpec = new DESKeySpec(newKey.getBytes(ENCODING));
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(ALGORITHM);
        SecretKey secretKey = keyFactory.generateSecret(desKeySpec);
        ///偏移量
        IvParameterSpec iv = new IvParameterSpec(newKey.getBytes(ENCODING));
        ///解密密文
        byte[] msessageb=new BASE64Decoder().decodeBuffer(message);
        Cipher cipher = Cipher.getInstance(CIPHER_TRANSFORMAT);
        cipher.init(Cipher.DECRYPT_MODE, secretKey, iv);
        byte[] retByte = cipher.doFinal(msessageb);
        return new String(retByte);
    }
    //大于8位解密秘钥
    public static String RestoreKey(String key) throws Exception {
        int lastnum = 0;
        if (key.length() > 8){
            if (StringUtils.isNotEmpty(key)){
                Pattern reg = Pattern.compile("\\D");
                Matcher m = reg.matcher(key);
                String numString = m.replaceAll("");
                if (numString.length() > 0){
                    lastnum = Integer.parseInt(numString.substring(numString.length() - 1));
                }
            }
            return key.substring(lastnum , lastnum+8);
        }
    else{
        return key;
        }
    }
    ///主函数  测试
    public static void main(String[] args) {
        try {
            String param = "{\"hphm\":\"桂BA0000\",\"hpzl\":\"02\",\"lx\":1}";
            System.out.println("返回数据(加密) ：" + DesEncryptor.encrypt(param,"txz54321"));

            String result = "kl/P//YgWpISZKi8is6ogcOrxudwG726";
            System.out.println("返回数据(解密) ：" + DesEncryptor.desDncrypt(result.replaceAll("\\\\r\\\\n", ""),"txz54321"));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}

