package org.fh.util;

/**
 * <AUTHOR>
 */
public class ZtUtil {

    /**
     *根据传进来的车辆状态字符串查询对应中文
     */
    public static String getZTInfo(String ZT){
        String ZTInfo = "";
        if (ZT.length() != 1){
            StringBuffer buf = new StringBuffer();
            for(int i2 = 0; i2 < ZT.length(); i2++) {
                if (Const.ZT_MAP.get(String.valueOf(ZT.charAt(i2))) != null){
                    buf.append(Const.ZT_MAP.get(String.valueOf(ZT.charAt(i2))));
                    buf.append(' ');
                }
            }
            ZTInfo = buf.toString();
        }else {
            ZTInfo = (String) Const.ZT_MAP.get(ZT);
        }
        return ZTInfo;
        }
}
