package org.fh.service.pass;

import org.fh.entity.Page;
import org.fh.entity.PageData;
import org.fh.entity.pass.PaslicenseEnterprisVeh;
import org.fh.entity.pass.VehicleImageBean;

import java.util.List;

/**
 * 说明： 区域通行证--面签车辆审批接口
 * 作者：FH Admin QQ313596790
 * 时间：2020-05-27
 * 官网：www.fhadmin.org
 */
public interface VehicleApprovalService {

    /**
     * 新增
     *
     * @param pd
     * @throws Exception
     */
    public void save(PageData pd) throws Exception;

    public void saveInfo(PaslicenseEnterprisVeh paslicenseEnterprisVeh) throws Exception;

    /**
     * 删除
     *
     * @param pd
     * @throws Exception
     */
    public void delete(PageData pd) throws Exception;

    /**
     * 修改
     *
     * @param pd
     * @throws Exception
     */
    public void edit(PaslicenseEnterprisVeh paslicenseEnterprisVeh) throws Exception;

    /**
     * 列表
     *
     * @param page
     * @throws Exception
     */
    public List<PageData> list(Page page) throws Exception;

    /**
     * 列表(全部)
     *
     * @param pd
     * @throws Exception
     */
    public List<PageData> listAll(PageData pd) throws Exception;

    /**
     * 获取总记录数
     *
     * @param pd
     * @return
     * @throws Exception
     */
    public int getCount(PageData pd) throws Exception;

    /**
     * 通过id获取数据
     *
     * @param pd
     * @throws Exception
     */
    public PageData findById(PageData pd) throws Exception;

    public PaslicenseEnterprisVeh findByID(String id);

    public PaslicenseEnterprisVeh GetInfoById(String id);

    /**
     * 通过id获取图片
     *
     * @param pd
     * @throws Exception
     */
    public VehicleImageBean getImageById(PageData pd) throws Exception;

    /**
     * 批量删除
     *
     * @param ArrayDATA_IDS
     * @throws Exception
     */
    public void deleteAll(String[] ArrayDATA_IDS) throws Exception;

    PaslicenseEnterprisVeh findByInfo(PaslicenseEnterprisVeh paslicenseEnterprisVeh);

  




}

