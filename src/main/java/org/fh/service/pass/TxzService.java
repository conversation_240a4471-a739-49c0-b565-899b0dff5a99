package org.fh.service.pass;


import org.fh.entity.Page;
import org.fh.entity.PageData;

import java.util.List;
import java.util.Map;

/**
 * 说明： 通行证接口
 * 作者：FH Admin QQ313596790
 * 时间：2020-05-26
 * 官网：www.fhadmin.org
 * @version
 */
public interface TxzService{

	/**新增
	 * @param pd
	 * @throws Exception
	 */
	public void save(PageData pd)throws Exception;

	/**删除
	 * @param pd
	 * @throws Exception
	 */
	public void delete(PageData pd)throws Exception;

	/**修改
	 * @param pd
	 * @throws Exception
	 */
	public void edit(PageData pd)throws Exception;
	public void update(PageData pd)throws Exception;

	/**列表
	 * @param page
	 * @throws Exception
	 */
	public List<PageData> list(Page page)throws Exception;
	public List<PageData> selectList(Page page)throws Exception;
	public List<PageData> selectByList(Page page)throws Exception;


	/**列表(全部)
	 * @param pd
	 * @throws Exception
	 */
	public List<PageData> listAll(PageData pd)throws Exception;

	/**通过id获取数据
	 * @param pd
	 * @throws Exception
	 */
	public PageData findById(PageData pd)throws Exception;

	/**批量删除
	 * @param ArrayDATA_IDS
	 * @throws Exception
	 */
	public void deleteAll(String[] ArrayDATA_IDS)throws Exception;

	/**批量审批
	 * @param ArrayDATA_IDS
	 * @throws Exception
	 */
	public void updateAll(PageData pd)throws Exception;
	public void updateById(PageData pd)throws Exception;


	List<Map<String, Object>> fandBySPZT();


	List<Map<String, Object>> fandByHPZL();

	List<String> GetRoad(String ysqs, String yxqz,String jbr);


	Map<String, Object>  GetSjtj(String ysqs, String yxqz,String jbr);

	List<PageData> rowlistPage(Page page)throws Exception;

	List datalist(Page page);
}

