package org.fh.service.pass;


import org.fh.entity.Page;
import org.fh.entity.PageData;
import org.fh.entity.pass.PASLICENSE;
import org.fh.entity.pass.PASLICENSEWithBLOBs;
import org.fh.entity.pass.PaslicenseRoadpart;

import java.util.List;
import java.util.Map;

public interface PASLICENSEService {

    int insert(PASLICENSEWithBLOBs record);

    List<PaslicenseRoadpart> select(String dj);

    PASLICENSEWithBLOBs getById(String xh);

    PASLICENSEWithBLOBs selectById(String hpzl,String hphm,String MEMBERIDCARD, String ENTERPRISEID);

    String selectXh(String hpzl,String hphm);

    PASLICENSEWithBLOBs selectListById(String XH);

    List<String> selectByDj();

    void update(PASLICENSEWithBLOBs record);

    void updateZx(PASLICENSEWithBLOBs record);

    public List<PageData> selectPage(Page page) throws Exception;

    public int getRoadCount(PageData pd)throws Exception;

    public PASLICENSE getId(String xh);

    void update_txz(PASLICENSEWithBLOBs paslicenseWithBLOBs);


    List<Map<String, Object>> trafficStatus(String hphm, String hpzl, String txzlx);

    List<PaslicenseRoadpart> selectAll();
}
