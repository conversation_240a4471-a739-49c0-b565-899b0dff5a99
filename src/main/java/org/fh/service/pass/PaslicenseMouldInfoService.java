package org.fh.service.pass;

import org.fh.entity.Page;
import org.fh.entity.PageData;

import java.util.List;

/** 
 * 说明： 模板和路段信息关联模块--模板明细接口
 * 作者：FH Admin QQ313596790
 * 时间：2020-06-13
 * 官网：www.fhadmin.org
 * @version
 */
public interface PaslicenseMouldInfoService{

	/**新增
	 * @param pd
	 * @throws Exception
	 */
	public void save(PageData pd)throws Exception;

	/**批量新增
	 * @param pd
	 * @throws Exception
	 */
	public void addBatch(List<PageData> pd)throws Exception;
	
	/**删除
	 * @param pd
	 * @throws Exception
	 */
	public void delete(PageData pd)throws Exception;
	
	/**修改
	 * @param pd
	 * @throws Exception
	 */
	public void edit(PageData pd)throws Exception;

	/**
	 * 通过道路ID获取数据
	 */
	public List<String> getById(String ROADPARTID,String MBID)throws Exception;
	
	/**列表
	 * @param page
	 * @throws Exception
	 */
	public List<PageData> list(Page page)throws Exception;
	
	/**列表(全部)
	 * @param pd
	 * @throws Exception
	 */
	public List<PageData> listAll(PageData pd)throws Exception;

	/**通过模板id获取路段id列表
	 * @param MBID
	 * @throws Exception
	 */
	public  List<String> findByMBId(String MBID)throws Exception;

	/**批量删除
	 * @param ArrayDATA_IDS
	 * @throws Exception
	 */
	public void deleteAll(String[] ArrayDATA_IDS)throws Exception;
	
}

