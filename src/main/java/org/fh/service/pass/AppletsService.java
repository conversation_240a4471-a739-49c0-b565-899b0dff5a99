package org.fh.service.pass;

import org.fh.entity.Page;
import org.fh.entity.PageData;
import org.fh.entity.pass.PASLICENSEWithBLOBs;
import org.fh.entity.pass.RoadVo;

import java.util.List;
import java.util.Map;

/**
 * 小程序接口
 */
public interface AppletsService {

    /**
     * 查询道路信息列表
     * @param pd
     * @return
     * @throws Exception
     */
    public List<PageData> datalistPage(Page page) throws Exception;

    /**
     * 查询面签账号信息
     * @param pd
     * @return
     * @throws Exception
     */
    public PageData findPass(PageData pd) throws Exception;

    public List<PageData> findByAHE(Page page) throws Exception;

    public List<PageData> findByAHEList(Page page) throws Exception;

    List<RoadVo> datalistPageByDJ(Map page);

    List<PageData> findByEN(Page page);

    List<Map<String, Object>> fandByMQ();

    List<PASLICENSEWithBLOBs> selectByIdHphm(PASLICENSEWithBLOBs paslicenseWithBLOBs);

    public String selectIdCard(String VEHNUM, String VEHTYPE, String MEMBERIDCARD) throws Exception;

    String selectCard(String VEHNUM, String VEHTYPE, String ENTERPRISEID) throws Exception;

    String selectByIdCard(String VEHNUM, String VEHTYPE) throws Exception;

    String GetByIdCard(String VEHNUM, String VEHTYPE) throws Exception;

    String GetIdCard(String VEHNUM, String VEHTYPE) throws Exception;

    List selectListIdCar(PASLICENSEWithBLOBs paslicenseWithBLOBs);

    List selectTest(PASLICENSEWithBLOBs paslicenseWithBLOBs);
}
