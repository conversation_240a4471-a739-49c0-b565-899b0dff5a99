package org.fh.service.pass;


import org.fh.entity.Page;
import org.fh.entity.PageData;
import org.fh.entity.pass.AccountImageBean;
import org.fh.entity.pass.PaslicenseEnterpris;

import java.util.List;
import java.util.Map;

/** 
 * 说明： 区域通行证--面签账户管理接口
 * 作者：FH Admin QQ313596790
 * 时间：2020-05-27
 * 官网：www.fhadmin.org
 * @version
 */
public interface AccountManagementService{

	/**新增
	 * @param pd
	 * @throws Exception
	 */
	public void save(PageData pd)throws Exception;
	
	/**删除
	 * @param pd
	 * @throws Exception
	 */
	public void delete(PageData pd)throws Exception;
	
	/**修改
	 * @param pd
	 * @throws Exception
	 */
	public void edit(PageData pd)throws Exception;

	/**修改
	 * @param pd
	 * @throws Exception
	 */
	public void editPwd(PageData pd)throws Exception;
	
	/**列表
	 * @param page
	 * @throws Exception
	 */
	public List<PageData> list(Page page)throws Exception;
	public List<PageData> selectAll(Page page)throws Exception;
	String selectId(String ID);
	public List<PageData> selectCar(Page page)throws Exception;
	
	/**列表(全部)
	 * @param pd
	 * @throws Exception
	 */
	public List<PageData> listAll(PageData pd)throws Exception;

	/**
	 * 获取分页查询总记录数
	 * @param pd
	 * @return
	 */
	public int getCount(PageData pd) throws Exception;
	public int getRoadCount(PageData pd) throws Exception;
	public int carCount(PageData pd) throws Exception;

	/**通过id获取数据
	 * @param pd
	 * @throws Exception
	 */
	public PageData findById(PageData pd)throws Exception;
	public PageData findPass(PageData pd)throws Exception;
	/**通过id获取数据
	 * @param pd
	 * @throws Exception
	 */
	public AccountImageBean getImageById(PageData pd)throws Exception;
	
	/**批量删除
	 * @param ArrayDATA_IDS
	 * @throws Exception
	 */
	public void deleteAll(String[] ArrayDATA_IDS)throws Exception;

	/**通过帐号名获取数据
	 * @param account
	 * @throws Exception
	 */
	public PaslicenseEnterpris getInfoByAccount(String account);

	public int updatePaslicenseEnterpris(PaslicenseEnterpris paslicenseEnterpris);

	public PaslicenseEnterpris findByStringId(String id);


	public PaslicenseEnterpris getInfoByAccountID(String id, String account);

    List<Map<String, Object>> fandByMQ();

}

