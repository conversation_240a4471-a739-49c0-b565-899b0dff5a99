package org.fh.service.pass.impl;

import org.fh.entity.pass.ForceVo;
import org.fh.entity.pass.SurveilVo;
import org.fh.entity.pass.VehicleVo;
import org.fh.entity.pass.ViolationVo;
import org.fh.mapper.dsno2.pass.ForceMapper;
import org.fh.mapper.dsno2.pass.SurveilMapper;
import org.fh.mapper.dsno2.pass.VehicleMapper;
import org.fh.mapper.dsno2.pass.ViolationMapper;
import org.fh.service.pass.VehicleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
/**
 * <AUTHOR>
 * @date 2020/7/28
 */
@Service
public class VehicleServiceImpl implements VehicleService {
    @Autowired
    private VehicleMapper vehicleMapper;
    @Autowired
    private ViolationMapper violationMapper;
    @Autowired
    private SurveilMapper surveilMapper;
    @Autowired
    private ForceMapper forceMapper;

    @Override
    public VehicleVo getZT(VehicleVo vehicleVo) {
        return vehicleMapper.getZT(vehicleVo.getHPZL(),vehicleVo.getHPHM());
    }

    @Override
    public List<ViolationVo> getWF(ViolationVo violationVo) {
        return violationMapper.GetViolation(violationVo.getHPZL(),violationVo.getHPHM(), "0");
    }

    @Override
    public List<SurveilVo> GetSurveilWf(SurveilVo surveilVo) {
        return surveilMapper.GetSurveil(surveilVo.getHPZL(),surveilVo.getHPHM(),"0");
    }

    @Override
    public List<ForceVo> GetForceWf(ForceVo forceVo) {
        return forceMapper.GetForce(forceVo.getHPZL(),forceVo.getHPHM(),"0");
    }

    @Override
    public VehicleVo getVehicleInfo(VehicleVo vehicleVo) {
        return vehicleMapper.getVehicleInfo(vehicleVo.getHPZL(),vehicleVo.getHPHM());
    }
}
