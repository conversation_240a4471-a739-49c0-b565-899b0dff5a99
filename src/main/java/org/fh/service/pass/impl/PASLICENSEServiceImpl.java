package org.fh.service.pass.impl;


import org.fh.entity.Page;
import org.fh.entity.PageData;
import org.fh.entity.pass.PASLICENSE;
import org.fh.entity.pass.PASLICENSEWithBLOBs;
import org.fh.entity.pass.PaslicenseRoadpart;
import org.fh.mapper.dsno2.pass.PASLICENSEMapper;
import org.fh.mapper.dsno2.pass.PaslicenseRoadpartMapper;
import org.fh.mapper.dsno2.pass.RoadInfoMapper;
import org.fh.service.pass.PASLICENSEService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class PASLICENSEServiceImpl implements PASLICENSEService {

    @Resource
    private PASLICENSEMapper paslicenseMapper;

    @Resource
    private PaslicenseRoadpartMapper paslicenseRoadpartMapper;

    @Resource
    private RoadInfoMapper roadInfoMapper;

    @Override
    public int insert(PASLICENSEWithBLOBs record) {
        record.setYlzd5(record.getYlzd5());
        record.setYlzd6(record.getYlzd6());
        record.setTxsbbh(record.getTxsbbh());
        record.setGxsj(new Date());
        record.setDjrq(new Date());
        return this.paslicenseMapper.insert(record);
    }

    @Override
    public List<PaslicenseRoadpart> select(String dj) {
        return paslicenseRoadpartMapper.select(dj);
    }

    @Override
    public List<String> selectByDj() {
        return paslicenseRoadpartMapper.selectByDj();
    }
    @Override
    public List<PaslicenseRoadpart> selectAll() {
        return paslicenseRoadpartMapper.selectAll();
    }
    @Override
    public PASLICENSEWithBLOBs getById(String xh) {
        PASLICENSEWithBLOBs record = new PASLICENSEWithBLOBs();
        record.getYlzd5();
        record.getYssbsd();
        record.getYlzd6();
        record = paslicenseMapper.select(xh);
        return record;
    }

    @Override
    public PASLICENSEWithBLOBs selectById(String hpzl, String hphm, String MEMBERIDCARD, String ENTERPRISEID) {
        PASLICENSEWithBLOBs record = paslicenseMapper.selectById(hpzl, hphm, MEMBERIDCARD, ENTERPRISEID);
        return record;
    }

    @Override
    public String selectXh(String hpzl, String hphm) {
        return paslicenseMapper.selectXh(hpzl, hphm);
    }

    @Override
    public PASLICENSEWithBLOBs selectListById(String XH) {
        PASLICENSEWithBLOBs record = new PASLICENSEWithBLOBs();
        record.getYlzd5();
        record.getYssbsd();
        record.getYlzd6();
        record = paslicenseMapper.selectListById(XH);
        return record;
    }

    @Override
    public List<Map<String, Object>> trafficStatus(String hphm, String hpzl, String txzlx) {
        ArrayList<Map<String,Object>> list = new ArrayList<>();
        Map<String, Object> map = paslicenseMapper.trafficStatus(hphm, hpzl, txzlx);
        Integer mapList = this.paslicenseMapper.trafficStatus1(hphm, hpzl, txzlx);
        if (map != null && mapList != null) {
            map.put("zs", mapList);
        }
        list.add(map);
        return list;
    }

    @Override
    public void update(PASLICENSEWithBLOBs record) {
        record.setGxsj(new Date());
        paslicenseMapper.update(record);
    }

    @Override
    public void updateZx(PASLICENSEWithBLOBs record) {
        record.setGxsj(new Date());
        paslicenseMapper.updateZx(record);
    }

    @Override
    public List<PageData> selectPage(Page page) throws Exception {
        return roadInfoMapper.selectPage(page);
    }

    @Override
    public int getRoadCount(PageData pd) throws Exception {
        return roadInfoMapper.getRoadCount(pd);
    }

    @Override
    public PASLICENSE getId(String xh) {
        return paslicenseMapper.getId(xh);
    }

    @Override
    public void update_txz(PASLICENSEWithBLOBs paslicenseWithBLOBs) {
        paslicenseWithBLOBs.setYlzd5(paslicenseWithBLOBs.getYlzd5());
        paslicenseWithBLOBs.setYlzd6(paslicenseWithBLOBs.getYlzd6());
        paslicenseWithBLOBs.setGxsj(new Date());
        this.paslicenseMapper.update_txz(paslicenseWithBLOBs);

    }
}

