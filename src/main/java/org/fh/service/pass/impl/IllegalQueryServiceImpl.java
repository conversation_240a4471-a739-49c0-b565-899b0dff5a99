package org.fh.service.pass.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.fh.entity.Page;
import org.fh.entity.PageData;
import org.fh.mapper.dsno2.pass.IllegalQueryMapper;
import org.fh.service.pass.IllegalQueryService;

/** 
 * 说明： 违法查询接口提供模块接口实现类
 * 作者：FH Admin Q313596790
 * 时间：2020-06-17
 * 官网：www.fhadmin.org
 * @version
 */
@Service
@Transactional //开启事物
public class IllegalQueryServiceImpl implements IllegalQueryService{

	@Autowired
	private IllegalQueryMapper illegalqueryMapper;
	
	/**新增
	 * @param pd
	 * @throws Exception
	 */
	public void save(PageData pd)throws Exception{
		illegalqueryMapper.save(pd);
	}
	
	/**删除
	 * @param pd
	 * @throws Exception
	 */
	public void delete(PageData pd)throws Exception{
		illegalqueryMapper.delete(pd);
	}
	
	/**修改
	 * @param pd
	 * @throws Exception
	 */
	public void edit(PageData pd)throws Exception{
		illegalqueryMapper.edit(pd);
	}
	
	/**列表
	 * @param page
	 * @throws Exception
	 */
	public List<PageData> list(Page page)throws Exception{
		return illegalqueryMapper.datalistPage(page);
	}
	
	/**列表(全部)
	 * @param pd
	 * @throws Exception
	 */
	public List<PageData> listAll(PageData pd)throws Exception{
		return illegalqueryMapper.listAll(pd);
	}
	
	/**通过车牌号码和车辆类型获取数据
	 * @param pd
	 * @throws Exception
	 */
	public PageData findByHPHMAndHPZL(PageData pd)throws Exception{
		return illegalqueryMapper.findByHPHMAndHPZL(pd);
	}
	
	/**批量删除
	 * @param ArrayDATA_IDS
	 * @throws Exception
	 */
	public void deleteAll(String[] ArrayDATA_IDS)throws Exception{
		illegalqueryMapper.deleteAll(ArrayDATA_IDS);
	}
	
}

