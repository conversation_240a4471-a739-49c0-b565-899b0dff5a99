package org.fh.service.pass.impl;

import java.util.List;


import org.fh.mapper.dsno2.pass.HistoricalDataMapper;
import org.fh.service.pass.HistoricalDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.fh.entity.Page;
import org.fh.entity.PageData;


/** 
 * 说明： 区域通行证--历史数据检索接口实现类
 * 作者：FH Admin Q313596790
 * 时间：2020-05-27
 * 官网：www.fhadmin.org
 * @version
 */
@Service
@Transactional //开启事物
public class HistoricalDataServiceImpl implements HistoricalDataService {

	@Autowired
	private HistoricalDataMapper historicaldataMapper;
	
	/**新增
	 * @param pd
	 * @throws Exception
	 */
	public void save(PageData pd)throws Exception{
		historicaldataMapper.save(pd);
	}

	/**注销
	 * @param pd
	 * @throws Exception
	 */
	public void zx(PageData pd)throws Exception{
		historicaldataMapper.zx(pd);
	}
	
	/**修改
	 * @param pd
	 * @throws Exception
	 */
	public void edit(PageData pd)throws Exception{
		historicaldataMapper.edit(pd);
	}
	
	/**列表
	 * @param pd
	 * @throws Exception
	 */
	public List<PageData> list(Page pd)throws Exception{
		return historicaldataMapper.datalistPage(pd);
	}
	
	/**列表(全部)
	 * @param pd
	 * @throws Exception
	 */
	public List<PageData> listAll(PageData pd)throws Exception{
		return historicaldataMapper.listAll(pd);
	}

	/**
	 * 获取总记录数
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public int getCount(PageData pd) throws Exception {
		return historicaldataMapper.getCount(pd);
	}

	/**通过id获取数据
	 * @param pd
	 * @throws Exception
	 */
	public PageData findById(PageData pd)throws Exception{
		return historicaldataMapper.findById(pd);
	}
	
	/**批量删除
	 * @param ArrayDATA_IDS
	 * @throws Exception
	 */
	public void deleteAll(String[] ArrayDATA_IDS)throws Exception{
		historicaldataMapper.deleteAll(ArrayDATA_IDS);
	}

	@Override
	public void zx_All(PageData pd) throws Exception {
		historicaldataMapper.zx_All(pd);
	}


}

