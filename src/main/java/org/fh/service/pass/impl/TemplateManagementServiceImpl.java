package org.fh.service.pass.impl;

import org.fh.entity.Page;
import org.fh.entity.PageData;
import org.fh.entity.pass.RoadPart;
import org.fh.mapper.dsno2.pass.TemplateManagementMapper;
import org.fh.service.pass.TemplateManagementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 说明： 区域通行证--模板管理接口实现类
 * 作者：FH Admin Q313596790
 * 时间：2020-05-27
 * 官网：www.fhadmin.org
 * @version
 */
@Service
@Transactional //开启事物
public class TemplateManagementServiceImpl implements TemplateManagementService {

	@Autowired
	private TemplateManagementMapper templatemanagementMapper;

	/**新增
	 * @param pd
	 * @throws Exception
	 */
	public void save(PageData pd)throws Exception{
		templatemanagementMapper.save(pd);
	}

	/**删除
	 * @param pd
	 * @throws Exception
	 */
	public void delete(PageData pd)throws Exception{
		templatemanagementMapper.delete(pd);
	}

	/**修改
	 * @param pd
	 * @throws Exception
	 */
	public void edit(PageData pd)throws Exception{
		templatemanagementMapper.edit(pd);
	}

	/**列表
	 * @param page
	 * @throws Exception
	 */
	public List<PageData> list(Page page)throws Exception{
		return templatemanagementMapper.datalistPage(page);
	}

	@Override
	public List<PageData> listRoad(Page page) throws Exception {
		return templatemanagementMapper.listPagelistRoad(page);
	}

	/**列表(全部)
	 * @param pd
	 * @throws Exception
	 */
	public List<PageData> listAll(PageData pd)throws Exception{
		return templatemanagementMapper.listAll(pd);
	}

	public List<RoadPart> listById(PageData pd)throws Exception{
		return templatemanagementMapper.listById(pd);
	}

	/**
	 * 获取总记录数
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public int getCount(PageData pd) throws Exception {
		return templatemanagementMapper.getCount(pd);
	}

	@Override
	public int getRoadCount(PageData pd) throws Exception {
		return templatemanagementMapper.getRoadCount(pd);
	}

	/**通过id获取数据
	 * @param pd
	 * @throws Exception
	 */
	public PageData findById(PageData pd)throws Exception{
		return templatemanagementMapper.findById(pd);
	}

	/**批量删除
	 * @param ArrayDATA_IDS
	 * @throws Exception
	 */
	public void deleteAll(String[] ArrayDATA_IDS)throws Exception{
		templatemanagementMapper.deleteAll(ArrayDATA_IDS);
	}

}

