package org.fh.service.pass.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.fh.entity.Page;
import org.fh.entity.PageData;
import org.fh.mapper.dsno2.pass.RoadMaintenanceMapper;
import org.fh.service.pass.RoadMaintenanceService;

/** 
 * 说明： 区域通行证--道路信息维护接口实现类
 * 作者：FH Admin Q313596790
 * 时间：2020-06-02
 * 官网：www.fhadmin.org
 * @version
 */
@Service
@Transactional //开启事物
public class RoadMaintenanceServiceImpl implements RoadMaintenanceService{

	@Autowired
	private RoadMaintenanceMapper roadmaintenanceMapper;
	
	/**新增
	 * @param pd
	 * @throws Exception
	 */
	public void save(PageData pd)throws Exception{
		roadmaintenanceMapper.save(pd);
	}
	
	/**删除
	 * @param pd
	 * @throws Exception
	 */
	public void delete(PageData pd)throws Exception{
		roadmaintenanceMapper.delete(pd);
	}
	
	/**修改
	 * @param pd
	 * @throws Exception
	 */
	public void edit(PageData pd)throws Exception{
		roadmaintenanceMapper.edit(pd);
	}
	
	/**列表
	 * @param page
	 * @throws Exception
	 */
	public List<PageData> list(Page page)throws Exception{
		return roadmaintenanceMapper.datalistPage(page);
	}
	
	/**列表(全部)
	 * @param pd
	 * @throws Exception
	 */
	public List<PageData> listAll(PageData pd)throws Exception{
		return roadmaintenanceMapper.listAll(pd);
	}

	/**获取总记录数
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public int getCount(PageData pd) throws Exception {
		return roadmaintenanceMapper.getCount(pd);
	}

	/**通过id获取数据
	 * @param pd
	 * @throws Exception
	 */
	public PageData findById(PageData pd)throws Exception{
		return roadmaintenanceMapper.findById(pd);
	}
	
	/**批量删除
	 * @param ArrayDATA_IDS
	 * @throws Exception
	 */
	public void deleteAll(String[] ArrayDATA_IDS)throws Exception{
		roadmaintenanceMapper.deleteAll(ArrayDATA_IDS);
	}
	
}

