package org.fh.service.pass.impl;

import org.fh.entity.Page;
import org.fh.entity.PageData;
import org.fh.mapper.dsno2.pass.BlacklistMapper;
import org.fh.service.pass.BlacklistService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 说明： 黑名单接口实现类
 * 作者：FH Admin Q313596790
 * 时间：2020-06-12
 * 官网：www.fhadmin.org
 */
@Service
@Transactional //开启事物
public class BlacklistServiceImpl implements BlacklistService {

    @Autowired
    private BlacklistMapper blacklistMapper;

    /**
     * 新增
     *
     * @param pd
     * @throws Exception
     */
    @Override
    public void save(PageData pd) throws Exception {
        blacklistMapper.save(pd);
    }

    /**
     * 删除
     *
     * @param pd
     * @throws Exception
     */
    @Override
    public void delete(PageData pd) throws Exception {
        blacklistMapper.delete(pd);
    }

    /**
     * 修改
     *
     * @param pd
     * @throws Exception
     */
    @Override
    public void edit(PageData pd) throws Exception {
        blacklistMapper.edit(pd);
    }

    /**
     * 列表
     *
     * @param page
     * @throws Exception
     */
    @Override
    public List<PageData> list(Page page) throws Exception {
        return blacklistMapper.datalistPage(page);
    }

    /**
     * 列表(全部)
     *
     * @param pd
     * @throws Exception
     */
    @Override
    public List<PageData> listAll(PageData pd) throws Exception {
        return blacklistMapper.listAll(pd);
    }

    /**
     * 通过id获取数据
     *
     * @param pd
     * @throws Exception
     */
    @Override
    public PageData findById(PageData pd) throws Exception {
        return blacklistMapper.findById(pd);
    }

    /**
     * 批量删除
     *
     * @param ArrayDATA_IDS
     * @throws Exception
     */
    @Override
    public void deleteAll(String[] ArrayDATA_IDS) throws Exception {
        blacklistMapper.deleteAll(ArrayDATA_IDS);
    }

    @Override
    public String fandByHPId(String HPHM, String HPZL) {
        return blacklistMapper.fandByHPId(HPHM, HPZL);
    }


    @Override
    public String fandBySYR(String SYR) {
        return blacklistMapper.fandBySYR(SYR);
    }

    @Override
    public void savesyr(PageData pd) {
        blacklistMapper.savesyr(pd);
    }

    @Override
    public List<Map<String, Object>> highstate(String hphm, String hpzl, String lstxz) {
        return blacklistMapper.highstate(hphm, hpzl, lstxz);
    }

    @Override
    public List<Map<String, Object>> overdue(String hphm, String hpzl) {
        return this.blacklistMapper.overdue(hphm, hpzl);
    }
}

