package org.fh.service.pass.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.fh.entity.Page;
import org.fh.entity.PageData;
import org.fh.mapper.dsno2.pass.ApprovalManagementMapper;
import org.fh.service.pass.ApprovalManagementService;

/** 
 * 说明： 区域通行证-审批管理接口实现类
 * 作者：FH Admin Q313596790
 * 时间：2020-05-27
 * 官网：www.fhadmin.org
 * @version
 */
@Service
@Transactional //开启事物
public class ApprovalManagementServiceImpl implements ApprovalManagementService{

	@Autowired
	private ApprovalManagementMapper approvalmanagementMapper;
	
	/**新增
	 * @param pd
	 * @throws Exception
	 */
	public void save(PageData pd)throws Exception{
		approvalmanagementMapper.save(pd);
	}
	
	/**删除
	 * @param pd
	 * @throws Exception
	 */
	public void delete(PageData pd)throws Exception{
		approvalmanagementMapper.delete(pd);
	}
	
	/**修改
	 * @param pd
	 * @throws Exception
	 */
	public void edit(PageData pd)throws Exception{
		approvalmanagementMapper.edit(pd);
	}
	
	/**列表
	 * @param page
	 * @throws Exception
	 */
	public List<PageData> list(Page page)throws Exception{
		return approvalmanagementMapper.datalistPage(page);
	}
	
	/**列表(全部)
	 * @param pd
	 * @throws Exception
	 */
	public List<PageData> listAll(PageData pd)throws Exception{
		return approvalmanagementMapper.listAll(pd);
	}

	/**
	 * 获取总记录数
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public int getCount(PageData pd) throws Exception {
		return approvalmanagementMapper.getCount(pd);
	}

	/**通过id获取数据
	 * @param pd
	 * @throws Exception
	 */
	public PageData findById(PageData pd)throws Exception{
		return approvalmanagementMapper.findById(pd);
	}
	
	/**批量删除
	 * @param ArrayDATA_IDS
	 * @throws Exception
	 */
	public void deleteAll(String[] ArrayDATA_IDS)throws Exception{
		approvalmanagementMapper.deleteAll(ArrayDATA_IDS);
	}
	
}

