package org.fh.service.pass.impl;


import org.fh.entity.Page;
import org.fh.entity.PageData;
import org.fh.mapper.dsno2.pass.RoadInfoMapper;
import org.fh.mapper.dsno2.pass.TxzMapper;
import org.fh.service.pass.TxzService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 说明： 通行证接口实现类
 * 作者：FH Admin Q313596790
 * 时间：2020-05-26
 * 官网：www.fhadmin.org
 */
@Service
@Transactional //开启事物
public class TxzServiceImpl implements TxzService {

    @Autowired
    private TxzMapper txzMapper;

    @Autowired
    private RoadInfoMapper roadInfoMapper;

    /**
     * 新增
     * @param pd
     * @throws Exception
     */
    @Override
    public void save(PageData pd) throws Exception {
        txzMapper.save(pd);
    }

    /**
     * 删除
     * @param pd
     * @throws Exception
     */
    @Override
    public void delete(PageData pd) throws Exception {
        txzMapper.delete(pd);
    }

    /**
     * 修改
     * @param pd
     * @throws Exception
     */
    @Override
    public void edit(PageData pd) throws Exception {
        txzMapper.edit(pd);
    }

    @Override
    public void update(PageData pd) throws Exception {
        txzMapper.update(pd);
    }

    /**
     * 列表
     * @param page
     * @throws Exception
     */
    @Override
    public List<PageData> list(Page page) throws Exception {
        return txzMapper.datalistPage(page);
    }

    @Override
    public List<PageData> selectList(Page page) throws Exception {
        return txzMapper.listPageselectList(page);
    }

    @Override
    public List<PageData> selectByList(Page page) throws Exception {
        return txzMapper.listPageselectByList(page);
    }

    /**
     * 列表(全部)
     * @param pd
     * @throws Exception
     */
    @Override
    public List<PageData> listAll(PageData pd) throws Exception {
        return txzMapper.listAll(pd);
    }

    /**
     * 通过id获取数据
     * @param pd
     * @throws Exception
     */
    @Override
    public PageData findById(PageData pd) throws Exception {
        return txzMapper.findById(pd);
    }

    /**
     * 批量删除
     * @param ArrayDATA_IDS
     * @throws Exception
     */
    public void deleteAll(String[] ArrayDATA_IDS) throws Exception {
        txzMapper.deleteAll(ArrayDATA_IDS);
    }

    /**
     * 批量审批
     * @param ArrayDATA_IDS
     * @throws Exception
     */
    public void updateAll(PageData pd) throws Exception {
        txzMapper.updateAll(pd);
    }

    @Override
    public void updateById(PageData pd) throws Exception {
        txzMapper.updateById(pd);
    }

    @Override
    public List<Map<String, Object>> fandByHPZL() {
        List<Map<String, Object>> map = txzMapper.fandByHPZL();
        return map;
    }

    @Override
    //@Cacheable(cacheNames = {"road"})
    public List<String> GetRoad(String ysqs, String yxqz, String jbr) {
        return txzMapper.GetRoad(ysqs, yxqz, jbr);
    }

    @Override
    public Map<String, Object> GetSjtj(String ysqs, String yxqz, String jbr) {

        Map<String, Object> resMap = new HashMap<>();
        //审批数据
        List<Map<String, Object>> spList = txzMapper.GetSPZT(ysqs, yxqz, jbr);
        //通行证数据统计
        List<Map<String, Object>> txzList = txzMapper.GetTXZ(ysqs, yxqz, jbr);
        //号牌种类统计
        List<Map<String, Object>> cllxList = txzMapper.GetCLLX(ysqs, yxqz, jbr);

        resMap.put("spList", spList);
        resMap.put("txzList", txzList);
        resMap.put("cllxList", cllxList);
        return resMap;
    }

    @Override
    public List<PageData> rowlistPage(Page page) throws Exception {
        return roadInfoMapper.rowlistPage(page);
    }

    @Override
    public List datalist(Page page) {
        return this.txzMapper.datalist(page);
    }

    @Override
    public List fandBySPZT() {
        List mapList = this.txzMapper.fandBySPZT();
        return mapList;
    }
}

