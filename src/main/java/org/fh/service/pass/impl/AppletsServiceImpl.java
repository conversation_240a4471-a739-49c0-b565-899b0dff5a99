package org.fh.service.pass.impl;

import org.fh.entity.Page;
import org.fh.entity.PageData;
import org.fh.entity.pass.PASLICENSEWithBLOBs;
import org.fh.entity.pass.RoadVo;
import org.fh.mapper.dsno2.pass.*;
import org.fh.service.pass.AppletsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 小程序接口
 */
@Service
public class AppletsServiceImpl implements AppletsService {

    @Autowired
    RoadMapper roadMapper;
    @Autowired
    AccountManagementMapper accountManagementMapper;
    @Autowired
    VehicleApprovalMapper vehicleApprovalMapper;
    @Autowired
    PASLICENSEMapper paslicenseMapper;
    @Autowired
    TxzMapper txzMapper;
    @Autowired
    PaslicenseRoadpartMapper paslicenseRoadpartMapper;



    /**
     * 道路列表查询
     * @param pd
     * @throws Exception
     * @return
     */
    @Override
    public List<PageData> datalistPage(Page page)throws Exception{
        return roadMapper.datalistPage(page);
    }

    @Override
    public PageData findPass(PageData pd) throws Exception {
        return accountManagementMapper.findPass(pd);
    }

    @Override
    public List<PageData> findByAHE(Page page) throws Exception {
        return vehicleApprovalMapper.findByAHE(page);
    }
    @Override
    public List<PageData> findByAHEList(Page page) throws Exception {
        return vehicleApprovalMapper.findByAHElistPage(page);
    }
    @Override
    public List<PageData> findByEN(Page page) {
        return vehicleApprovalMapper.findByEN(page);
    }


    @Override
    public List<RoadVo> datalistPageByDJ(Map page) {
        return roadMapper.listDJ(page);
    }

    @Override
    public List<Map<String,Object>> fandByMQ() {
        return accountManagementMapper.fandByMQ();

    }

    @Override
    public List<PASLICENSEWithBLOBs> selectByIdHphm(PASLICENSEWithBLOBs paslicenseWithBLOBs) {
        paslicenseWithBLOBs.getYlzd5();
        paslicenseWithBLOBs.getYlzd6();
        paslicenseWithBLOBs.getYssbsd();
        return paslicenseMapper.selectByIdHphm(paslicenseWithBLOBs);
    }

    @Override
    public String selectIdCard(String VEHNUM, String VEHTYPE, String MEMBERIDCARD) throws Exception {
        return vehicleApprovalMapper.selectIdCard(VEHNUM, VEHTYPE, MEMBERIDCARD);
    }

    @Override
    public String selectCard(String VEHNUM, String VEHTYPE, String ENTERPRISEID) throws Exception {
        return this.vehicleApprovalMapper.selectCard(VEHNUM, VEHTYPE, ENTERPRISEID);
    }
    @Override
    public String selectByIdCard(String VEHNUM, String VEHTYPE) throws Exception {
        return this.vehicleApprovalMapper.selectByIdCard(VEHNUM, VEHTYPE);
    }
    @Override
    public String GetByIdCard(String VEHNUM, String VEHTYPE) throws Exception {
        return this.vehicleApprovalMapper.GetByIdCard(VEHNUM, VEHTYPE);
    }
    @Override
    public String GetIdCard(String VEHNUM, String VEHTYPE) throws Exception {
        return this.vehicleApprovalMapper.GetIdCard(VEHNUM, VEHTYPE);
    }
    @Override
    public List selectListIdCar(PASLICENSEWithBLOBs paslicenseWithBLOBs) {
        paslicenseWithBLOBs.getYlzd6();
        return this.paslicenseMapper.selectListIdCar(paslicenseWithBLOBs);
    }
    @Override
    public List selectTest(PASLICENSEWithBLOBs paslicenseWithBLOBs) {
        paslicenseWithBLOBs.getYlzd5();
        paslicenseWithBLOBs.getYlzd6();
        return this.paslicenseMapper.selectTest(paslicenseWithBLOBs);
    }
}
