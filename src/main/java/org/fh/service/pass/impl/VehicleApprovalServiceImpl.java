package org.fh.service.pass.impl;

import java.util.List;

import org.fh.entity.pass.PaslicenseEnterpris;
import org.fh.entity.pass.PaslicenseEnterprisVeh;
import org.fh.entity.pass.VehicleImageBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.fh.entity.Page;
import org.fh.entity.PageData;
import org.fh.mapper.dsno2.pass.VehicleApprovalMapper;
import org.fh.service.pass.VehicleApprovalService;

/**
 * 说明： 区域通行证--面签车辆审批接口实现类
 * 作者：FH Admin Q313596790
 * 时间：2020-05-27
 * 官网：www.fhadmin.org
 * @version
 */
@Service
@Transactional //开启事物
public class VehicleApprovalServiceImpl implements VehicleApprovalService{

	@Autowired
	private VehicleApprovalMapper vehicleapprovalMapper;

	/**新增
	 * @param pd
	 * @throws Exception
	 */
	public void save(PageData pd)throws Exception{
		vehicleapprovalMapper.save(pd);
	}


	public void saveInfo(PaslicenseEnterprisVeh paslicenseEnterprisVeh) throws Exception {
		vehicleapprovalMapper.saveInfo(paslicenseEnterprisVeh);

	}

	/**删除
	 * @param pd
	 * @throws Exception
	 */
	public void delete(PageData pd)throws Exception{
		vehicleapprovalMapper.delete(pd);
	}

	/**修改
	 * @param paslicenseEnterprisVeh
	 * @throws Exception
	 */
	public void edit(PaslicenseEnterprisVeh paslicenseEnterprisVeh)throws Exception{
		vehicleapprovalMapper.edit(paslicenseEnterprisVeh);
	}
	@Override
	public PaslicenseEnterprisVeh GetInfoById(String id) {
		return vehicleapprovalMapper.GetInfoById(id);
	}
	/**列表
	 * @param page
	 * @throws Exception
	 */
	public List<PageData> list(Page page)throws Exception{
		return vehicleapprovalMapper.datalistPage(page);
	}

	/**列表(全部)
	 * @param pd
	 * @throws Exception
	 */
	public List<PageData> listAll(PageData pd)throws Exception{
		return vehicleapprovalMapper.listAll(pd);
	}

	/**获取总记录数
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public int getCount(PageData pd) throws Exception {
		return vehicleapprovalMapper.getCount(pd);
	}

	/**通过id获取数据
	 * @param pd
	 * @throws Exception
	 */
	public PageData findById(PageData pd)throws Exception{
		return vehicleapprovalMapper.findById(pd);
	}

	@Override
	public PaslicenseEnterprisVeh findByID(String id) {
		return vehicleapprovalMapper.findByID(id);
	}

	/**通过id获取图片
	 * @param pd
	 * @throws Exception
	 */
	public VehicleImageBean getImageById(PageData pd)throws Exception{
		return vehicleapprovalMapper.getImageById(pd);
	}

	/**批量删除
	 * @param ArrayDATA_IDS
	 * @throws Exception
	 */
	public void deleteAll(String[] ArrayDATA_IDS)throws Exception{
		vehicleapprovalMapper.deleteAll(ArrayDATA_IDS);
	}
	@Override
	public PaslicenseEnterprisVeh findByInfo(PaslicenseEnterprisVeh paslicenseEnterprisVeh) {
		return vehicleapprovalMapper.findByInfo(paslicenseEnterprisVeh);
	}




}

