package org.fh.service.pass.impl;

import org.fh.entity.Page;
import org.fh.entity.PageData;
import org.fh.mapper.dsno2.pass.PaslicenseMouldInfoMapper;
import org.fh.service.pass.PaslicenseMouldInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/** 
 * 说明： 模板和路段信息关联模块--模板明细接口实现类
 * 作者：FH Admin Q313596790
 * 时间：2020-06-13
 * 官网：www.fhadmin.org
 * @version
 */
@Service
@Transactional //开启事物
public class PaslicenseMouldInfoServiceImpl implements PaslicenseMouldInfoService{

	@Autowired
	private PaslicenseMouldInfoMapper paslicensemouldinfoMapper;
	
	/**新增
	 * @param pd
	 * @throws Exception
	 */
	public void save(PageData pd)throws Exception{
		paslicensemouldinfoMapper.save(pd);
	}

	/**批量新增
	 * @param varlist
	 * @throws Exception
	 */
	public void addBatch(List<PageData> pd)throws Exception{
		paslicensemouldinfoMapper.addBatch(pd);
	};
	
	/**删除
	 * @param pd
	 * @throws Exception
	 */
	public void delete(PageData pd)throws Exception{
		paslicensemouldinfoMapper.delete(pd);
	}
	
	/**修改
	 * @param pd
	 * @throws Exception
	 */
	public void edit(PageData pd)throws Exception{
		paslicensemouldinfoMapper.edit(pd);
	}

	/**
	 * 通过道路ID获取数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	@Override
	public List<String> getById(String ROADPARTID,String MBID) throws Exception {
		return paslicensemouldinfoMapper.getById(ROADPARTID,MBID);
	}

	/**列表
	 * @param page
	 * @throws Exception
	 */
	public List<PageData> list(Page page)throws Exception{
		return paslicensemouldinfoMapper.datalistPage(page);
	}
	
	/**列表(全部)
	 * @param pd
	 * @throws Exception
	 */
	public List<PageData> listAll(PageData pd)throws Exception{
		return paslicensemouldinfoMapper.listAll(pd);
	}

	/**通过模板id获取路段信息列表
	 * @param MBID
	 * @throws Exception
	 */
	public List<String> findByMBId(String MBID)throws Exception{
		return paslicensemouldinfoMapper.findByMBID(MBID);
	}

	
	/**批量删除
	 * @param ArrayDATA_IDS
	 * @throws Exception
	 */
	public void deleteAll(String[] ArrayDATA_IDS)throws Exception{
		paslicensemouldinfoMapper.deleteAll(ArrayDATA_IDS);
	}
	
}

