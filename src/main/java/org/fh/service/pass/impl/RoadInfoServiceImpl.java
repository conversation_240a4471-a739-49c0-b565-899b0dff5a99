package org.fh.service.pass.impl;

import org.fh.entity.Page;
import org.fh.entity.PageData;
import org.fh.entity.pass.Road;
import org.fh.mapper.dsno2.pass.RoadInfoMapper;
import org.fh.service.pass.RoadInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/** 
 * 说明： 区域通行证--道路信息列表接口实现类
 * 作者：FH Admin Q313596790
 * 时间：2020-05-27
 * 官网：www.fhadmin.org
 * @version
 */
@Service
@Transactional //开启事物
public class RoadInfoServiceImpl implements RoadInfoService {

	@Autowired
	private RoadInfoMapper roadInfoMapper;
	
	/**新增
	 * @param pd
	 * @throws Exception
	 */
	public void save(PageData pd)throws Exception{
		roadInfoMapper.save(pd);
	}
	
	/**删除
	 * @param pd
	 * @throws Exception
	 */
	public void delete(PageData pd)throws Exception{
		roadInfoMapper.delete(pd);
	}
	
	/**修改
	 * @param pd
	 * @throws Exception
	 */
	public void edit(PageData pd)throws Exception{
		roadInfoMapper.edit(pd);
	}
	
	/**列表
	 * @param page
	 * @throws Exception
	 */
	public List<PageData> list(Page page)throws Exception{
		return roadInfoMapper.datalistPage(page);
	}

	/**根据路段ID列表获取路段信息列表
	 * @param page
	 * @throws Exception
	 */
	public List<PageData> datalistPageByRoadIDList(Page page)throws Exception{
		return roadInfoMapper.datalistPageByRoadIDList(page);
	}
	
	/**列表(全部)
	 * @param pd
	 * @throws Exception
	 */
	public List<PageData> listAll(PageData pd)throws Exception{
		return roadInfoMapper.listAll(pd);
	}



	/**
	 * 获取总记录数
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public int getCount(PageData pd) throws Exception {
		return roadInfoMapper.getCount(pd);
	}

	/**通过id获取数据
	 * @param pd
	 * @throws Exception
	 */
	public PageData findById(PageData pd)throws Exception{
		return roadInfoMapper.findById(pd);
	}
	
	/**批量删除
	 * @param ArrayDATA_IDS
	 * @throws Exception
	 */
	public void deleteAll(String[] ArrayDATA_IDS)throws Exception{
		roadInfoMapper.deleteAll(ArrayDATA_IDS);
	}

	@Override
	public List<Road> fndByRoad(String name) {
		return roadInfoMapper.fndByRoad(name);
	}

	/**删除
	 * @param pd
	 * @throws Exception
	 */
	@Override
	public void Delete(PageData pd)throws Exception{
		roadInfoMapper.Delete(pd);
	}

	/**批量删除
	 * @param ArrayDATA_IDS
	 * @throws Exception
	 */
	@Override
	public void DeleteAll(String[] ArrayDATA_IDS){
		roadInfoMapper.DeleteAll(ArrayDATA_IDS);
	}


//	@Override
//	public List<Map<String,Object>> fndByRoad(String name) {
//		return roadInfoMapper.fndByRoad(name);
//	}




}

