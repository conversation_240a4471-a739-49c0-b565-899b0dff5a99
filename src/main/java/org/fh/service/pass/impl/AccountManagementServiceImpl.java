package org.fh.service.pass.impl;

import org.fh.entity.Page;
import org.fh.entity.PageData;
import org.fh.entity.pass.AccountImageBean;
import org.fh.entity.pass.PaslicenseEnterpris;
import org.fh.mapper.dsno2.pass.AccountManagementMapper;
import org.fh.service.pass.AccountManagementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;


/** 
 * 说明： 区域通行证--面签账户管理接口实现类
 * 作者：FH Admin Q313596790
 * 时间：2020-05-27
 * 官网：www.fhadmin.org
 * @version
 */
@Service
@Transactional //开启事物
public class AccountManagementServiceImpl implements AccountManagementService {

	@Autowired
	private AccountManagementMapper accountmanagementMapper;
	
	/**新增
	 * @param pd
	 * @throws Exception
	 */
	public void save(PageData pd)throws Exception{
		accountmanagementMapper.save(pd);
	}
	
	/**删除
	 * @param pd
	 * @throws Exception
	 */
	public void delete(PageData pd)throws Exception{
		accountmanagementMapper.delete(pd);
	}
	
	/**修改
	 * @param pd
	 * @throws Exception
	 */
	public void edit(PageData pd)throws Exception{
		accountmanagementMapper.edit(pd);
	}

	@Override
	public void editPwd(PageData pd) throws Exception {
		accountmanagementMapper.editPwd(pd);
	}

	/**列表
	 * @param page
	 * @throws Exception
	 */
	public List<PageData> list(Page page)throws Exception{
		return accountmanagementMapper.datalistPage(page);
	}
	public List<PageData> selectCar(Page page)throws Exception{
		return accountmanagementMapper.listPageselectCar(page);
	}
	public List<PageData> selectAll(Page page)throws Exception{
		return accountmanagementMapper.selectAll(page);
	}

	@Override
	public String selectId(String ID) {
		return accountmanagementMapper.selectId(ID);
	}

	/**列表(全部)
	 * @param pd
	 * @throws Exception
	 */
	public List<PageData> listAll(PageData pd)throws Exception{
		return accountmanagementMapper.listAll(pd);
	}

	/**
	 * 获取分页查询总记录数
	 * @param pd
	 * @return
	 */
	public int getCount(PageData pd)throws Exception{
		return accountmanagementMapper.getCount(pd);
	}
	public int getRoadCount(PageData pd)throws Exception{
		return accountmanagementMapper.getRoadCount(pd);
	}
	public int carCount(PageData pd)throws Exception{
		return accountmanagementMapper.getCount(pd);
	}

	/**通过id获取数据
	 * @param pd
	 * @throws Exception
	 */
	public PageData findById(PageData pd)throws Exception{
		return accountmanagementMapper.findById(pd);
	}

	public PaslicenseEnterpris getInfoByAccountID(String id, String account) {
		return accountmanagementMapper.getInfoByAccountID(id, account);
	}

	public PageData findPass(PageData pd)throws Exception{
		return accountmanagementMapper.findById(pd);
	}

	/**通过id获取数据
	 * @param pd
	 * @throws Exception
	 */
	public AccountImageBean getImageById(PageData pd)throws Exception{
		return accountmanagementMapper.getImageById(pd);
	}
	
	/**批量删除
	 * @param ArrayDATA_IDS
	 * @throws Exception
	 */
	public void deleteAll(String[] ArrayDATA_IDS)throws Exception{
		accountmanagementMapper.deleteAll(ArrayDATA_IDS);
	}

	@Override
	public PaslicenseEnterpris getInfoByAccount(String account) {
		return accountmanagementMapper.getInfoByAccount(account);
	}

	@Override
	public int updatePaslicenseEnterpris(PaslicenseEnterpris paslicenseEnterpris) {
		return accountmanagementMapper.updatePaslicenseEnterpris(paslicenseEnterpris);
	}
	@Override
	public PaslicenseEnterpris findByStringId(String ID) {
		return accountmanagementMapper.findByStringId(ID);
	}

	@Override
	public List<Map<String, Object>> fandByMQ() {
		return accountmanagementMapper.fandByMQ();
	}

}

