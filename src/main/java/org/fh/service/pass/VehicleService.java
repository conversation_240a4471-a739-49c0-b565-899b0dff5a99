package org.fh.service.pass;

import org.fh.entity.pass.ForceVo;
import org.fh.entity.pass.SurveilVo;
import org.fh.entity.pass.VehicleVo;
import org.fh.entity.pass.ViolationVo;

import java.util.List;
/**
 * <AUTHOR>
 * @date 2020/7/28
 */
public interface VehicleService {
    /**
     * 获取代码属性
     *
     * @return ZT
     */
    VehicleVo getZT(VehicleVo vehicleVo);
    /**
     * 获取VIO_VIOLATION表违法信息
     *
     * @return ZT
     */
    List<ViolationVo> getWF(ViolationVo violationVo);
    /**
     * 获取VIO_SURVEIL表违法信息
     *
     * @return ZT
     */
    List<SurveilVo> GetSurveilWf(SurveilVo surveilVo);


    List<ForceVo> GetForceWf(ForceVo forceVo);

    VehicleVo getVehicleInfo(VehicleVo vehicleVo);


}
