package org.fh.service.pass;

import org.fh.entity.Page;
import org.fh.entity.PageData;

import java.util.List;
import java.util.Map;

/**
 * 说明： 黑名单接口
 * 作者：FH Admin QQ313596790
 * 时间：2020-06-12
 * 官网：www.fhadmin.org
 * @version
 */
public interface BlacklistService{

	/**新增
	 * @param pd
	 * @throws Exception
	 */
	public  void save(PageData pd)throws Exception;

	/**删除
	 * @param pd
	 * @throws Exception
	 */
	public  void delete(PageData pd)throws Exception;

	/**修改
	 * @param pd
	 * @throws Exception
	 */
	public  void edit(PageData pd)throws Exception;

	/**列表
	 * @param page
	 * @throws Exception
	 */
	public List<PageData> list(Page page)throws Exception;

	/**列表(全部)
	 * @param pd
	 * @throws Exception
	 */
	public  List<PageData> listAll(PageData pd)throws Exception;

	/**通过id获取数据
	 * @param pd
	 * @throws Exception
	 */
	public  PageData findById(PageData pd)throws Exception;

	/**批量删除
	 * @param ArrayDATA_IDS
	 * @throws Exception
	 */
	public  void deleteAll(String[] ArrayDATA_IDS)throws Exception;

	public  String fandByHPId(String HPHM, String HPZL);



	public  String fandBySYR(String SYR);

	public  void savesyr(PageData pd);

	List<Map<String, Object>> highstate(String hphm, String hpzl, String lstxz);

	List<Map<String, Object>> overdue(String hphm, String hpzl);
}

