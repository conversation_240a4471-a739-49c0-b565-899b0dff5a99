package org.fh.service.edu.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.fh.entity.Page;
import org.fh.entity.PageData;
import org.fh.mapper.dsno2.edu.Edu_Full_ControllMapper;
import org.fh.service.edu.Edu_Full_ControllService;

/** 
 * 说明：   读取现场/ 体验教育人员信息接口接口实现类
 * 作者：FH Admin Q313596790
 * 时间：2020-05-14
 * 官网：www.fhadmin.org
 * @version
 */
@Service
@Transactional //开启事物
public class Edu_Full_ControllServiceImpl implements Edu_Full_ControllService{

	@Autowired
	private Edu_Full_ControllMapper edu_full_controllMapper;
	
	/**新增
	 * @param pd
	 * @throws Exception
	 */
	public void save(PageData pd)throws Exception{
		edu_full_controllMapper.save(pd);
	}
	
	/**删除
	 * @param pd
	 * @throws Exception
	 */
	public void delete(PageData pd)throws Exception{
		edu_full_controllMapper.delete(pd);
	}
	
	/**修改
	 * @param pd
	 * @throws Exception
	 */
	public void edit(PageData pd)throws Exception{
		edu_full_controllMapper.edit(pd);
	}
	
	/**列表
	 * @param page
	 * @throws Exception
	 */
	public List<PageData> list(Page page)throws Exception{
		return edu_full_controllMapper.datalistPage(page);
	}
	
	/**列表(全部)
	 * @param pd
	 * @throws Exception
	 */
	public List<PageData> listAll(PageData pd)throws Exception{
		return edu_full_controllMapper.listAll(pd);
	}
	
	/**通过id获取数据
	 * @param pd
	 * @throws Exception
	 */
	public PageData findById(PageData pd)throws Exception{
		return edu_full_controllMapper.findById(pd);
	}
	
	/**批量删除
	 * @param ArrayDATA_IDS
	 * @throws Exception
	 */
	public void deleteAll(String[] ArrayDATA_IDS)throws Exception{
		edu_full_controllMapper.deleteAll(ArrayDATA_IDS);
	}
	
}

