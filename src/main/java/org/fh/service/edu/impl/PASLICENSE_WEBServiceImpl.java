package org.fh.service.edu.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.fh.entity.Page;
import org.fh.entity.PageData;
import org.fh.mapper.dsno2.edu.PASLICENSE_WEBMapper;
import org.fh.service.edu.PASLICENSE_WEBService;

/** 
 * 说明： PASLICENSE_WEB接口实现类
 * 作者：FH Admin Q313596790
 * 时间：2020-05-22
 * 官网：www.fhadmin.org
 * @version
 */
@Service
@Transactional //开启事物
public class PASLICENSE_WEBServiceImpl implements PASLICENSE_WEBService{

	@Autowired
	private PASLICENSE_WEBMapper paslicense_webMapper;
	
	/**新增
	 * @param pd
	 * @throws Exception
	 */
	public void save(PageData pd)throws Exception{
		paslicense_webMapper.save(pd);
	}
	
	/**删除
	 * @param pd
	 * @throws Exception
	 */
	public void delete(PageData pd)throws Exception{
		paslicense_webMapper.delete(pd);
	}
	
	/**修改
	 * @param pd
	 * @throws Exception
	 */
	public void edit(PageData pd)throws Exception{
		paslicense_webMapper.edit(pd);
	}
	
	/**列表
	 * @param page
	 * @throws Exception
	 */
	public List<PageData> list(Page page)throws Exception{
		return paslicense_webMapper.datalistPage(page);
	}
	
	/**列表(全部)
	 * @param pd
	 * @throws Exception
	 */
	public List<PageData> listAll(PageData pd)throws Exception{
		return paslicense_webMapper.listAll(pd);
	}
	
	/**通过id获取数据
	 * @param pd
	 * @throws Exception
	 */
	public PageData findById(PageData pd)throws Exception{
		return paslicense_webMapper.findById(pd);
	}
	
	/**批量删除
	 * @param ArrayDATA_IDS
	 * @throws Exception
	 */
	public void deleteAll(String[] ArrayDATA_IDS)throws Exception{
		paslicense_webMapper.deleteAll(ArrayDATA_IDS);
	}
	
}

